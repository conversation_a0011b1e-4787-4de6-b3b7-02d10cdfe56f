// plugins/withFFmpegAndroidGradle.js
const { withAppBuildGradle, withProjectBuildGradle } = require('@expo/config-plugins');
const fs = require('fs');
const path = require('path');

module.exports = function withFFmpegAndroidGradle(config) {
  // First, modify the root android/build.gradle
  config = withProjectBuildGradle(config, config => {
    let projectBuildGradle = config.modResults.contents;

    // Remove ffmpegKitPackage from ext block if it exists
    const ffmpegKitPackageRegex = /\s*ffmpegKitPackage\s*=\s*["']full-gpl["']\s*,?\s*/g;

    if (projectBuildGradle.match(ffmpegKitPackageRegex)) {
      projectBuildGradle = projectBuildGradle.replace(ffmpegKitPackageRegex, '');
      console.log('Removed ffmpegKitPackage = "full-gpl" from root build.gradle ext block');
    } else {
      console.log('ffmpegKitPackage not found in root build.gradle ext block');
    }

    // Add flatDir to allprojects repositories block
    const allprojectsRegex = /(allprojects\s*\{[\s\S]*?repositories\s*\{)/;
    const flatDirEntry = '\n        flatDir { dirs "$rootDir/libs" }';

    if (projectBuildGradle.match(allprojectsRegex)) {
      // Check if flatDir already exists
      const flatDirRegex = /flatDir\s*\{\s*dirs\s*['"]\$rootDir\/libs['"]\s*\}/;

      if (!projectBuildGradle.match(flatDirRegex)) {
        projectBuildGradle = projectBuildGradle.replace(allprojectsRegex, `$1${flatDirEntry}`);
        console.log('Added flatDir { dirs "$rootDir/libs" } to allprojects repositories block');
      } else {
        console.log('flatDir configuration already exists in allprojects repositories block');
      }
    } else {
      console.log('Could not find allprojects repositories block in build.gradle');
    }

    // Add Kotlin version resolution strategy directly to allprojects block
    const allprojectsOpeningRegex = /(allprojects\s*\{)/;
    const kotlinResolutionStrategy = `
    configurations.all {
        resolutionStrategy.eachDependency { details ->
            if (details.requested.group == 'org.jetbrains.kotlin') {
                details.useVersion "1.9.25"
                details.because("Force Kotlin version to 1.9.25 to fix Compose Compiler compatibility")
            }
        }
    }`;

    // Check if Kotlin resolution strategy already exists
    const kotlinStrategyRegex =
      /configurations\.all\s*\{[\s\S]*?resolutionStrategy\.eachDependency[\s\S]*?org\.jetbrains\.kotlin/;

    if (!projectBuildGradle.match(kotlinStrategyRegex)) {
      // Add right after allprojects opening brace
      if (projectBuildGradle.match(allprojectsOpeningRegex)) {
        projectBuildGradle = projectBuildGradle.replace(allprojectsOpeningRegex, `$1${kotlinResolutionStrategy}`);
        console.log('Added Kotlin version resolution strategy to allprojects block');
      } else {
        console.log('Could not find allprojects block to add Kotlin resolution strategy');
      }
    } else {
      console.log('Kotlin resolution strategy already exists in allprojects block');
    }

    config.modResults.contents = projectBuildGradle;
    return config;
  });

  // Then, modify the app/build.gradle
  return withAppBuildGradle(config, async config => {
    let buildGradle = config.modResults.contents;

    // Add import statement at the very top if not already present
    const importStatement = 'import java.net.URL';
    if (!buildGradle.includes(importStatement)) {
      buildGradle = importStatement + '\n' + buildGradle;
      console.log('Added import java.net.URL to build.gradle');
    } else {
      console.log('import java.net.URL already exists in build.gradle');
    }

    // Add repositories configuration to android block
    const repositoriesConfig = `    repositories {
        flatDir {
            dirs "$rootDir/libs"
        }
    }`;

    // Check if repositories config already exists to avoid duplicates
    if (buildGradle.includes('flatDir') && buildGradle.includes('dirs "$rootDir/libs"')) {
      console.log('FFmpeg repositories configuration already exists in build.gradle');
    } else {
      // Find the android block and add repositories after the opening brace
      const androidBlockMatch = buildGradle.match(/android\s*{/);

      if (androidBlockMatch) {
        const insertIndex = androidBlockMatch.index + androidBlockMatch[0].length;
        buildGradle = buildGradle.slice(0, insertIndex) + '\n' + repositoriesConfig + buildGradle.slice(insertIndex);
        console.log('Added repositories configuration to android block in build.gradle');
      } else {
        console.warn('Could not find android block in build.gradle');
      }
    }

    // Add dependencies
    const ffmpegDependencies = `    implementation(name: 'ffmpeg-kit-full-gpl', ext: 'aar')
    implementation 'com.arthenica:smart-exception-java:0.2.1'`;

    // Check if dependencies already exist to avoid duplicates
    if (buildGradle.includes("implementation(name: 'ffmpeg-kit-full-gpl', ext: 'aar')")) {
      console.log('FFmpeg dependencies already exist in build.gradle');
    } else {
      // Find the dependencies block and add our dependencies
      const dependenciesMatch = buildGradle.match(/dependencies\s*{/);

      if (dependenciesMatch) {
        const insertIndex = dependenciesMatch.index + dependenciesMatch[0].length;
        buildGradle = buildGradle.slice(0, insertIndex) + '\n' + ffmpegDependencies + buildGradle.slice(insertIndex);
        console.log('Added FFmpeg dependencies to build.gradle');
      } else {
        console.warn('Could not find dependencies block in build.gradle');
      }
    }

    // Add afterEvaluate block at the bottom of the file
    const afterEvaluateBlock = `
// Download AAR immediately when this script is evaluated
def aarUrl = 'https://github.com/NooruddinLakhani/ffmpeg-kit-full-gpl/releases/download/v1.0.0/ffmpeg-kit-full-gpl.aar'
def aarFile = file("\${rootDir}/libs/ffmpeg-kit-full-gpl.aar")
if (!aarFile.exists()) {
    println "📦 AAR not found, downloading now..."
    if (!aarFile.parentFile.exists()) {
        println "📁 Creating directory: \${aarFile.parentFile.absolutePath}"
        aarFile.parentFile.mkdirs()
    }
    
    try {
        println "⏬ Downloading AAR from \$aarUrl..."
        new URL(aarUrl).withInputStream { i ->
            aarFile.withOutputStream { it << i }
        }
        println "✅ AAR downloaded successfully"
    } catch (Exception e) {
        println "❌ Failed to download AAR: \${e.message}"
        // Don't fail the build, let the task-based approach handle it
    }
}
afterEvaluate {
    // Backup task-based approach
    tasks.register("downloadAar") {
        doLast {
            if (!aarFile.parentFile.exists()) {
                aarFile.parentFile.mkdirs()
            }
            if (!aarFile.exists()) {
                println "⏬ Downloading AAR from \$aarUrl..."
                new URL(aarUrl).withInputStream { i ->
                    aarFile.withOutputStream { it << i }
                }
                println "✅ AAR downloaded to \${aarFile.absolutePath}"
            }
        }
    }
    
    // Ensure it runs before any build
    project.gradle.projectsEvaluated {
        tasks.matching { task ->
            task.name.startsWith('assemble') || 
            task.name.startsWith('compile') ||
            task.name.startsWith('generate')
        }.configureEach {
            dependsOn downloadAar
        }
    }
}`;

    // Check if afterEvaluate block already exists to avoid duplicates
    if (buildGradle.includes('tasks.register("downloadAar")')) {
      console.log('afterEvaluate block already exists in build.gradle');
    } else {
      // Add at the end of the file
      buildGradle = buildGradle + afterEvaluateBlock;
      console.log('Added afterEvaluate block to build.gradle');
    }

    config.modResults.contents = buildGradle;
    return config;
  });
};
