import React from 'react';
import { copyToClipboard, delay, hideLoader, showError, showLoader, showSuccess, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon } from '@/components/ui';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { ArrowRight } from '@/components/ui/icons';
import colors from '@/theme/colors';
import { EXPO_PUBLIC_PUBLIC_URL } from '@env';
import { useNavigation } from '@react-navigation/native';
import { Copy, Link21, ReceiveSquare, Send2, TickCircle } from 'iconsax-react-native/src';
import { ScrollView, View } from 'react-native';
import SendInvoiceModal from '@/components/invoices/send-invoice-modal';
import ListItemCard from '@/components/ui/cards/list-item-card';
import SectionContainer from '@/components/ui/section-container';
import useModals from 'src/hooks/use-modals';
import { InvoiceInterface, toAppUrl } from 'catlog-shared';
import { useFileDownload } from 'src/hooks/use-file-download';

interface Props {
  invoice: InvoiceInterface;
}
const InvoiceCreatedSuccessfully: React.FC<Props> = ({ invoice }) => {
  const navigation = useNavigation();
  const { modals, toggleModal } = useModals(['sendInvoice']);
  const { downloadFile, isLoading } = useFileDownload();

  const handleDownloadInvoice = async () => {
    try {
      if (invoice?.invoice_id === null) return;

      // const subUrl = `orders/export?${paramsFromObject(reqData)}`;
      showLoader('Downloading Invoice', true);

      const subUrl = `invoices/pdf/${invoice.invoice_id}`;

      const fileName = `invoice-${invoice.invoice_id}.pdf`;
      const downloadResult = await downloadFile(subUrl, fileName);
      hideLoader();
      await delay(1000);
      showSuccess('Invoice downloaded successfully');
    } catch (error) {
      hideLoader();
      await delay(1000);
      showError(error);
    }
  };

  const extraActions = [
    {
      title: 'Copy Invoice Link',
      onPress: () =>
        copyToClipboard(
          toAppUrl(`invoices/${invoice?.invoice_id}`, true, EXPO_PUBLIC_PUBLIC_URL),
          'Copied to Clipboard',
        ),
      leftElement: (
        <CircledIcon className="bg-accentYellow-pastel p-8">
          <Link21 size={wp(15)} color={colors.accentYellow.main} />
        </CircledIcon>
      ),
      rightElement: (
        <CircledIcon className="bg-white p-8">
          <Copy size={wp(15)} color={colors.primary.main} />
        </CircledIcon>
      ),
    },
    {
      title: 'Send Invoice',
      leftElement: (
        <CircledIcon className="bg-accentRed-pastel p-8">
          <Send2 size={wp(15)} color={colors.accentRed.main} />
        </CircledIcon>
      ),
      rightElement: <RightElement />,
      onPress: () => toggleModal('sendInvoice'),
    },
    {
      title: 'Download Invoice as PDF',
      leftElement: (
        <CircledIcon className="bg-accentOrange-pastel p-8">
          <ReceiveSquare size={wp(15)} color={colors.accentOrange.main} />
        </CircledIcon>
      ),
      rightElement: <RightElement />,
      onPress: () => handleDownloadInvoice(),
    },
  ];

  return (
    <>
      <View className="flex-1 justify-center">
        <ScrollView className="flex-1">
          <View className={'items-center justify-center mt-30'}>
            <CircledIcon className="bg-accentGreen-pastel p-15">
              <CircledIcon className="bg-accentGreen-main p-25">
                <TickCircle variant={'Bold'} size={wp(50)} color={colors.white} />
              </CircledIcon>
            </CircledIcon>
            <BaseText fontSize={22} type={'heading'} classes="text-center mt-10 max-w-[325px]">
              Your invoice has{'\n'}been created
            </BaseText>
          </View>
          <SectionContainer className="mx-20 mt-30">
            {extraActions?.map((value, index) => (
              <ListItemCard
                key={value.title}
                showBorder={index !== extraActions.length - 1}
                title={value.title}
                description={undefined}
                titleProps={{ weight: 'medium', fontSize: 14 }}
                titleClasses="text-black-secondary"
                onPress={() => value?.onPress?.()}
                // disabled={isMultiSelect ? isActive(value.value)! : false}
                leftElement={value?.leftElement}
                rightElement={value?.rightElement}
              />
            ))}
          </SectionContainer>
        </ScrollView>
        <FixedBtnFooter
          buttons={[
            {
              text: 'See all Invoices',
              onPress: () => navigation.goBack(),
            },
          ]}
        />
      </View>
      <SendInvoiceModal
        activeInvoice={invoice}
        isVisible={modals.sendInvoice}
        closeModal={() => toggleModal('sendInvoice', false)}
      />
    </>
  );
};
export default InvoiceCreatedSuccessfully;

const RightElement = () => (
  <CircledIcon className="bg-white p-8">
    <ArrowRight size={wp(15)} currentColor={colors.primary.main} strokeWidth={wp(2)} />
  </CircledIcon>
);
