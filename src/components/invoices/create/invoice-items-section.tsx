import { wp } from '@/assets/utils/js';
import { WhiteCardBtn } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import Button, { ButtonSize } from '@/components/ui/buttons/button';
import { ChevronDown } from '@/components/ui/icons';
import Input from '@/components/ui/inputs/input';
import Accordion, { AccordionMethod } from '@/components/ui/others/accordion';
import AccordionAnchor from '@/components/ui/others/accordion/accordion-anchor';
import colors from '@/theme/colors';
import { GetItemsParams, ProductItemInterface, StrippedItem } from 'catlog-shared';
import { FormikProps } from 'formik';
import { Add } from 'iconsax-react-native/src';
import { View } from 'react-native';
import SelectSpecificProductsModal from 'src/components/products/storefront-products/select-specific-product-modal';
import { ApiData } from 'src/hooks/use-api';
import useModals from 'src/hooks/use-modals';
import { InvoiceFormParams } from 'src/screens/invoices/create-invoice';
import InvoiceItemCard from './invoice-item-card';

interface InvoiceItemsSectionProps {
  form: FormikProps<InvoiceFormParams>;
  getProductsRequest: ApiData<GetItemsParams, { data: { items: StrippedItem[] } }>;
  accordionRef?: React.RefObject<AccordionMethod>;
  onPressSave: VoidFunction;
  isComplete: boolean;
  hasErrors: boolean;
}

const InvoiceItemsSection = ({
  getProductsRequest,
  onPressSave,
  isComplete,
  hasErrors,
  accordionRef,
  form,
}: InvoiceItemsSectionProps) => {
  const { modals, toggleModal } = useModals(['selectProducts']);

  const handleDeleteItem = (index: number) => {
    const selectedItems = form.values.items;
    selectedItems.splice(index, 1);
    form.setFieldValue('items', selectedItems);
  };

  const selectProductItem = (id: string) => {
    const allProducts = getProductsRequest?.response?.data?.items;
    const selectedProducts = allProducts;
    return selectedProducts.find(item => item.id === id);
  };

  const handleSetProductSelection = (selectionIds: string[]) => {
    const selectedItems: InvoiceFormParams['items'] = [];

    for (let index = 0; index < selectionIds.length; index++) {
      const id = selectionIds[index];
      const selectedProduct = selectProductItem(id);

      if (selectedProduct) {
        const item: InvoiceFormParams['items'][0] = {
          id,
          name: selectedProduct.name,
          quantity: 1,
          price: selectedProduct.price!,
          image: selectedProduct.image,
        };
        selectedItems.push(item);
      }
    }
    form.setFieldValue('items', selectedItems);
  };

  const handleToggleQuantity = (index: number, action: 'decrease' | 'increase') => {
    const selectedItemsCopy = form.values.items;
    const selectedItem = form.values.items[index];
    if (selectedItem) {
      //handle increase quantity
      if (action === 'increase') {
        selectedItem.quantity = selectedItem.quantity + 1;
        form.setFieldValue('items', selectedItemsCopy);
        return;
      }

      //handle decrease quantity
      if (action === 'decrease') {
        if (selectedItem.quantity === 1) {
          handleDeleteItem(index);
        } else {
          selectedItem.quantity = selectedItem.quantity - 1;
          form.setFieldValue('items', selectedItemsCopy);
        }
        return;
      }
    }
  };


  return (
    <View>
      <Accordion
        anchorElement={status => (
          <AccordionAnchor title="Invoice Items" isSaved={isComplete} isError={hasErrors} isOpened={status} />
        )}
        ref={accordionRef}
        initiallyOpened>
        {form.values.items?.length < 1 && (
          <Pressable onPress={() => toggleModal('selectProducts')} className="mt-15">
            <Input
              editable={false}
              onPressIn={() => toggleModal('selectProducts')}
              label={'Select Items'}
              rightAccessory={
                <View className="p-3 my-12 bg-grey-bgOne rounded-full">
                  <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.grey.muted} />
                </View>
              }
              containerClasses={`py-0`}
            />
          </Pressable>
        )}
        <View className="mt-15">
          {form.values.items.map((item, index) => (
            <InvoiceItemCard
              key={item.id}
              item={item}
              onChangeItem={() => toggleModal('selectProducts')}
              onPressDelete={() => handleDeleteItem(index)}
              onPressToggleQuality={action => handleToggleQuantity(index, action)}
            />
          ))}
        </View>
        {form.values.items?.length > 0 && (
          <WhiteCardBtn
            leftIcon={<Add size={wp(14)} color={colors.primary.main} />}
            onPress={() => toggleModal('selectProducts')}>
            Add Item
          </WhiteCardBtn>
        )}
        <Button
          className="self-end py-10"
          btnStyle="px-30"
          style={{ width: 'auto' }}
          text={'Save'}
          size={ButtonSize.MEDIUM}
          onPress={onPressSave}
          disabled={false}
        />
      </Accordion>

      <SelectSpecificProductsModal
        products={getProductsRequest?.response?.data?.items ?? ([] as any)}
        isVisible={modals.selectProducts}
        loadingStates={{ isLoading: getProductsRequest?.isLoading, isReLoading: getProductsRequest?.isReLoading }}
        closeModal={() => toggleModal('selectProducts', false)}
        getProductsRequest={getProductsRequest as any}
        selectedProducts={form.values?.items?.map(item => item?.id as string)}
        setSelectedProducts={handleSetProductSelection} //todo: @silas take a look at this
        onPressContinue={() => toggleModal('selectProducts', false)}
      />
    </View>
  );
};

export default InvoiceItemsSection;
