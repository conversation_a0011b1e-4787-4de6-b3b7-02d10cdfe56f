import { formatDate, getFieldvalues, wp } from '@/assets/utils/js';
import { CircledIcon } from '@/components/ui';
import { ArrowRight, Search } from '@/components/ui/icons';
import colors from '@/theme/colors';
import { FormikProps } from 'formik';
import { AddCircle } from 'iconsax-react-native/src';
import { useMemo, useRef, useState } from 'react';
import { View } from 'react-native';
import AddCustomerModal from '@/components/customer/add-customer-modal';
import CustomerInitial from '@/components/customer/customer-initial';
import Pressable from '@/components/ui/base/pressable';
import Button, { ButtonSize } from '@/components/ui/buttons/button';
import ListItemCard from '@/components/ui/cards/list-item-card';
import DateInput from '@/components/ui/inputs/date-input';
import Input from '@/components/ui/inputs/input';
import SelectDropdown, { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import Accordion, { AccordionMethod } from '@/components/ui/others/accordion';
import AccordionAnchor from '@/components/ui/others/accordion/accordion-anchor';
import useAuth<PERSON>ontext from 'src/contexts/auth/auth-context';
import { ResponseWithPagination, useApi } from 'src/hooks/use-api';
import useModals from 'src/hooks/use-modals';
import { InvoiceFormParams } from 'src/screens/invoices/create-invoice';
import { CustomerInterface, PaginateSearchParams, GET_CUSTOMERS, CURRENCIES, removeCountryCode } from 'catlog-shared';
import SelectCustomer from 'src/components/customer/select-customer';

interface CustomerResponseWithPagination extends ResponseWithPagination<CustomerInterface[]> {}
export interface CustomerResponse {
  data: CustomerResponseWithPagination;
}

interface BasicInformationSectionProps {
  form: FormikProps<InvoiceFormParams>;
  accordionRef?: React.RefObject<AccordionMethod>;
  onPressSave: VoidFunction;
  isComplete: boolean;
  hasErrors: boolean;
}
const BasicInformationSection = ({
  form,
  accordionRef,
  isComplete,
  hasErrors,
  onPressSave,
}: BasicInformationSectionProps) => {
  const { store } = useAuthContext();
  const { modals, toggleModal } = useModals(['addCustomerModal', 'calender']);

  const dropDownRef = useRef<DropDownMethods>(null);
  const currencyRef = useRef<DropDownMethods>(null);

  const getCustomersRequest = useApi<PaginateSearchParams, CustomerResponse>(
    {
      key: 'get-customers',
      apiFunction: GET_CUSTOMERS,
      method: 'GET',
      onSuccess: response => {},
    },
    {
      filter: {},
      per_page: 9007199254740991,
      sort: 'desc',
    },
  );

  const customersMapped = useMemo(
    () =>
      getCustomersRequest?.response?.data?.data?.map(customer => ({
        value: customer.id!,
        label: customer.name,
        subTitle: removeCountryCode(customer.phone),
        leftElement: <CustomerInitial initial={customer.name[0]} />,
      })) ?? [],
    [getCustomersRequest?.response],
  );

  const handleOpenAddCustomerModal = () => {
    dropDownRef.current?.close();
    setTimeout(() => {
      toggleModal('addCustomerModal');
    }, 600);
  };

  const handleAddCustomer = () => {
    toggleModal('addCustomerModal', false);
    setTimeout(() => {
      dropDownRef.current?.open();
    }, 600);
  };

  const handleSelectCustomer = (customer: CustomerInterface) => {
    const customerId = customer.id!;
    form.setFieldValue('customer', customerId);

    const customers = getCustomersRequest?.response?.data?.data ?? [];
    const selectedCustomer = customers.find(({ id }) => id === customerId);
    form.setFieldValue('customerInfo', selectedCustomer);
  };

  const currencies = CURRENCY_OPTIONS.filter(data => store?.currencies?.storefront?.includes(data.value)) ?? [];

  return (
    <View>
      <Accordion
        initiallyOpened
        anchorElement={status => (
          <AccordionAnchor title="Basic Information" isOpened={status} isSaved={isComplete} isError={hasErrors} />
        )}
        ref={accordionRef}>
        <View>
          <Input {...getFieldvalues('title', form)} label={'Invoice Title'} containerClasses="mt-15" />
          <SelectDropdown
            ref={currencyRef}
            items={currencies}
            onPressItem={value => form.setFieldValue('currency', value)}
            selectedItem={form.values.currency}
            label={'Invoice Currency'}
            containerClasses="mt-15"
          />
          <SelectCustomer
            onSelectCustomer={handleSelectCustomer}
            selectedCustomer={form.values?.customerInfo?.id}
            containerClasses="mt-15"
          />
          <Pressable onPress={() => toggleModal('calender')} className="mt-15">
            <DateInput
              label={'Date Created'}
              value={form.values?.date_created ? formatDate(form.values?.date_created, 'D MMM YYYY') : null}
              onDateChange={date => form.setFieldValue('date_created', date)}
            />
          </Pressable>
          <Pressable onPress={() => toggleModal('calender')} className="mt-15">
            <DateInput
              label={'Due Date'}
              value={form.values?.date_due ? formatDate(form.values?.date_due, 'D MMM YYYY') : null}
              onDateChange={date => form.setFieldValue('date_due', date)}
            />
          </Pressable>
        </View>
        <Button
          className="self-end py-10"
          btnStyle="px-30"
          style={{ width: 'auto' }}
          text={'Save'}
          size={ButtonSize.MEDIUM}
          onPress={onPressSave}
          disabled={false}
        />
      </Accordion>
      <AddCustomerModal
        isVisible={modals.addCustomerModal}
        closeModal={() => toggleModal('addCustomerModal', false)}
        onPressButton={handleAddCustomer}
      />
    </View>
  );
};

export default BasicInformationSection;

const CURRENCY_OPTIONS = [
  {
    value: CURRENCIES.NGN,
    label: 'Naira (₦)',
  },
  {
    value: CURRENCIES.GHC,
    label: 'Cedi (₵)',
  },
  {
    value: CURRENCIES.ZAR,
    label: 'Rand (R)',
  },
  {
    value: CURRENCIES.USD,
    label: 'Dollar ($)',
  },
  {
    value: CURRENCIES.EUR,
    label: 'Euro (€)',
  },
  {
    value: CURRENCIES.GBP,
    label: 'Pound (£)',
  },
];
