import { delay, ensureUniqueItems, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { useNavigation } from '@react-navigation/native';
import { GET_INVOICES, GetInvoicesParams, INVOICE_STATUSES, InvoiceInterface } from 'catlog-shared';
import { Receipt2 } from 'iconsax-react-native/src';
import React, { useEffect, useState } from 'react';
import { RefreshControl, View } from 'react-native';
import Animated, { ScrollHandlerProcessed } from 'react-native-reanimated';
import { ResponseWithPagination, useApi } from 'src/hooks/use-api';
import useInvoiceActions from 'src/hooks/use-invoice-actions';
import usePagination from 'src/hooks/use-pagination';
import { BaseText, CircledIcon } from '../ui';
import EmptyState from '../ui/empty-states/empty-state';
import InvoiceCard from './invoice-card';
import InvoiceInfoModal from './invoice-info-modal';
import InvoicesSkeletonLoader from './invoices-skeleton-loader';
import { filter } from 'eslint.config';

interface InvoicesResponse extends ResponseWithPagination<{ invoices: InvoiceInterface[]; store: string }> {}
interface InvoiceListProps {
  scrollHandler?: ScrollHandlerProcessed<Record<string, unknown>>;
  invoiceStatus?: INVOICE_STATUSES;
  isSearchPage?: boolean;
  filters?: GetInvoicesParams['filter'];
}

const InvoiceList = ({ scrollHandler, isSearchPage = false, filters }: InvoiceListProps) => {
  const navigation = useNavigation();
  const [invoices, setInvoices] = useState<InvoiceInterface[]>([]);
  const { currentPage, goNext, setPage } = usePagination();

  useEffect(() => {
    if (filters) {
      setInvoices([]);
      setPage(0);
    }
  }, [filters]);

  const getInvoicesReq = useApi<GetInvoicesParams, InvoicesResponse>(
    {
      apiFunction: GET_INVOICES,
      key: GET_INVOICES.name,
      method: 'GET',
      onSuccess: response => {
        setInvoices(prev => ensureUniqueItems([...prev!, ...response?.data?.invoices]));
      },
    },
    {
      filter:
        Object.keys(filters ?? {})?.length > 0
          ? filters
          : {
              search: '',
              products: [],
            },
      page: currentPage,
      per_page: 10,
    },
  );

  const { openInvoice, handleInvoiceItemAction, currentInvoice, modals, toggleModal } = useInvoiceActions(
    invoices,
    setInvoices,
  );

  const handlePullToRefresh = async () => {
    try {
      setPage(0);
      await delay(1000);
      setInvoices([]);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <View className="flex-1">
      <Animated.FlatList
        className="flex-1 px-20 pb-40"
        contentContainerStyle={{ flexGrow: 1 }}
        data={invoices}
        onScroll={scrollHandler}
        ListHeaderComponent={() =>
          isSearchPage &&
          invoices?.length > 0 && (
            <View className="py-10 px-5">
              <BaseText classes="text-grey-mutedDark"> {getInvoicesReq?.response?.total ?? 0} Invoices found </BaseText>
            </View>
          )
        }
        keyExtractor={(item, index) => item?.id ?? 'null' + index}
        onEndReachedThreshold={0.3}
        onEndReached={() => {
          if (!getInvoicesReq?.isLoading || !getInvoicesReq?.isReLoading) {
            goNext();
          }
        }}
        refreshControl={<RefreshControl refreshing={false} onRefresh={handlePullToRefresh} />}
        ListEmptyComponent={() =>
          getInvoicesReq?.isLoading === true || getInvoicesReq?.isReLoading == true ? (
            <View key="skeleton" className="mt-20">
              <InvoicesSkeletonLoader />
            </View>
          ) : (
            <>
              {!getInvoicesReq?.isLoading && !getInvoicesReq?.isReLoading && invoices.length < 1 && (
                <EmptyState
                  icon={
                    <CircledIcon
                      className="mb-20 p-15 bg-white"
                      style={{
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 10,
                        },
                        shadowOpacity: 0.05,
                        shadowRadius: 6.84,
                        elevation: 5,
                      }}>
                      <Receipt2 variant="Bulk" size={wp(40)} color={colors.grey.muted} />
                    </CircledIcon>
                  }
                  onPressBtn={() => navigation.navigate('CreateInvoice')}
                  btnText={'Create Invoice'}
                  showBtn={!isSearchPage}
                  text={isSearchPage ? 'No invoices found' : 'No invoices created yet'}
                />
              )}
            </>
          )
        }
        renderItem={({ item, index }) => (
          <InvoiceCard
            onAction={action => handleInvoiceItemAction(action, item)}
            onPress={() => openInvoice(item)}
            index={index}
            item={item}
          />
        )}
        ListFooterComponent={
          <View style={{ marginBottom: 80 }}>
            {getInvoicesReq?.isLoading && (
              <View className="mt-5">
                <InvoicesSkeletonLoader />
              </View>
            )}
          </View>
        }
      />

      {currentInvoice && (
        <InvoiceInfoModal
          copyInvoiceLink={() => handleInvoiceItemAction('copy')}
          editInvoice={() => handleInvoiceItemAction('edit')}
          openOptions={() => handleInvoiceItemAction('options')}
          activeInvoice={currentInvoice}
          isVisible={modals.info}
          {...{ modals, toggleModal, handleInvoiceItemAction }}
          closeModal={() => toggleModal('info', false)}
        />
      )}
    </View>
  );
};

export default InvoiceList;
