import { useNavigation } from '@react-navigation/native';
import { AffiliateInterface, DELETE_AFFILIATE, PaginateSearchParams } from 'catlog-shared';
import { Edit2, Trash, UserOctagon } from 'iconsax-react-native/src';
import { useState } from 'react';
import { Alert, LayoutAnimation, RefreshControl, View } from 'react-native';
import Animated, { ScrollHandlerProcessed } from 'react-native-reanimated';
import Toast from 'react-native-toast-message';

import { updateOrDeleteItemFromList, wp } from '@/assets/utils/js';
import AddAffiliateModal from '@/components/affiliates/add-affiliate-modal';
import AffiliateCard from '@/components/affiliates/affiliate-card';
import AffiliateInformationModal from '@/components/affiliates/affiliate-info-modal';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import FAB from '@/components/ui/buttons/fab';
import EmptyState from '@/components/ui/empty-states/empty-state';
import Shimmer from '@/components/ui/shimmer';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import useModals from '@/hooks/use-modals';
import colors from '@/theme/colors';
import { actionIsAllowed, SCOPES } from 'src/assets/utils/js/permissions';
import useAuthContext from 'src/contexts/auth/auth-context';
import Can from 'src/components/ui/can';
import { OptionWithIcon } from 'src/components/ui/more-options';

interface AffiliateResponseWithPagination extends ResponseWithPagination<AffiliateInterface[]> {}
interface AffiliateResponse {
  data: AffiliateResponseWithPagination;
}

interface AffiliateListSectionProps {
  scrollHandler?: ScrollHandlerProcessed<Record<string, unknown>>;
  affiliates: AffiliateInterface[];
  setAffiliates: React.Dispatch<React.SetStateAction<AffiliateInterface[]>>;
  pullToRefreshFunc?: VoidFunction;
  isReLoading: boolean;
  isLoading: boolean;
  total_pages: number;
  currentPage: number;
  goNext: (totalPages?: number) => void;
  setPage: React.Dispatch<number>;
  fromFiltersPage?: boolean;
  isSearch?: boolean;
  error?: any;
}

const PER_PAGE = 10;
const AffiliateListSection = ({
  scrollHandler,
  affiliates,
  setAffiliates,
  pullToRefreshFunc,
  isReLoading,
  isLoading,
  total_pages,
  currentPage,
  goNext,
  setPage,
  isSearch,
  error,
}: AffiliateListSectionProps) => {
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = () => {
    setIsRetrying(true);
    if (pullToRefreshFunc) {
      pullToRefreshFunc();
      setTimeout(() => setIsRetrying(false), 2000);
    }
  };

  const { userRole } = useAuthContext();
  const [isEdit, setIsEdit] = useState(false);
  const [activeAffiliate, setActiveAffiliate] = useState<AffiliateInterface>({} as AffiliateInterface);

  const { toggleModal, modals } = useModals(['affiliateInfo', 'addAffiliateModal']);

  const navigation = useNavigation();

  const deleteAffiliateRequest = useApi({ key: 'delete-affiliate', apiFunction: DELETE_AFFILIATE, method: 'DELETE' });

  const handleOnPressEdit = () => {
    setIsEdit(true);
    toggleModal('affiliateInfo', false);
    setTimeout(() => {
      toggleModal('addAffiliateModal', true);
    }, 600);
  };

  const handleDeleteAffiliate = async (item: AffiliateInterface, index: number) => {
    const layoutAnimConfig = {
      duration: 300,
      update: {
        type: LayoutAnimation.Types.easeInEaseOut,
      },
      delete: {
        duration: 100,
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
    };
    const delFunc = async () => {
      if (item.id) {
        const [response, error] = await deleteAffiliateRequest.makeRequest({
          id: item.id!,
        });
        if (response) {
          Toast.show({ type: 'success', text1: 'Affiliate deleted successfully' });
          affiliates.splice(index, 1);
          LayoutAnimation.configureNext(layoutAnimConfig);
          setAffiliates(affiliates);
        }
        if (error) {
          Toast.show({ type: 'error', text1: error.body.message });
        }
      }
    };

    Alert.alert('Delete affiliate', 'Delete affiliate permanently', [
      { text: 'Cancel', onPress: () => {} },
      { text: 'Delete', onPress: delFunc, style: 'destructive' },
    ]);
  };

  const canManageAffiliates = actionIsAllowed({
    permission: SCOPES.CUSTOMERS.UPDATE_CUSTOMERS,
    userRole,
  });

  const createAffiliateCallback = (updatedData?: AffiliateInterface) => {
    toggleModal('addAffiliateModal', false);
    if (isEdit) {
      setAffiliates(updateOrDeleteItemFromList(affiliates, 'id', activeAffiliate.id, updatedData ?? null));
      return;
    }
    setAffiliates(prev => [updatedData!, ...prev]);
  };

  const moreOptions = (item: AffiliateInterface, index: number) => [
    {
      optionElement: (
        <OptionWithIcon icon={<Edit2 size={wp(15)} color={colors.black.placeholder} />} label={'Edit Affiliate'} />
      ),
      title: 'Edit Affiliate',
      onPress: () => {
        setActiveAffiliate(item);
        handleOnPressEdit();
      },
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Trash size={wp(15)} color={colors.black.placeholder} />} label={'Delete Affiliate'} />
      ),
      title: 'Delete Affiliate',
      onPress: () => handleDeleteAffiliate(item, index),
    },
  ];

  return (
    <View style={{ flex: 1 }}>
      <QueryErrorBoundary
        error={error}
        isLoading={isLoading}
        refetch={handleRetry}
        isRetrying={isRetrying}
        variant="section"
        customErrorMessage="We couldn't load your affiliates. Please check your connection and try again."
        errorTitle="Failed to load affiliates"
        padAround
        classes="mt-20">
        <Animated.FlatList
          data={affiliates}
          onScroll={scrollHandler}
          scrollEventThrottle={16}
          keyExtractor={(item, index) => item.id + index}
          refreshControl={<RefreshControl refreshing={false} onRefresh={() => pullToRefreshFunc()} />}
          ListEmptyComponent={() =>
            isLoading ? (
              <AffiliateListSkeletonLoader />
            ) : (
              <EmptyState
                icon={<UserOctagon variant="Bulk" size={wp(40)} color={colors.grey.muted} />}
                btnText="Add new affiliate"
                text={"Your affiliates' list \n  will appear here"}
              />
            )
          }
          className="flex-1 px-20"
          contentContainerStyle={{ flexGrow: 1 }}
          renderItem={({ item, index }) => (
            <AffiliateCard
              affiliate={item}
              index={index}
              onPress={() => {
                setActiveAffiliate(item);
                toggleModal('affiliateInfo');
              }}
              moreOptions={moreOptions(item, index)}
            />
          )}
          onEndReached={() => {
            if (!isLoading && affiliates?.length > 0 && currentPage < total_pages) {
              goNext(total_pages);
            }
          }}
          ListFooterComponent={
            <View style={{ marginBottom: 120 }}>
              {affiliates?.length > 0 && isLoading && (
                <View className="mt-5">
                  <AffiliateListSkeletonLoader />
                </View>
              )}
            </View>
          }
        />
      </QueryErrorBoundary>
      {!isSearch && (
        <Can data={{ permission: SCOPES.CUSTOMERS.UPDATE_CUSTOMERS }}>
          <FAB
            onPress={() => {
              setIsEdit(false);
              toggleModal('addAffiliateModal');
            }}
          />
        </Can>
      )}
      {modals.addAffiliateModal && (
        <AddAffiliateModal
          isEdit={isEdit}
          activeAffiliate={activeAffiliate}
          isVisible={modals.addAffiliateModal}
          closeModal={() => toggleModal('addAffiliateModal', false)}
          callBack={data => createAffiliateCallback(data)}
        />
      )}
      {modals.affiliateInfo && (
        <AffiliateInformationModal
          isVisible={modals.affiliateInfo}
          activeAffiliate={activeAffiliate}
          closeModal={() => toggleModal('affiliateInfo', false)}
          onPressButton={() => handleOnPressEdit()}
          canManageAffiliates={canManageAffiliates}
        />
      )}
    </View>
  );
};

export default AffiliateListSection;

export const AffiliateSkeleton = () => {
  return (
    <View className="flex-row items-center border-b border-b-grey-border py-15">
      <Shimmer {...{ height: 50, width: 50, borderRadius: 100 }} />
      <View className="mx-12 flex-1">
        <Row className="mr-12 mb-10">
          <Shimmer {...{ height: 15, width: 150, borderRadius: 50 }} className="mr-2" />
          <Shimmer {...{ height: 5, width: 15, borderRadius: 50 }} />
        </Row>
        <Shimmer {...{ height: 10, width: 70, borderRadius: 50 }} />
      </View>
    </View>
  );
};

export const AffiliateListSkeletonLoader = () => {
  return (
    <View>
      {Array.from({ length: 10 }, (_, i) => i).map(i => (
        <AffiliateSkeleton key={i} />
      ))}
    </View>
  );
};
