import { ScrollView, View } from 'react-native';
import Input from '../ui/inputs/input';
import PhoneNumberInput from '../ui/inputs/phone-number-input';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { delay, enumToHumanFriendly, getFieldvalues, hp, wp } from '@/assets/utils/js';
import { phoneValidation } from '@/assets/utils/js/common-validations';
import { useApi } from '@/hooks/use-api';
import Toast from 'react-native-toast-message';
import { useEffect, useMemo } from 'react';
import {
  AffiliateInterface,
  CreateAffiliateParams,
  AFFILIATE_TYPES,
  UpdateAffiliateParams,
  CREATE_AFFILIATE,
  UPDATE_AFFILIATE,
} from 'catlog-shared';
import { BottomSheetScrollView, BottomSheetView } from '@gorhom/bottom-sheet';
import SelectDropdown from '../ui/inputs/select-dropdown';

interface AddAffiliateModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressButton?: () => void;
  callBack?: (updateData?: AffiliateInterface) => void;
  btnTitle?: string;
  activeAffiliate?: AffiliateInterface;
  isEdit?: boolean;
}

export interface AffiliateFormParams extends Omit<CreateAffiliateParams, 'phone'> {
  phone: { code: string; digits: string };
  id?: string;
}

const AddAffiliateModal = ({
  closeModal,
  callBack,
  activeAffiliate,
  isEdit,
  btnTitle,
  ...props
}: AddAffiliateModalProps) => {
  const createAffiliateRequest = useApi<CreateAffiliateParams>({
    apiFunction: CREATE_AFFILIATE,
    method: 'POST',
    key: 'create-affiliate',
  });

  const updateAffiliateRequest = useApi<UpdateAffiliateParams>({
    apiFunction: UPDATE_AFFILIATE,
    method: 'PUT',
    key: 'update-affiliate',
  });

  const form = useFormik<AffiliateFormParams>({
    initialValues: {
      id: '',
      type: AFFILIATE_TYPES.PERSON,
      name: '',
      email: '',
      phone: {
        code: '+234',
        digits: '',
      },
    },
    validationSchema: addAffiliateValidationSchema,
    onSubmit: async values => {
      const phone = `${values.phone.code}-${values.phone.digits}`;
      let response = null;
      let error = null;

      if (isEdit) {
        const editResponse = await updateAffiliateRequest.makeRequest({
          id: values.id!,
          name: values.name,
          email: values.email,
          type: values.type,
          phone,
        });

        response = editResponse[0];
        error = editResponse[1];
      } else {
        const createResponse = await createAffiliateRequest.makeRequest({
          name: values.name,
          email: values.email,
          type: values.type,
          phone,
        });

        response = createResponse[0];
        error = createResponse[1];
      }

      if (response) {
        form.resetForm();
        callBack?.(response?.data);
        closeModal();
        await delay(700);
        Toast.show({ type: 'success', text1: `Affiliate ${isEdit ? 'updated' : 'created'} successfully` });
        return;
      }

      Toast.show({ type: 'error', text1: error.body.body.message ?? 'Something went wrong' });
    },
    enableReinitialize: true,
  });

  useEffect(() => {
    if (!isEdit) {
      form.resetForm();
    }
    if (isEdit && activeAffiliate) {
      form.setFieldValue('id', activeAffiliate.id);
      form.setFieldValue('name', activeAffiliate.name);
      form.setFieldValue('type', activeAffiliate.type);
      if (activeAffiliate.type === AFFILIATE_TYPES.PERSON) {
        form.setFieldValue('email', activeAffiliate.email);
        const phoneSplit = {
          code: activeAffiliate?.phone?.split('-')[0],
          digits: activeAffiliate?.phone?.includes('-')
            ? activeAffiliate?.phone?.split('-')[1]
            : activeAffiliate?.phone,
        };
        form.setFieldValue('phone', phoneSplit);
      }
    }
  }, [activeAffiliate]);

  const preOpeningAction = () => {
    if (!isEdit) {
      form.resetForm();
    }
    if (isEdit && activeAffiliate) {
      form.setFieldValue('id', activeAffiliate.id);
      form.setFieldValue('name', activeAffiliate.name);
      if (activeAffiliate.type === AFFILIATE_TYPES.PERSON) {
        form.setFieldValue('email', activeAffiliate.email);
        const phoneSplit = {
          code: activeAffiliate?.phone?.split('-')[0],
          digits: activeAffiliate?.phone?.includes('-') ? activeAffiliate?.phone?.split('-')[1] : activeAffiliate?.phone,
        };
        form.setFieldValue('phone', phoneSplit);
      }
    }
  };

  const affiliateTypesMapped = useMemo(
    () =>
      Object.values(AFFILIATE_TYPES).map(type => ({
        value: type,
        label: enumToHumanFriendly(type),
      })),
    [],
  );

  const isPerson = form.values.type === AFFILIATE_TYPES.PERSON;

  return (
    <BottomModal
      enableDynamicSizing
      {...props}
      closeModal={closeModal}
      title={isEdit ? 'Edit Affiliate' : 'Add Affiliate'}
      onModalShow={preOpeningAction}
      useScrollView
      useChildrenAsDirectChild
      buttons={[
        {
          text: isEdit ? 'Update Affiliate' : 'Save Affiliate',
          onPress: () => form.handleSubmit(),
          isLoading: createAffiliateRequest.isLoading || updateAffiliateRequest.isLoading,
        },
      ]}>
      <BottomSheetView style={{ paddingHorizontal: wp(20) }} enableFooterMarginAdjustment>
        <SelectDropdown
          {...getFieldvalues('type', form, 'select')}
          showLabel
          label={'Affiliate type'}
          items={affiliateTypesMapped}
          descriptionProps={{ classes: 'mt-5' }}
          containerClasses="mt-15"
        />
        <Input
          useBottomSheetInput
          label={form.values.type === AFFILIATE_TYPES.PERSON ? 'Name' : "Name e.g. 'Instagram Ad'"}
          {...getFieldvalues('name', form)}
          containerClasses="mt-15"
        />
        {isPerson && (
          <>
            <PhoneNumberInput
              label={'Phone Number'}
              useBottomSheetInput
              containerClasses="mt-15"
              {...getFieldvalues('phone', form)}
              onChange={value => form.setFieldValue('phone', value)}
            />
            <Input
              useBottomSheetInput
              label={'Email Address'}
              keyboardType={'email-address'}
              autoCapitalize="none"
              containerClasses="mt-15"
              {...getFieldvalues('email', form)}
            />
          </>
        )}
        <View className="h-40" />
      </BottomSheetView>
    </BottomModal>
  );
};

export const addAffiliateValidationSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  phone: phoneValidation(),
  email: Yup.string().email('Invalid email address'),
  type: Yup.string().required('Type is required'),
});

export default AddAffiliateModal;
