import { useNavigation } from '@react-navigation/native';
import {
  ORDER_STATUSES,
  OrderInterface,
  BULK_ORDER_UPDATE,
  UPDATE_ORDER_PAYMENT_STATUS,
  GET_RECENT_PAYMENTS,
  PaymentInterface,
  humanFriendlyDate,
  LINK_INVOICE_TO_PAYMENTS,
  GetOrdersParams,
} from 'catlog-shared';
import { ArrowDown2, Bag, CloseCircle, LinkSquare, Money, Shop, TickCircle } from 'iconsax-react-native/src';
import React, { useEffect, useRef, useState } from 'react';
import { Platform, RefreshControl, View } from 'react-native';
import * as Animatable from 'react-native-animatable';
import Animated, { ScrollHandlerProcessed } from 'react-native-reanimated';
import Toast from 'react-native-toast-message';
import Input from 'src/components/ui/inputs/input';
import { colorAlternates } from 'src/constant/static-data';
import useAuthStore from 'src/contexts/auth/store';
import { ResponseWithoutPagination, useApi } from 'src/hooks/use-api';
import { OrderListSkeletonLoader } from 'src/screens/orders/list';

import OrderItemCard from './order-item-card';

import {
  alertPromise,
  delay,
  enumToHumanFriendly,
  toCurrency,
  toNaira,
  updateOrDeleteItemFromList,
  wp,
} from '@/assets/utils/js';
import { BaseModal, BaseText } from '@/components/ui';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import CircledIcon from '@/components/ui/circled-icon';
import EmptyState from '@/components/ui/empty-states/empty-state';
import { ArrowRight } from '@/components/ui/icons';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import SelectDropdown, { DropDownItem, DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import useOrdersApi from '@/hooks/use-orders-api';
import colors from '@/theme/colors';
import { FINALIZED_ORDERS } from 'src/assets/utils/js/constants';
import { filter } from 'eslint.config';
import BottomSheet, { BottomSheetModal, BottomSheetView } from '@gorhom/bottom-sheet';
import eventEmitter from 'src/assets/utils/js/event-emitter';
import useEventListener from 'src/hooks/use-event-emitter';

enum UPDATE_METHODS {
  LINK_PAYMENT = 'LINK_PAYMENT',
  MANUAL = 'MANUAL',
}

interface DropdownRefs {
  bulkOrderUpdate: DropDownMethods;
  markAsPaid: DropDownMethods;
  payments: DropDownMethods;
}

interface OrdersListProps {
  scrollHandler?: ScrollHandlerProcessed<Record<string, unknown>>;
  filters: GetOrdersParams['filter'];
  externalFilters?: GetOrdersParams['filter'];
  totalFound?: number;
  isSearchPage?: boolean;
}

const OrdersList = ({ scrollHandler, filters, isSearchPage = false, totalFound }: OrdersListProps) => {
  const [updatePaymentMethod, setUpdatePaymentMethod] = useState<UPDATE_METHODS | null>(null);
  const [activeOrder, setActiveOrder] = useState<string>('');
  const [selectedPayment, setSelectedPayment] = useState<string>(null);

  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const { orders, setOrders, getOrdersRequest, handlePullToRefresh, handleOnEndReach } = useOrdersApi(filters);
  const navigation = useNavigation();
  const [showOrderActionToolBox, setShowOrderActionToolBox] = useState(false);

  const dropdownRefs = useRef<{ [K in keyof DropdownRefs]: DropDownMethods | null }>({
    bulkOrderUpdate: null,
    markAsPaid: null,
    payments: null,
  });

  const changeBulkStatusRequest = useApi({ apiFunction: BULK_ORDER_UPDATE, method: 'PUT', key: 'update-bulk-order' });
  const getRecentPaymentsRequest = useApi<void, ResponseWithoutPagination<PaymentInterface[]>>({
    apiFunction: GET_RECENT_PAYMENTS,
    method: 'GET',
    autoRequest: false,
    key: 'get-recent-payments',
  });

  const linkPaymentRequest = useApi({
    apiFunction: LINK_INVOICE_TO_PAYMENTS,
    method: 'PUT',
    key: 'link-invoice-payments',
  });

  const payments = getRecentPaymentsRequest.response?.data ?? ([] as PaymentInterface[]);

  useEventListener(`${filters.status}_UPDATES`, data => {
    setOrders(prev => [data, ...prev]);
  });

  const mappedPayments: DropDownItem[] = payments.map((p, i) => {
    const color = colorAlternates[i ? i % colorAlternates.length : 0];

    return {
      value: p.id,
      label: `${enumToHumanFriendly(p?.payment_method ?? 'UNKNOWN')} - ${humanFriendlyDate(p?.created_at)}`,
      subTitle: toCurrency(toNaira(p.amount), p.currency),
      leftElement: (
        <CircledIcon style={{ backgroundColor: color.bgColor }}>
          <Money color={color.iconColor} variant="Bold" size={wp(24)} />
        </CircledIcon>
      ),
    };
  });

  const updatePaymentStatusRequest = useApi({
    apiFunction: UPDATE_ORDER_PAYMENT_STATUS,
    method: 'PUT',
    key: 'update-payment-status',
  });

  const toggleMarkAsPaid = async (id: string) => {
    if (!id) {
      return;
    }

    setActiveOrder(id);
    await delay(800);
    dropdownRefs.current?.markAsPaid?.open();
  };

  const onSelectPaymentLinkingMethod = async () => {
    if (updatePaymentMethod === UPDATE_METHODS.MANUAL) {
      const alertResponse = await alertPromise(
        'Update payment Status',
        `Clicking on "Mark as paid" would update this order to PAID, this action cannot be reversed.`,
        'Mark as paid',
        'Cancel',
        false,
      );
      if (alertResponse === false) {
        return;
      }
      handlePaymentStatusUpdate();
      return;
    }

    dropdownRefs.current?.markAsPaid.close();
    await delay(800);
    dropdownRefs.current?.payments?.open();
    setUpdatePaymentMethod(null);
  };

  const handlePaymentStatusUpdate = async () => {
    const [response, error] = await updatePaymentStatusRequest.makeRequest({
      id: activeOrder,
    });

    if (response) {
      dropdownRefs.current?.markAsPaid?.close();
      setOrders(updateOrDeleteItemFromList(orders, 'id', activeOrder, { is_paid: true }));
      await delay(800);
      setUpdatePaymentMethod(null);
      Toast.show({ type: 'success', text1: 'Payment status updated successfully' });
    }
    if (error) {
      Toast.show({ type: 'error', text1: error?.body?.body?.message });
    }
  };

  const linkPayment = async () => {
    if (!selectedPayment) return;

    const order = orders.find(o => o.id === activeOrder);
    const payment = payments.find(p => p.id === selectedPayment);

    const [res, err] = await linkPaymentRequest.makeRequest({
      invoice_id: typeof order?.invoice === 'string' ? order.invoice : order.invoice.invoice_id,
      reference: payment?.reference,
    });

    if (res) {
      setOrders(updateOrDeleteItemFromList(orders, 'id', activeOrder, { is_paid: true }));

      dropdownRefs.current?.payments.close();
      await delay(800);
      Toast.show({ text1: 'Payment linked successfully', type: 'success' });
    }

    if (err) {
      Toast.show({ text1: err?.body?.body?.message, type: 'error' });
    }
  };

  const actionCallBack = (activeKey: string, updatedData?: OrderInterface) => {
    setOrders(updateOrDeleteItemFromList(orders, 'id', activeKey, updatedData ?? null));
  };

  const getNextOrderStatus = () => {
    let status: ORDER_STATUSES;

    switch (filters.status) {
      case ORDER_STATUSES.PENDING:
        status = ORDER_STATUSES.PROCESSING;
        break;
      case ORDER_STATUSES.PROCESSING:
        status = ORDER_STATUSES.FULFILLED;
        break;
      default:
        status = ORDER_STATUSES.PROCESSING;
        break;
    }

    return status;
  };

  useEffect(() => {
    if (selectedOrders.length > 0) {
      // setShowOrderActionToolBox(false);
      bottomSheetRef.current?.present();
    } else {
      bottomSheetRef.current.close();
      // setShowOrderActionToolBox(true);
    }
  }, [selectedOrders]);

  const selectOrders = (orderId: string) => {
    const orderCopy = [...selectedOrders];
    const indexSearch = orderCopy.findIndex(d => d === orderId);

    if (indexSearch === -1) {
      setSelectedOrders(prev => [...prev, orderId]);
      return;
    }

    orderCopy.splice(indexSearch, 1);
    setSelectedOrders(orderCopy);
  };

  const actionText =
    filters?.status === ORDER_STATUSES.PENDING
      ? 'Mark Orders as Confirmed'
      : filters.status === ORDER_STATUSES.PROCESSING
        ? 'Mark Orders as Fulfilled'
        : '';

  const handleBulkOrderUpdate = async (status: ORDER_STATUSES) => {
    const selectOrder = (id: string) => orders.find(o => o.id === id);
    const alertResponse = await alertPromise(
      'Update Order Status',
      `Clicking "Yes, Update" will update the order status of the selected orders to ${status}`,
      'Yes, Update',
      'Cancel',
      false,
    );
    if (alertResponse === false) {
      return;
    }

    const reqData = { orders: selectedOrders.map(id => ({ id, status })) };

    const [response, error] = await changeBulkStatusRequest.makeRequest(reqData);

    if (response) {
      Toast.show({ type: 'success', text1: 'Selected order status updated successfully' });

      await Promise.all(selectedOrders.map(async i => {
        eventEmitter.emit(`${status}_UPDATES`, { ...selectOrder(i), status });
      }));
      setOrders(orders.filter(o => !selectedOrders.includes(o.id)));
      // selectedOrders.forEach(async i => {
      //   setOrders(updateOrDeleteItemFromList(orders, 'id', i, null));
      //   eventEmitter.emit(`${status}_UPDATES`, { ...selectOrder(i), status });
      // });
      setSelectedOrders([]);
    }

    if (error) {
      Toast.show({ type: 'error', text1: error.body.body.message });
    }
  };

  const action = [
    {
      value: 'action',
      label: actionText,
      leftElement: (
        <CircledIcon>
          <TickCircle color={colors.accentGreen.main} size={wp(15)} strokeWidth={2} />
        </CircledIcon>
      ),
      rightElement: (
        <CircledIcon className="bg-white">
          <ArrowRight currentColor={colors.primary.main} strokeWidth={2} size={wp(15)} />
        </CircledIcon>
      ),
    },
    {
      value: 'cancel',
      label: 'Cancel Selected Orders',
      leftElement: (
        <CircledIcon>
          <CloseCircle color={colors.accentRed.main} size={wp(15)} strokeWidth={2} />
        </CircledIcon>
      ),
      rightElement: (
        <CircledIcon className="bg-white">
          <ArrowRight currentColor={colors.primary.main} strokeWidth={2} size={wp(15)} />
        </CircledIcon>
      ),
    },
  ];

  const markAsPaidAction: DropDownItem[] = [
    {
      value: UPDATE_METHODS.LINK_PAYMENT,
      label: 'Link a payment',
      subTitle: 'Connect a payment made by a customer',
      leftElement: (
        <CircledIcon>
          <LinkSquare color={colors.accentGreen.main} variant="Bold" size={wp(20)} />
        </CircledIcon>
      ),
    },
    {
      value: UPDATE_METHODS.MANUAL,
      label: 'Update Manually',
      subTitle: 'Manually update this order to paid',
      leftElement: (
        <CircledIcon>
          <Shop color={colors.accentOrange.main} variant="Bold" size={wp(20)} />
        </CircledIcon>
      ),
    },
  ];

  const bottomSheetRef = useRef<BottomSheetModal>(null);

  return (
    <>
      <QueryErrorBoundary
        error={getOrdersRequest.error}
        isLoading={getOrdersRequest.isLoading}
        refetch={getOrdersRequest.refetch}
        variant="section"
        errorTitle="Failed to load orders"
        padAround>
        <Animated.FlatList
          data={orders}
          removeClippedSubviews={Platform.OS === 'android'}
          windowSize={Platform.OS === 'android' ? 10 : 21}
          maxToRenderPerBatch={Platform.OS === 'android' ? 15 : 30}
          updateCellsBatchingPeriod={Platform.OS === 'android' ? 50 : 100}
          scrollEventThrottle={16}
          onScroll={scrollHandler}
          onEndReachedThreshold={0.3}
          refreshControl={<RefreshControl refreshing={false} onRefresh={() => handlePullToRefresh()} />}
          renderItem={({ item, index }) => (
            <OrderItemCard
              order={item}
              actionCallBack={actionCallBack}
              onPress={() => navigation.navigate('OrderInfo', { id: item.id })}
              orderStatus={filters.status}
              handleSelection={() => selectOrders(item.id)}
              toggleMarkAsPaid={toggleMarkAsPaid}
              isSelectionActive={selectedOrders.includes(item.id)}
            />
          )}
          ListHeaderComponent={() =>
            isSearchPage &&
            orders.length > 0 && (
              <View className="py-10 px-5">
                <BaseText classes="text-grey-mutedDark">
                  {' '}
                  {getOrdersRequest?.response?.data?.total ?? 0} Orders found{' '}
                </BaseText>
              </View>
            )
          }
          ListFooterComponent={() => (
            <View style={{ paddingBottom: 120 }} className="px-10">
              {orders?.length > 0 && getOrdersRequest.isLoading && (
                <View>
                  <OrderListSkeletonLoader />
                </View>
              )}
            </View>
          )}
          ListEmptyComponent={() =>
            getOrdersRequest.isLoading ? (
              <OrderListSkeletonLoader />
            ) : (
              <EmptyState
                icon={<Bag variant="Bulk" size={wp(40)} color={colors.grey.muted} />}
                btnText="Record an Order"
                onPressBtn={() => navigation.navigate('RecordOrder')}
                showBtn={!FINALIZED_ORDERS.includes(filters.status)}
                text={
                  FINALIZED_ORDERS.includes(filters.status)
                    ? `${enumToHumanFriendly(filters?.status ?? '')} orders \n will appear here`
                    : `No ${enumToHumanFriendly(filters?.status ?? '').toLowerCase()} orders to show \n try recording an order`
                }
              />
            )
          }
          onEndReached={handleOnEndReach}
          className="flex-1 px-10"
          contentContainerStyle={{ flexGrow: 1 }}
        />
      </QueryErrorBoundary>
      <BottomSheetModal
        ref={bottomSheetRef}
        enableOverDrag={false}
        enableDynamicSizing
        handleComponent={() => <View />}>
        <BottomSheetView>
          <FixedBtnFooter
            style={{ paddingBottom: 0 }}
            buttons={[
              {
                text: 'Order Selected',
                isLoading: changeBulkStatusRequest.isLoading,
                onPress: () => dropdownRefs.current?.bulkOrderUpdate.open(),
                leftAddOn: (
                  <CircledIcon className="p-0 h-24 w-24 mr-10">
                    <BaseText fontSize={12} weight="semiBold" classes="text-primary-main">
                      {selectedOrders?.length}
                    </BaseText>
                  </CircledIcon>
                ),
                rightAddOn: (
                  <View className="ml-8">
                    <ArrowDown2 size={wp(18)} color={colors.white} />
                  </View>
                ),
              },
            ]}
          />
        </BottomSheetView>
      </BottomSheetModal>
      <SelectDropdown
        showAnchor={false}
        closeAfterSelection={false}
        // ref={markAsPaidDropDownRef}
        ref={ref => (dropdownRefs.current.markAsPaid = ref)}
        selectedItem={updatePaymentMethod}
        headerComponent={
          <View className="mx-20">
            <BaseText classes="text-black-muted">
              Clicking "Mark as Paid" will set this order to PAID permanently. Proceed?
            </BaseText>
          </View>
        }
        onPressItem={v => setUpdatePaymentMethod(v as UPDATE_METHODS)}
        items={markAsPaidAction}
        containerClasses="mb-15"
        label="Mark as Paid"
        genItemKeysFun={value => value.label}
        showButton
        buttons={[
          {
            text: 'Link Payment',
            onPress: onSelectPaymentLinkingMethod,
            isLoading: updatePaymentStatusRequest.isLoading,
          },
        ]}
        // showButton
        showLabel
      />
      <SelectDropdown
        showAnchor={false}
        closeAfterSelection={false}
        sectionEmptyStateProps={{
          text: 'You have no payments yet',
          icon: (
            <CircledIcon className="bg-white p-15 mb-12">
              <Money variant="Bold" color={colors.grey.muted} size={wp(30)} />
            </CircledIcon>
          ),
        }}
        ref={ref => (dropdownRefs.current.payments = ref)}
        selectedItem={selectedPayment}
        headerComponent={
          payments.length > 0 && (
            <View>
              <Input placeholder="Search Payments" />
            </View>
          )
        }
        onPressItem={v => setSelectedPayment(v)}
        items={mappedPayments}
        containerClasses="mb-15"
        label="Mark as Paid"
        genItemKeysFun={value => value.label}
        showButton={payments.length > 0}
        buttons={[
          {
            text: 'Link Payment',
            onPress: linkPayment,
            isLoading: linkPaymentRequest.isLoading,
          },
        ]}
        showLabel
      />
      <SelectDropdown
        // ref={dropDownRef}
        ref={ref => (dropdownRefs.current.bulkOrderUpdate = ref)}
        showAnchor={false}
        selectedItem=""
        onPressItem={item => handleBulkOrderUpdate(item === 'cancel' ? ORDER_STATUSES.CANCELLED : getNextOrderStatus())}
        // showLabel
        items={action}
        descriptionProps={{ classes: 'mt-5' }}
        containerClasses="my-15"
      />
    </>
  );
};

export default OrdersList;
