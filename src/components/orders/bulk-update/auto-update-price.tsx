import { getFieldvalues, hp, toCurrency, wp, Yup } from '@/assets/utils/js';
import { BaseText, CircledIcon, Container, Row, WhiteCardBtn } from '@/components/ui';
import Input from 'src/components/ui/inputs/input';
import { View } from 'react-native';
import { AutoUpdateFormProps, BulkUpdateForm, BulkUpdateMethod } from 'src/screens/products/types';
import CustomImage from 'src/components/ui/others/custom-image';
import { useMemo, useState } from 'react';
import SelectDropdown from 'src/components/ui/inputs/select-dropdown';
import Separator from 'src/components/ui/others/separator';
import { FormikProps, useFormik } from 'node_modules/formik/dist';
import LeftLabelledInput from 'src/components/ui/inputs/left-labelled-input';
import { BulkUpdateFormItem } from 'catlog-shared';

interface AutoUpdatePriceProps {
  selected: BulkUpdateFormItem[];
  currency: string;
  autoUpdateForm: FormikProps<AutoUpdateFormProps>;
  calcNewPrice: (old_price: number, values: AutoUpdateFormProps) => number;
}

const AutoUpdatePrice = ({
  currency,
  selected,
  autoUpdateForm,
  calcNewPrice,
}: AutoUpdatePriceProps) => {

  const previewItems = useMemo(() => {
    const items = [...selected];

    for (let i = 0; i < 3; i++) {
      const j = i + Math.floor(Math.random() * (items.length - i));
      [items[i], items[j]] = [items[j], items[i]];
    }
    return items.slice(0, 3);
  }, [selected]);

  const changePercentage = (dir: '+' | '-') => {
    const value = autoUpdateForm?.values.percentage;
    if (dir === '+' && value < 100) {
      autoUpdateForm?.setFieldValue('percentage', value + 1);
    } else if (dir === '-' && value > 0) {
      autoUpdateForm?.setFieldValue('percentage', value - 1);
    }
  };

  return (
    <View>
      <BaseText type="heading">Update Price Automatically</BaseText>
      <SelectDropdown
        selectedItem={autoUpdateForm?.values.price_action}
        onPressItem={i => autoUpdateForm?.setFieldValue('price_action', i)}
        label={'Increase or Decrease Price'}
        items={updateType}
        containerClasses="mt-15"
        showLabel
      />
      <SelectDropdown
        selectedItem={autoUpdateForm?.values.update_method}
        onPressItem={i => autoUpdateForm?.setFieldValue('update_method', i)}
        label={'Update Method'}
        items={updateMethod}
        containerClasses="mt-15"
        showLabel
      />
      {autoUpdateForm?.values.update_method === 'percentage' ? (
        <Input
          label={'Percentage (No more than 100)'}
          containerClasses="mt-15"
          {...getFieldvalues('percentage', autoUpdateForm)}
          error={getFieldvalues('percentage', autoUpdateForm).error}
          hasError={getFieldvalues('percentage', autoUpdateForm).hasError}
          keyboardType="numeric"
        />
      ) : (
        <LeftLabelledInput
          leftText={currency}
          label={'Amount (No more than 100)'}
          containerClasses="mt-15"
          {...getFieldvalues('amount', autoUpdateForm)}
          error={getFieldvalues('amount', autoUpdateForm).error}
          hasError={getFieldvalues('amount', autoUpdateForm).hasError}
          keyboardType="numeric"
        />
      )}
      <Separator className="mx-0 my-20" />
      <View>
        <BaseText type="heading">Preview</BaseText>
        <View className="mt-20" style={{ rowGap: hp(15) }}>
          {previewItems.map((p, i) => (
            // i >= batchData.batchStart && i < batchData.batchEnd &&
            <View className="bg-grey-bgOne rounded-12" key={i}>
              <Row className="justify-start py-8 px-12">
                <CustomImage
                  imageProps={{ source: { uri: p?.image }, contentFit: 'cover' }}
                  className="h-40 w-40 rounded-8"
                />
                <View className="flex-1">
                  <BaseText weight="medium" classes="text-black-secondary ml-10">
                    {p?.name}
                  </BaseText>
                </View>
              </Row>
              <Row className="bg-white rounded-12 border border-grey-border">
                <View className="flex-1 p-15">
                  <BaseText classes="line-through text-black-placeholder">{toCurrency(p?.old_price)}</BaseText>
                </View>
                <View className="flex-1 border-l border-l-grey-border p-15">
                  <BaseText>
                    {/* {toCurrency(p.price)} */}
                    {toCurrency(calcNewPrice(p?.old_price, autoUpdateForm?.values))}
                  </BaseText>
                </View>
              </Row>
            </View>
          ))}
        </View>
      </View>
    </View>
  );
};

export default AutoUpdatePrice;

const updateType = [
  {
    value: 'increase',
    label: 'Increase',
  },
  {
    value: 'decrease',
    label: 'Decrease',
  },
];

const updateMethod = [
  {
    value: 'percentage',
    label: 'Percentage',
  },
  {
    value: 'fixed',
    label: 'Fixed',
  },
];
