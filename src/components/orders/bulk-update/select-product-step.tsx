import colors from '@/theme/colors';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Container, Row, WhiteCardBtn } from '@/components/ui';
import { Note1, Profile, Shop } from 'iconsax-react-native/src';
import useRouteParams from '@/hooks/use-route-params';
import useModals from 'src/hooks/use-modals';
import ScreenInfoHeader, { ColorPaletteType } from 'src/components/ui/screen-info-header';
import useSteps from 'src/hooks/use-steps';
import Pressable from 'src/components/ui/base/pressable';
import Input from 'src/components/ui/inputs/input';
import { View } from 'react-native';
import { ChevronDown } from 'src/components/ui/icons';
import SelectProductsModal from 'src/components/products/storefront-products/select-products-modal';
import { ApiData } from 'src/hooks/use-api';
import { useApi } from 'src/hooks/use-api';
import { ProductsResponse } from 'src/screens/products/storefront';
import SectionContainer from 'src/components/ui/section-container';
import ListItemCard from 'src/components/ui/cards/list-item-card';
import Radio from 'src/components/ui/buttons/radio';
import SelectSpecificProductsModal from 'src/components/products/storefront-products/select-specific-product-modal';
import { BulkUpdateMethod } from 'src/screens/products/types';
import { ProductItemInterface, GetItemsParams, GET_ITEMS, VariantItem } from 'catlog-shared';

enum UPDATE_PRICE_STEPS {
  SELECT_PRODUCT = 'SELECT_PRODUCT',
  SELECT_UPDATE_TYPE = 'SELECT_UPDATE_TYPE',
  INCREASE_PRICE_FORM = 'INCREASE_PRICE_FORM',
}

interface SelectProductStepProps {
  handleItemsSelect: (itemsSelected: string[]) => void;
  selectedItems: string[];
  isQuantity?: boolean;
  method: BulkUpdateMethod;
  setMethod: React.Dispatch<React.SetStateAction<BulkUpdateMethod>>;
  getProductsRequest: ApiData<GetItemsParams, { data: { items: ProductItemInterface[] } }>;
}

const SelectProductStep = ({
  handleItemsSelect,
  selectedItems,
  isQuantity,
  getProductsRequest,
  method,
  setMethod,
}: SelectProductStepProps) => {
  const { modals, toggleModal } = useModals(['selectProducts']);

  return (
    <View>
      <View style={{ display: selectedItems?.length > 0 ? 'none' : 'flex' }}>
        <BaseText type="heading" fontSize={15}>
          Select Products
        </BaseText>
        <Pressable onPress={() => toggleModal('selectProducts')} className="mt-15">
          <Input
            editable={false}
            onPressIn={() => toggleModal('selectProducts')}
            label={'Select Product'}
            rightAccessory={
              <View className="p-3 my-12 bg-grey-bgOne rounded-full">
                <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.grey.muted} />
              </View>
            }
            containerClasses={`py-0`}
          />
        </Pressable>
      </View>

      <View style={{ display: selectedItems?.length > 0 ? 'flex' : 'none' }}>
        <Row className="bg-grey-bgOne pl-14 p-10 rounded-12">
          <BaseText>{selectedItems?.length} Product Selected</BaseText>
          <WhiteCardBtn onPress={() => toggleModal('selectProducts')}>Edit Selection</WhiteCardBtn>
        </Row>
        {!isQuantity && (
          <SectionContainer className="py-0 px-0">
            {options.map((o, index) => (
              <ListItemCard
                leftElement={<CircledIcon iconBg="bg-white">{o.icon}</CircledIcon>}
                rightElement={<Radio active={method === o.key} />}
                titleProps={{ fontSize: wp(14), type: 'heading', style: { marginBottom: wp(3) } }}
                className="py-14 px-12"
                title={o.title}
                description={o.description}
                onPress={() => setMethod(o.key)}
                showBorder={index !== options.length - 1}
                descriptionProps={{ fontSize: wp(12), weight: 'regular', classes: 'mt-2' }}
                key={index}
              />
            ))}
          </SectionContainer>
        )}
      </View>
      <SelectSpecificProductsModal
        products={getProductsRequest?.response?.data?.items ?? []}
        isVisible={modals.selectProducts}
        loadingStates={{isLoading: getProductsRequest?.isLoading, isReLoading: getProductsRequest?.isReLoading}}
        closeModal={() => toggleModal('selectProducts', false)}
        getProductsRequest={getProductsRequest}
        selectedProducts={selectedItems}
        setSelectedProducts={handleItemsSelect}
        onPressContinue={() => toggleModal('selectProducts', false)}
      />
    </View>
  );
};

export default SelectProductStep;

const options = [
  {
    title: 'Update Price Manually',
    description: 'Manually update individual Prices',
    icon: <Profile size={wp(20)} variant={'Bold'} color={colors.accentOrange.main} />,
    show: true,
    key: BulkUpdateMethod.MANUAL,
  },
  {
    title: 'Update Automatically',
    description: 'Update products by percentages or an amount',
    icon: <Shop size={wp(20)} variant={'Bold'} color={colors.accentYellow.main} />,
    key: BulkUpdateMethod.AUTO,
  },
];
