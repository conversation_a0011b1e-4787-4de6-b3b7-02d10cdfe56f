import colors from '@/theme/colors';
import { hp, wp } from '@/assets/utils/js';
import { BaseText, Row, WhiteCardBtn } from '@/components/ui';
import useModals from 'src/hooks/use-modals';
import { View } from 'react-native';
import { ArrowUpRight } from 'src/components/ui/icons';
import { AutoUpdateFormProps, BulkUpdateForm, BulkUpdateMethod } from 'src/screens/products/types';
import CustomImage from 'src/components/ui/others/custom-image';
import LeftLabelledInput from 'src/components/ui/inputs/left-labelled-input';
import updateOptionsQuantityModal from './update-option-price-modal';
import { useState } from 'react';
import AutoUpdatePrice from './auto-update-price';
import { BulkUpdateFormItem } from 'catlog-shared';
import { FormikProps } from 'node_modules/formik/dist';
import CustomSwitch from 'src/components/ui/inputs/custom-switch';
import QuantityToggle from 'src/components/ui/buttons/quantity-toggle';
import Pressable from 'src/components/ui/base/pressable';
import { Trash } from 'node_modules/iconsax-react-native/src';
import CircledIcon from 'src/components/ui/circled-icon';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import UpdateOptionsQuantityModal from './update-option-quantity-modal';

interface UUpdateQtyStepProps {
  selected: BulkUpdateFormItem[];
  currency: string;
  batchData: {
    batchStart: number;
    batchEnd: number;
  };
  handleFormUpdate: (id: string, data: BulkUpdateFormItem) => void;
}

const UpdateQtyStep = ({ currency, selected, batchData, handleFormUpdate }: UUpdateQtyStepProps) => {
  const [activeItemIndex, setActiveItemIndex] = useState<number>(null);
  const { modals, toggleModal } = useModals(['selectProducts', 'updateOptionsQuantityModal']);

  const updateItem = (value: number, item: BulkUpdateFormItem) => {
    const quantity = value;
    const itemUpdate = { ...item, quantity };
    handleFormUpdate(item.id, itemUpdate);
  };

  const openOptionsModal = (index: number) => {
    setActiveItemIndex(index);
    toggleModal('updateOptionsQuantityModal');
  };

  const updateIsAlwaysAvailable = (value: boolean, item: BulkUpdateFormItem) => {
    const itemUpdate: BulkUpdateFormItem = { ...item, is_always_available: value };
    handleFormUpdate(item.id, itemUpdate);
  };

  const deleteItem = (i: number) => {
    // const selectedCopy = form;
    // selectedCopy.slice(i, 1);
    // setForm(selectedCopy);
  };

  return (
    <View>
      <View>
        <BaseText type="heading">Update Price Manually</BaseText>
        <View className="mt-20" style={{ rowGap: hp(15) }}>
          {selected.map(
            (p, i) =>
              i >= batchData.batchStart &&
              i < batchData.batchEnd && (
                <View className="bg-grey-bgOne rounded-12" key={i}>
                  <Row className="justify-start py-8 px-12">
                    <CustomImage
                      imageProps={{ source: { uri: p?.image }, contentFit: 'cover' }}
                      className="h-40 w-40 rounded-8"
                    />
                    <View className="flex-1">
                      <Row className="justify-start py-8 px-12">
                        <BaseText weight="medium" classes="text-black-secondary ml-10 mr-10">
                          {p.name}
                        </BaseText>
                        {p?.variants?.options?.length > 0 && (
                          <StatusPill
                            statusType={StatusType.DEFAULT}
                            title={`${p?.variants?.options?.length} options`}
                          />
                        )}
                      </Row>
                    </View>
                    <Pressable onPress={() => deleteItem(i)}>
                      <CircledIcon>
                        <Trash color={colors.accentRed.main} size={wp(18)} />
                      </CircledIcon>
                    </Pressable>
                  </Row>
                  <View className="bg-white rounded-12 p-15 border border-grey-border">
                    <Row>
                      <Row className="justify-start">
                        <BaseText classes="text-black-placeholder mr-10">Always in stock</BaseText>
                        <CustomSwitch
                          value={p?.is_always_available}
                          onValueChange={v => updateIsAlwaysAvailable(v, p)}
                        />
                      </Row>

                      {p.is_always_available == false && p.variants?.options?.length > 1 && (
                        <WhiteCardBtn
                          icon={<ArrowUpRight size={wp(14)} currentColor={colors.primary.main} strokeWidth={2} />}
                          className="py-0 px-0"
                          onPress={() => openOptionsModal(i)}>
                          {p.variants?.options?.length} Available
                        </WhiteCardBtn>
                      )}
                      {p.variants?.options?.length > 1 === false && p.is_always_available === false && (
                        <QuantityToggle
                          disabled={p.is_always_available && p.variants?.options?.length > 1 === false}
                          quantity={p?.quantity ?? 10}
                          showDelete={false}
                          onPressAdd={() => updateItem(p.quantity + 1, p)}
                          onPressMinus={() => updateItem(p.quantity - 1, p)}
                        />
                      )}
                    </Row>
                  </View>
                </View>
              ),
          )}
        </View>
      </View>
      <UpdateOptionsQuantityModal
        item={selected[activeItemIndex] ?? undefined}
        {...{ currency }}
        update={d => handleFormUpdate(selected[activeItemIndex]?.id, d)}
        isVisible={modals.updateOptionsQuantityModal}
        closeModal={() => toggleModal('updateOptionsQuantityModal', false)}
      />
    </View>
  );
};

export default UpdateQtyStep;
