import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { ActivityIndicator, Dimensions, StyleSheet, View } from 'react-native';
import { BaseText, Row } from 'src/components/ui';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import { IVideo } from 'src/@types/utils';
import * as VideoThumbnails from 'expo-video-thumbnails';
import CustomImage from 'src/components/ui/others/custom-image';
import colors from 'src/theme/colors';
import { hp, wp } from 'src/assets/utils/js';
import { useVideoPlayer, VideoView } from 'expo-video';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import Animated, { useAnimatedStyle, useSharedValue, runOnJS, interpolate, clamp } from 'react-native-reanimated';
import { useVideoMeta } from 'src/hooks/use-video-metadata';

interface SelectThumbnailStepProps {
  video: IVideo;
  currentTime: number;
  setCurrentTime: React.Dispatch<React.SetStateAction<number>>;
}

const SelectThumbnailStep: React.FC<SelectThumbnailStepProps> = ({ video, currentTime, setCurrentTime }) => {
  const [thumbnails, setThumbnails] = useState<{ uri: string; time: number }[]>([]);
  // const [currentTime, setCurrentTime] = useState(0);
  const [loading, setLoading] = useState(true);

  const { width } = Dimensions.get('window');
  const trackWidth = width - wp(40); // Total track width minus margins
  const selectorWidth = wp(15); // Width of the selector handle

  const videoSource = video?.src;
  const player = useVideoPlayer(videoSource);
  const duration = video?.duration || 0;

  const { duration: playerDuration, loading: metadataLoading } = useVideoMeta(videoSource);

  const videoDuration = useMemo(() => {
    return duration ? videoDuration : playerDuration * 1000;
  }, [playerDuration, duration]);

  // Shared values for animation
  const selectorPosition = useSharedValue(
    currentTime ? (currentTime / videoDuration) * (trackWidth - selectorWidth) : 0,
  );
  const isDragging = useSharedValue(false);
  const trackProgress = useSharedValue(0);
  const initialTouchPosition = useSharedValue(0);

  useEffect(() => {
    if (video?.src) {
      generateThumbnails();
    }
  }, [video]);

  // Update video player time based on selector position
  const updateVideoTime = useCallback(
    (position: number) => {
      if (videoDuration > 0) {
        const timeRatio = position / (trackWidth - selectorWidth);
        const newTime = timeRatio * videoDuration;
        setCurrentTime(newTime);

        // Seek video to new position
        if (player) {
          player.currentTime = newTime / 1000; // Convert to seconds
        }
      }
    },
    [trackWidth, selectorWidth, videoDuration, player],
  );

  // Update selector position based on video time
  const updateSelectorFromTime = useCallback(
    (time: number) => {
      if (videoDuration > 0) {
        const timeRatio = time / videoDuration;
        const newPosition = timeRatio * (trackWidth - selectorWidth);
        selectorPosition.value = clamp(newPosition, 0, trackWidth - selectorWidth);
        trackProgress.value = timeRatio;
      }
    },
    [trackWidth, selectorWidth, videoDuration],
  );

  // Listen to video time updates
  useEffect(() => {
    if (player) {
      const interval = setInterval(() => {
        if (!isDragging.value && player.currentTime) {
          const currentTimeMs = player.currentTime * 1000;
          setCurrentTime(currentTimeMs);
          runOnJS(updateSelectorFromTime)(currentTimeMs);
        }
      }, 100);

      return () => clearInterval(interval);
    }
  }, [player, updateSelectorFromTime]);

  const generateThumbnails = useCallback(async () => {
    setLoading(true);
    try {
      const duration = videoDuration || 0;
      const thumbnailCount = Math.max(6, Math.min(12, Math.floor(duration / 1000)));
      const thumbnailPromises = [];

      for (let i = 0; i < thumbnailCount; i++) {
        const time = (i * duration) / thumbnailCount;
        const thumbnailPromise = VideoThumbnails.getThumbnailAsync(video.src, {
          time: Math.ceil(time),
          quality: 0.7,
        });
        thumbnailPromises.push(thumbnailPromise);
      }

      const thumbnailResults = await Promise.all(thumbnailPromises);
      const thumbnailData = thumbnailResults.map((result, index) => ({
        uri: result.uri,
        time: (index * duration) / thumbnailCount,
      }));

      setThumbnails(thumbnailData);
    } catch (error) {
      console.error('Error generating thumbnails:', error);
    } finally {
      setLoading(false);
    }
  }, [videoDuration]);

  const formatTime = (milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Gesture handler for scrubbing
  const scrubGesture = Gesture.Pan()
    .onStart(event => {
      isDragging.value = true;
      // Store the initial touch position relative to the track
      initialTouchPosition.value = event.x - selectorPosition.value;
    })
    .onUpdate(event => {
      // Calculate new position based on absolute touch position
      const newPosition = clamp(event.x - initialTouchPosition.value, 0, trackWidth - selectorWidth);

      selectorPosition.value = newPosition;

      // Update progress
      const progress = newPosition / (trackWidth - selectorWidth);
      trackProgress.value = progress;

      // Update video time
      runOnJS(updateVideoTime)(newPosition);
    })
    .onEnd(() => {
      isDragging.value = false;
    });

  // Animated styles
  const selectorAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: selectorPosition.value },
      // { scale: isDragging.value ? 1.2 : 1 }
    ],
    opacity: isDragging.value ? 0.9 : 1,
  }));

  const progressBarStyle = useAnimatedStyle(() => ({
    width: interpolate(trackProgress.value, [0, 1], [0, trackWidth]),
  }));

  const timeIndicatorStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: selectorPosition.value - 20 }],
    opacity: isDragging.value ? 1 : 0,
  }));

  if (loading) {
    return (
      <BottomSheetView style={{ flex: 1 }} enableFooterMarginAdjustment>
        <View className="flex-1 m-20 border border-grey-border rounded-12">
          <View className="flex-1 items-center justify-center">
            <CustomImage imageProps={{ source: require('@/assets/gif/loader.gif') }} className="w-40 h-40 rounded-8" />
          </View>
        </View>
      </BottomSheetView>
    );
  }

  return (
    <BottomSheetView style={{ flex: 1 }} enableFooterMarginAdjustment>
      {/* Video Player */}
      <View className="flex-1 m-20 border border-grey-border rounded-12">
        <View className="flex-1 rounded-12 overflow-hidden bg-grey-bgOne">
          <VideoView
            player={player}
            style={{ flex: 1 }}
            // contentFit={'cover'}
            nativeControls={false}
            allowsFullscreen={false}
            allowsPictureInPicture={false}
          />
        </View>
      </View>

      {/* Time Display */}
      {/* <View className="mx-20 mb-2">
        <Row className="justify-between items-center">
          <BaseText className="text-sm text-grey-textSecondary">{formatTime(currentTime)}</BaseText>
          <BaseText className="text-sm text-grey-textSecondary">{formatTime(videoDuration)}</BaseText>
        </Row>
      </View> */}

      {/* Custom Video Track */}
      <View className="mt-2 mx-20 rounded-8 overflow-hidden">
        {loading ? (
          <View className="h-40 bg-grey-bgTwo rounded-12 flex items-center justify-center">
            <ActivityIndicator size="small" color={colors.primary.main} />
          </View>
        ) : (
          <View style={{ height: hp(40) }}>
            {/* Thumbnail Track */}
            <Row className="h-full relative">
              {thumbnails.map((thumbnail, index) => (
                <CustomImage
                  key={index}
                  imageProps={{
                    source: { uri: thumbnail.uri },
                    contentFit: 'cover',
                  }}
                  className="h-full flex-1"
                />
              ))}

              {/* Progress Overlay */}
              <Animated.View
                style={[
                  progressBarStyle,
                  {
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    height: '100%',
                    backgroundColor: 'rgba(0, 122, 255, 0.3)',
                  },
                ]}
              />

              {/* Track Border */}
              <View
                style={[
                  StyleSheet.absoluteFill,
                  {
                    borderWidth: 2,
                    borderColor: colors.primary.pastel,
                    borderRadius: 4,
                  },
                ]}
              />
            </Row>

            {/* Gesture Handler Container */}
            <View style={StyleSheet.absoluteFill}>
              <GestureHandlerRootView style={{ flex: 1 }}>
                <GestureDetector gesture={scrubGesture}>
                  <View style={{ flex: 1 }}>
                    {/* Selector Handle */}
                    <Animated.View
                      style={[
                        selectorAnimatedStyle,
                        {
                          position: 'absolute',
                          width: selectorWidth,
                          height: '100%',
                          justifyContent: 'center',
                          alignItems: 'center',
                          borderRadius: 2,
                          borderWidth: 2,
                          borderColor: colors.primary.main,
                          // backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        },
                      ]}>
                      <View
                        style={{
                          width: wp(3),
                          height: hp(6),
                          backgroundColor: colors.primary.main,
                          borderRadius: wp(1.5),
                        }}
                      />
                    </Animated.View>

                    {/* Time Indicator (shows during dragging) */}
                    <Animated.View
                      style={[
                        timeIndicatorStyle,
                        {
                          position: 'absolute',
                          width: wp(40),
                          height: '100%',
                          backgroundColor: 'rgba(0, 0, 0, 0.8)',
                          borderRadius: 4,
                          justifyContent: 'center',
                          alignItems: 'center',
                        },
                      ]}>
                      <BaseText className="text-xs text-white font-medium">{formatTime(currentTime)}</BaseText>
                    </Animated.View>
                  </View>
                </GestureDetector>
              </GestureHandlerRootView>
            </View>
          </View>
        )}
      </View>

      {/* Instructions */}
      <View className="p-20">
        <BaseText fontSize={12} classes="text-grey-textSecondary text-center">
          Drag the handle to scrub through your video
        </BaseText>
      </View>
    </BottomSheetView>
  );
};

export default SelectThumbnailStep;
