import { DocumentUpload, PlayCircle, Trash } from 'iconsax-react-native/src';
import { View } from 'react-native';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import colors from 'src/theme/colors';
import { wp } from 'src/assets/utils/js';
import Separator from 'src/components/ui/others/separator';
import Button, { ButtonSize, ButtonVariant } from 'src/components/ui/buttons/button';
import { IMedia, IVideo } from 'src/@types/utils';
import Pressable from 'src/components/ui/base/pressable';
import CustomImage from 'src/components/ui/others/custom-image';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import { useEffect } from 'react';

interface VideoCardProps {
  video: IVideo;
  isHighlight?: boolean;
  onPressViewVideo?: (video: IVideo) => void;
  onPressUploadButton?: VoidFunction;
}

const VideoCard = ({ video, isHighlight, onPressViewVideo, onPressUploadButton }: VideoCardProps) => {
  const uploaded = video?.url ? true : false;

  const progressWidth = useSharedValue(0);
  useEffect(() => {
    console.log('video?.uploadProgress: ', video?.uploadProgress);
    console.log('video?.isUploading: ', video?.isUploading);
    progressWidth.value = withTiming(video?.uploadProgress, { duration: 200 });
  }, [video?.uploadProgress]);

  const isUploading = video?.isUploading;

  // console.log(video);

  const descriptionText = isHighlight
    ? '0 Products'
    : `${video.fileSize > 0 ? (video.fileSize > 1024 * 1024 ? (video.fileSize / (1024 * 1024)).toFixed(2) + ' MB ' : (video.fileSize / 1024).toFixed(2) + ' KB' + ` • ${formatTime(video?.duration)}`) : ''}`;

  const progressStyle = useAnimatedStyle(() => {
    return {
      width: `${progressWidth.value}%`,
    };
  });

  return (
    <View className="bg-grey-bgOne rounded-12 px-12 py-8">
      <Row style={{ gap: wp(10) }}>
        {video?.meta?.thumbnail?.url ? (
          <CustomImage imageProps={{ source: { uri: video?.meta.thumbnail?.url } }} className="h-50 w-50  rounded-5" />
        ) : (
          <CircledIcon className="h-50 w-50 rounded-[6px] p-10 bg-grey-extraLight">
            <DocumentUpload size={wp(26)} variant="Bold" color={colors.black.muted} />
          </CircledIcon>
        )}
        <View className="flex-1">
          <Row style={{ gap: wp(10) }}>
            <View className="flex-1">
              <BaseText weight="medium" classes="text-black-secondary" numberOfLines={1}>
                {video?.name ?? ''}
              </BaseText>
            </View>
            <Pressable onPress={() => onPressViewVideo(video)}>
              <CircledIcon className="p-6 bg-white">
                <PlayCircle size={wp(18)} color={colors.black.muted} />
              </CircledIcon>
            </Pressable>
            <CircledIcon className="p-6 bg-white">
              <Trash size={wp(18)} color={colors.accentRed.main} />
            </CircledIcon>
          </Row>
          <Row disableSpread>
            <BaseText fontSize={12} classes="text-black-placeholder">
              {descriptionText}
            </BaseText>
            {(uploaded || isUploading) && (
              <>
                <View className="w-3 h-3 rounded-full bg-black-muted mx-6" />
                <BaseText fontSize={12} classes="text-accentGreen-main">
                  {video?.isUploading ? 'Uploading' : 'Upload Complete'}
                </BaseText>
              </>
            )}
          </Row>
          {isUploading && (
            <Animated.View style={progressStyle} className="h-5 bg-accentGreen-main rounded-r-full mt-5" />
          )}
        </View>
      </Row>
      <Separator className="my-10 mx-0" />
      <Row>
        <Button
          text="Upload Video"
          variant={ButtonVariant.LIGHT}
          size={ButtonSize.SMALL}
          onPress={onPressUploadButton}
        />
        {isHighlight && <Button text="Add Product" variant={ButtonVariant.LIGHT} size={ButtonSize.SMALL} />}
      </Row>
    </View>
  );
};

export default VideoCard;

// Utility function to format time from milliseconds
const formatTime = (seconds: number): string => {
  if (isNaN(seconds)) {
    return '';
  }
  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${minutes} min ${secs < 10 ? '0' : ''}${secs.toFixed(0)} secs`;
};
