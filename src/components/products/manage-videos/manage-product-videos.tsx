import { Product } from '../create-products/types';
import { View } from 'react-native';
import { showLoader, wp } from 'src/assets/utils/js';
import AddVideo from '../create-products/add-video';
import VideoCard from './video-card';
import { memo, useState, useCallback, useMemo, useEffect, ReactNode } from 'react';
import { AppMediaType, Image, IVideo } from 'src/@types/utils';
import useModals from 'src/hooks/use-modals';
import ViewVideoModal from './view-video-modal';
import VideoEditorModal, { SelectedTrimData } from './video-editor-modal';
import { useVideoProcessingQueue, QueueItem } from 'src/hooks/use-video-processing-queue';
import { MediaType } from 'catlog-shared';
import * as VideoThumbnails from 'expo-video-thumbnails';
import { pickMultipleMedia } from 'src/assets/utils/js/pick-multiple-images';

interface Props {
  // product: Product;
  videos: IVideo[];
  images: Image[];
  thumbnail?: number;
  saveVideos: (videos: IVideo[]) => void;
  saveMedias: (medias: AppMediaType[]) => void;
  removeProductVideo: (index: number) => void;
}

const ManageProductVideos: React.FC<Props> = ({
  videos,
  images,
  customAddVideoButton,
  saveVideos,
  saveMedias,
  removeProductVideo,
}) => {
  const { modals, toggleModal } = useModals(['viewVideo', 'editVideo']);
  const [video, setVideo] = useState<IVideo>(null);

  const [infoMessage, setInfoMessage] = useState(null);

  const productMedias: AppMediaType[] = useMemo(() => {
    const imgMedias = (images ?? [])?.map(i => ({ ...i, lastModified: 0, file: null, type: MediaType.IMAGE }));
    const videoMedias = (videos ?? [])?.map(i => ({ ...i, type: MediaType.VIDEO }));
    return [...imgMedias, ...videoMedias];
  }, [videos, images]);

  useEffect(() => {
    if (infoMessage) {
      showLoader(infoMessage, false, true);
    }
  }, [infoMessage]);

  // Callback to update video in your local state when processing completes
  const updateVideoInQueue = useCallback(
    (queueItem: QueueItem) => {
      // Update the video in your product.videos array
      const updatedVideos = videos.map(v => {
        if (v.name === queueItem.name || v.key === queueItem.key) {
          return {
            ...v,
            url: queueItem.processedVideoUrl,
            src: queueItem.src,
            thumbnail: queueItem.processedThumbnailUrl,
            isUploading: queueItem.isUploading,
            uploadProgress: queueItem.uploadProgress,
            error: queueItem.status === 'failed',
            fileSize: queueItem.fileSize,
            meta: {
              ...v.meta,
              thumbnail: {
                ...v.meta?.thumbnail,
                url: queueItem.processedThumbnailUrl,
                src: v.meta?.thumbnail?.src,
                isUploading: queueItem.status === 'processing',
                uploadProgress: queueItem.overallProgress,
                error: queueItem.status === 'failed',
              },
            },
          };
        }
        return v;
      });

      saveVideos(updatedVideos);
    },
    [videos, saveVideos],
  );

  const { addToQueue, removeFromQueue, retryItem, getCurrentStepName } = useVideoProcessingQueue(updateVideoInQueue);

  const removeVideoProgress = (index: number, taskId: string) => {
    removeFromQueue(taskId);
    removeProductVideo?.(index);
  };

  const handleViewVideo = (video: IVideo) => {
    setVideo(video);
    toggleModal('viewVideo', true);
  };

  const onPressUploadVideo = (video: IVideo) => {
    setVideo(video);
    toggleModal('editVideo', true);
  };

  // Complete the editing process by adding to processing queue
  const onCompleteEditing = async (result: {
    video: IVideo;
    thumbnailTimestamp: number;
    trimData?: SelectedTrimData;
  }) => {
    const { video: editedVideo, trimData } = result;

    const generateThumbnail = async () => {
      try {
        const thumbnail = await VideoThumbnails.getThumbnailAsync(video.src, {
          time: Math.ceil(result.thumbnailTimestamp),
          quality: 0.7,
        });

        return thumbnail.uri;
      } catch (error) {
        console.error('Error generating thumbnails:', error);
      }
    };

    const thumbnailUrl = await generateThumbnail();

    // Create queue item for processing
    const queueItem: Omit<
      QueueItem,
      'queueId' | 'status' | 'currentStep' | 'stepProgress' | 'overallProgress' | 'retryCount'
    > = {
      type: MediaType.VIDEO,
      src: editedVideo.src,
      name: editedVideo.name,
      lastModified: Date.now(),
      file: null,
      key: editedVideo.key || editedVideo.name,

      // Trimming parameters
      startTimeMs: trimData?.startTime || 0,
      endTimeMs: trimData?.endTime || 0,

      // Video metadata
      meta: {
        id: editedVideo.mediaHash || editedVideo.name,
        thumbnail: {
          src: thumbnailUrl,
          name: `${editedVideo.name}_thumbnail`,
          lastModified: Date.now(),
          file: null,
          isUploading: false,
          uploadProgress: 0,
          url: '',
          error: false,
          key: `${editedVideo.name}_thumbnail`,
        },
      },
    };

    console.log(JSON.stringify(queueItem));

    // Add to processing queue
    addToQueue(queueItem);

    // Close modal
    toggleModal('editVideo', false);

    // Optional: Show success message or update UI to indicate processing started
    console.log('Video added to processing queue:', editedVideo.name);
  };

  const handlePickVideo = async () => {
    pickMultipleMedia(productMedias, saveMedias, false, {}, setInfoMessage);
  };

  // console.log('product?.videos: ', product?.videos)

  return (
    <View className="mt-20">
      <View style={{ gap: wp(10) }}>
        {videos?.map((video, idx) => (
          <VideoCard
            key={idx}
            video={video}
            onPressViewVideo={handleViewVideo}
            onPressUploadButton={() => onPressUploadVideo(video)}
            removeVideoProgress={() => removeVideoProgress(idx, video.key)}
          />
        ))}
      </View>

      <AddVideo handlePickVideo={handlePickVideo} />

      {modals.viewVideo && (
        <ViewVideoModal isVisible={modals.viewVideo} closeModal={() => toggleModal('viewVideo', false)} video={video} />
      )}
      {modals.editVideo && (
        <VideoEditorModal
          isVisible={modals.editVideo}
          closeModal={() => toggleModal('editVideo', false)}
          video={video}
          onComplete={onCompleteEditing}
        />
      )}
    </View>
  );
};

export default memo(ManageProductVideos);
