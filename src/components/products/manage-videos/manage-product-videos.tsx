import { Product } from '../create-products/types';
import { ActivityIndicator, View } from 'react-native';
import { wp } from 'src/assets/utils/js';
import AddVideo from '../create-products/add-video';
import VideoCard from './video-card';
import { memo, useState, useCallback } from 'react';
import SheetModal, { SheetModalProps } from 'src/components/ui/modals/sheet-modal';
import { IVideo } from 'src/@types/utils';
import { useVideoPlayer, VideoView } from 'node_modules/expo-video/build';
import colors from 'src/theme/colors';
import useModals from 'src/hooks/use-modals';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import CircledIcon from 'src/components/ui/circled-icon';
import { Pause, Play } from 'node_modules/iconsax-react-native/src';
import { BaseText, Row } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import { useEvent } from 'expo';
import ViewVideoModal from './view-video-modal';
import TrimVideoModal from './trim-video-modal';
import VideoEditorModal, { SelectedTrimData } from './video-editor-modal';
import { useVideoProcessingQueue, QueueItem } from 'src/hooks/use-video-processing-queue';
import { MediaType } from 'catlog-shared';
import * as VideoThumbnails from 'expo-video-thumbnails';

interface Props {
  product: Product;
  thumbnail?: number;
  saveVideos: (videos: IVideo[]) => void;
}

const ManageProductVideos: React.FC<Props> = ({ product, saveVideos }) => {
  const { modals, toggleModal } = useModals(['viewVideo', 'editVideo']);
  const [video, setVideo] = useState<IVideo>(null);

  // Callback to update video in your local state when processing completes
  const updateVideoInQueue = useCallback(
    (queueItem: QueueItem) => {
      // Update the video in your product.videos array
      const updatedVideos = product.videos.map(v => {
        if (v.name === queueItem.name || v.key === queueItem.key) {
          return {
            ...v,
            url: queueItem.processedVideoUrl,
            src: queueItem.src,
            thumbnail: queueItem.processedThumbnailUrl,
            isUploading: queueItem.isUploading,
            uploadProgress: queueItem.uploadProgress,
            error: queueItem.status === 'failed',
            fileSize: queueItem.fileSize,
            meta: {
              ...v.meta,
              thumbnail: {
                ...v.meta?.thumbnail,
                url: queueItem.processedThumbnailUrl,
                src: v.meta?.thumbnail?.src,
                isUploading: queueItem.status === 'processing',
                uploadProgress: queueItem.overallProgress,
                error: queueItem.status === 'failed',
              },
            },
          };
        }
        return v;
      });

      saveVideos(updatedVideos);
    },
    [product.videos, saveVideos],
  );

  const {
    queue,
    completedItems,
    failedItems,
    isProcessing,
    currentItem,
    addToQueue,
    removeFromQueue,
    retryItem,
    getCurrentStepName,
    totalItems,
    pendingCount,
    processingCount,
    completedCount,
    failedCount,
  } = useVideoProcessingQueue(updateVideoInQueue);

  const handleViewVideo = (video: IVideo) => {
    setVideo(video);
    toggleModal('viewVideo', true);
  };

  const onPressUploadVideo = (video: IVideo) => {
    setVideo(video);
    toggleModal('editVideo', true);
  };

  // Complete the editing process by adding to processing queue
  const onCompleteEditing = async (result: { video: IVideo; thumbnailTimestamp: number; trimData?: SelectedTrimData }) => {
    const { video: editedVideo, trimData } = result;

    const generateThumbnail = async () => {
      try {
        const thumbnail = await VideoThumbnails.getThumbnailAsync(video.src, {
          time: Math.ceil(result.thumbnailTimestamp),
          quality: 0.7,
        });

        return thumbnail.uri;
      } catch (error) {
        console.error('Error generating thumbnails:', error);
      }
    };

    const thumbnailUrl = await generateThumbnail();

    // Create queue item for processing
    const queueItem: Omit<
      QueueItem,
      'queueId' | 'status' | 'currentStep' | 'stepProgress' | 'overallProgress' | 'retryCount'
    > = {
      type: MediaType.VIDEO,
      src: editedVideo.src,
      name: editedVideo.name,
      lastModified: Date.now(),
      file: null,
      key: editedVideo.key || editedVideo.name,

      // Trimming parameters
      startTimeMs: trimData?.startTime || 0,
      endTimeMs: trimData?.endTime || 0,

      // Video metadata
      meta: {
        id: editedVideo.mediaHash || editedVideo.name,
        thumbnail: {
          src:  thumbnailUrl,
          name: `${editedVideo.name}_thumbnail`,
          lastModified: Date.now(),
          file: null,
          isUploading: false,
          uploadProgress: 0,
          url: '',
          error: false,
          key: `${editedVideo.name}_thumbnail`,
        },
      },
    };

    console.log(JSON.stringify(queueItem))

    // Add to processing queue
    addToQueue(queueItem);

    // Close modal
    toggleModal('editVideo', false);

    // Optional: Show success message or update UI to indicate processing started
    console.log('Video added to processing queue:', editedVideo.name);
  };

  return (
    <View className="mt-20">
      {/* Processing Queue Status - Optional UI to show queue status */}
      {(isProcessing || queue.length > 0) && (
        <View
          style={{ padding: wp(10), backgroundColor: colors.grey.muted, borderRadius: wp(8), marginBottom: wp(10) }}>
          <Row style={{ justifyContent: 'space-between', alignItems: 'center' }}>
            <View>
              <BaseText style={{ fontWeight: 'bold' }}>Processing Videos</BaseText>
              {currentItem && (
                <BaseText style={{ fontSize: 12, color: colors.grey.muted }}>
                  {getCurrentStepName(currentItem.currentStep)} - {currentItem.overallProgress}%
                </BaseText>
              )}
            </View>
            <View style={{ alignItems: 'flex-end' }}>
              <BaseText style={{ fontSize: 12 }}>
                {completedCount} completed, {pendingCount + processingCount} remaining
              </BaseText>
              {failedCount > 0 && (
                <BaseText style={{ fontSize: 12, color: colors.accentRed.main }}>{failedCount} failed</BaseText>
              )}
            </View>
          </Row>

          {currentItem && (
            <View style={{ marginTop: wp(5) }}>
              <View style={{ height: 4, backgroundColor: colors.grey.muted, borderRadius: 2 }}>
                <View
                  style={{
                    height: '100%',
                    // position: 'absolute',
                    backgroundColor: colors.primary.extraLight,
                    borderRadius: 2,
                    width: `${currentItem.overallProgress}%`,
                  }}
                />
              </View>
            </View>
          )}
        </View>
      )}

      <View style={{ gap: wp(10) }}>
        {product?.videos?.map((video, idx) => (
          <VideoCard
            key={idx}
            video={video}
            onPressViewVideo={handleViewVideo}
            onPressUploadButton={() => onPressUploadVideo(video)}
          />
        ))}
      </View>

      <AddVideo />

      {modals.viewVideo && (
        <ViewVideoModal isVisible={modals.viewVideo} closeModal={() => toggleModal('viewVideo', false)} video={video} />
      )}

      {modals.editVideo && (
        <VideoEditorModal
          isVisible={modals.editVideo}
          closeModal={() => toggleModal('editVideo', false)}
          video={video}
          onComplete={onCompleteEditing}
        />
      )}
    </View>
  );
};

export default memo(ManageProductVideos);
