import { CloseCircle, Copy, InfoCircle, TickCircle } from 'iconsax-react-native/src';
import { ActivityIndicator, Dimensions, StyleSheet, View } from 'react-native';
import { cx, delay, hideLoader, hp, showLoader, showSuccess, toCurrency, wp, Yup } from 'src/assets/utils/js';
import { BaseText, Row, SelectionPill } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import SheetModal, { SheetModalProps } from 'src/components/ui/modals/sheet-modal';
import SectionContainer from 'src/components/ui/section-container';
import colors from 'src/theme/colors';
import Input from 'src/components/ui/inputs/input';
import Button, { ButtonSize, ButtonVariant } from 'src/components/ui/buttons/button';
import { BottomSheetScrollView, BottomSheetView } from '@gorhom/bottom-sheet';
import { useApi } from 'src/hooks/use-api';
import { ADD_DOMAIN, CHECK_DOMAIN } from 'catlog-shared';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import useStatusbar from 'src/hooks/use-statusbar';
import Shimmer from 'src/components/ui/shimmer';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import { VideoView, useVideoPlayer } from 'expo-video';
import { useEvent } from 'expo';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  runOnJS,
  clamp,
  interpolate,
  withSpring,
  useDerivedValue,
} from 'react-native-reanimated';
import * as FileSystem from 'expo-file-system';
import CustomImage from 'src/components/ui/others/custom-image';
import * as VideoThumbnails from 'expo-video-thumbnails';
import { IVideo, TrimData } from 'src/@types/utils';

interface Props {
  closeModal: () => void;
  video: IVideo;
  onPressSave?: (video: Partial<TrimData>) => void;

  trimStartTime: number;
  trimEndTime: number;
  setTrimStartTime: React.Dispatch<React.SetStateAction<number>>;
  setTrimEndTime: React.Dispatch<React.SetStateAction<number>>;
}

export interface TrimVideoRef {
  getTrimData: () => { startTime: number; endTime: number; videoDuration: number };
  setTrimData: (timeData: { startTime: number; endTime: number }) => void;
  resetSelection: () => void;
  playVideo: () => void;
  pauseVideo: () => void;
}

const MIN_SELECTION_WIDTH = 80;

const TrimVideo = forwardRef<TrimVideoRef, Props>(
  ({ closeModal, video, onPressSave, trimStartTime, trimEndTime, setTrimStartTime, setTrimEndTime, ...props }, ref) => {
    const [thumbnails, setThumbnails] = useState([]);
    const [loading, setLoading] = useState(false);

    // const [trimStartTime, setTrimStartTime] = useState(0);
    // const [trimEndTime, setTrimEndTime] = useState(0);

    const { setStatusBarStyle } = useStatusbar();
    const [isLoading, setIsLoading] = useState(true);

    const videoSource = video?.src;
    const duration = video?.duration;

    // Reanimated shared values
    const selectionStart = useSharedValue(trimStartTime ? (trimStartTime / duration) * 100 : 0);
    const selectionWidth = useSharedValue(trimEndTime ? ((trimEndTime - trimStartTime) / duration) * 100 : 100);
    const thumbnailTrackWidth = useSharedValue(0);
    const isDraggingBox = useSharedValue(false);
    const isDraggingLeftHandle = useSharedValue(false);
    const isDraggingRightHandle = useSharedValue(false);

    // Store initial positions for gesture tracking
    const initialSelectionStart = useSharedValue(trimStartTime ? (trimStartTime / duration) * 100 : 0);
    const initialSelectionWidth = useSharedValue(trimEndTime ? ((trimEndTime - trimStartTime) / duration) * 100 : 100);

    const player = useVideoPlayer(videoSource);

    const thumbnailTrackWidthValue = useDerivedValue(() => {
      return thumbnailTrackWidth.value;
    });

    // Expose methods via ref
    useImperativeHandle(
      ref,
      () => ({
        getTrimData: () => ({
          startTime: trimStartTime,
          endTime: trimEndTime,
          videoDuration: duration,
        }),
        setTrimData: timeData => {
          setTrimStartTime(timeData.startTime);
          setTrimEndTime(timeData.endTime);
        },
        resetSelection: () => {
          if (thumbnailTrackWidthValue.value > 0) {
            const initialWidth = thumbnailTrackWidthValue.value * 0.3;
            const initialStart = thumbnailTrackWidthValue.value * 0.35;

            selectionStart.value = initialStart;
            selectionWidth.value = Math.max(initialWidth, MIN_SELECTION_WIDTH);

            updateTrimTimes();
          }
        },
        playVideo: () => {
          if (player) {
            player.play();
          }
        },
        pauseVideo: () => {
          if (player) {
            player.pause();
          }
        },
      }),
      [trimStartTime, trimEndTime, duration, thumbnailTrackWidthValue, player],
    );

    useEffect(() => {
      if (videoSource) {
        generateThumbnails();
      }
    }, [videoSource]);

    useEffect(() => {
      if (player) {
        player.addListener('statusChange', ({ status }) => {
          if (status === 'loading') setIsLoading(true);
          if (status === 'readyToPlay') setIsLoading(false);
        });

        return () => {
          player.removeAllListeners('statusChange');
        };
      }
    }, [player]);

    const { isPlaying } = useEvent(player, 'playingChange', { isPlaying: player.playing });

    const setTrackWidth = (trackWidth: number) => {
      thumbnailTrackWidth.value = trackWidth;

      const initialWidth = trackWidth * 0.3;
      const initialStart = trimStartTime ? (trimStartTime / duration) * 100 : trackWidth * 0.35;

      selectionStart.value = initialStart;
      selectionWidth.value = Math.max(initialWidth, MIN_SELECTION_WIDTH);

      updateTrimTimes();
    };

    const generateThumbnails = async () => {
      setLoading(true);
      try {
        // Calculate number of thumbnails (one every 2 seconds, minimum 10)
        const thumbnailInterval = 6000; // 2 seconds
        const thumbnailCount = Math.max(10, Math.ceil(duration / thumbnailInterval));

        const thumbnailPromises = [];

        for (let i = 0; i < thumbnailCount; i++) {
          const time = (i * duration) / thumbnailCount;

          const thumbnailPromise = VideoThumbnails.getThumbnailAsync(videoSource, {
            time: Math.ceil(time),
            quality: 0.5,
          });

          thumbnailPromises.push(thumbnailPromise);
        }

        const thumbnailResults = await Promise.all(thumbnailPromises);

        const thumbnailData = thumbnailResults.map((result, index) => ({
          uri: result.uri,
          time: (index * duration) / thumbnailCount,
          width: result.width,
          height: result.height,
        }));

        setThumbnails(thumbnailData);
      } catch (error) {
        console.error('Error generating thumbnails:', error);
      } finally {
        setLoading(false);
      }
    };

    const updateTrimTimes = () => {
      'worklet';
      if (thumbnailTrackWidth.value === 0 || !duration) return;

      // Calculate percentages with proper bounds checking
      const startPercentage = Math.max(0, Math.min(1, selectionStart.value / thumbnailTrackWidth.value));
      const endPercentage = Math.max(
        0,
        Math.min(1, (selectionStart.value + selectionWidth.value) / thumbnailTrackWidth.value),
      );

      // Calculate times and clamp to video duration
      const startTime = Math.max(0, Math.min(duration, startPercentage * duration));
      const endTime = Math.max(startTime, Math.min(duration, endPercentage * duration));

      // Ensure minimum duration and that end time doesn't exceed video duration
      const minDuration = 1000; // 1 second minimum
      const adjustedEndTime = Math.max(startTime + minDuration, Math.min(duration, endTime));
      const adjustedStartTime = Math.min(startTime, duration - minDuration);

      runOnJS(setTrimStartTime)(adjustedStartTime);
      runOnJS(setTrimEndTime)(adjustedEndTime);
    };

    // Left handle gesture (resize from left)
    const leftHandleGesture = Gesture.Pan()
      .onStart(() => {
        isDraggingLeftHandle.value = true;
        initialSelectionStart.value = selectionStart.value;
        initialSelectionWidth.value = selectionWidth.value;
      })
      .onUpdate(event => {
        const newStart = clamp(
          initialSelectionStart.value + event.translationX,
          0,
          initialSelectionStart.value + initialSelectionWidth.value - MIN_SELECTION_WIDTH,
        );
        const widthDelta = initialSelectionStart.value - newStart;

        selectionStart.value = newStart;
        selectionWidth.value = initialSelectionWidth.value + widthDelta;

        updateTrimTimes();
      })
      .onEnd(() => {
        isDraggingLeftHandle.value = false;
      });

    // Right handle gesture (resize from right)
    const rightHandleGesture = Gesture.Pan()
      .onStart(() => {
        isDraggingRightHandle.value = true;
        initialSelectionWidth.value = selectionWidth.value;
      })
      .onUpdate(event => {
        const newWidth = clamp(
          initialSelectionWidth.value + event.translationX,
          MIN_SELECTION_WIDTH,
          thumbnailTrackWidth.value - selectionStart.value,
        );

        selectionWidth.value = newWidth;
        updateTrimTimes();
      })
      .onEnd(() => {
        isDraggingRightHandle.value = false;
      });

    // Box drag gesture (move entire selection)
    const boxDragGesture = Gesture.Pan()
      .onStart(() => {
        isDraggingBox.value = true;
        initialSelectionStart.value = selectionStart.value;
      })
      .onUpdate(event => {
        const newStart = clamp(
          initialSelectionStart.value + event.translationX,
          0,
          thumbnailTrackWidth.value - selectionWidth.value,
        );

        selectionStart.value = newStart;
        updateTrimTimes();
      })
      .onEnd(() => {
        isDraggingBox.value = false;
        // Add a subtle spring animation when releasing
        selectionStart.value = withSpring(selectionStart.value);
      });

    // Animated styles
    const selectionBoxStyle = useAnimatedStyle(() => ({
      left: selectionStart.value,
      width: selectionWidth.value,
      opacity: isDraggingBox.value ? 0.8 : 1,
    }));

    const leftOverlayStyle = useAnimatedStyle(() => ({
      width: selectionStart.value,
    }));

    const rightOverlayStyle = useAnimatedStyle(() => ({
      width: thumbnailTrackWidth.value - (selectionStart.value + selectionWidth.value),
    }));

    const leftHandleStyle = useAnimatedStyle(() => ({
      opacity: isDraggingLeftHandle.value ? 0.8 : 1,
      transform: [{ scale: isDraggingLeftHandle.value ? 1.1 : 1 }],
    }));

    const rightHandleStyle = useAnimatedStyle(() => ({
      opacity: isDraggingRightHandle.value ? 0.8 : 1,
      transform: [{ scale: isDraggingRightHandle.value ? 1.1 : 1 }],
    }));

    const formatTime = (milliseconds: number) => {
      const seconds = Math.floor(milliseconds / 1000);
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    const trackWidthValue = useDerivedValue(() => {
      return thumbnailTrackWidth.value;
    });

    const markers = useMemo(() => generateTimeMarkers(duration, trackWidthValue), [trackWidthValue, duration]);

    const handleSave = () => {
      if (onPressSave) {
        onPressSave({
          startTime: trimStartTime,
          endTime: trimEndTime,
          videoDuration: duration,
        });
      }
    };

    if (loading) {
      return (
        <BottomSheetView style={{ flex: 1 }} enableFooterMarginAdjustment>
          <View className="flex-1 m-20 border border-grey-border rounded-12">
            <View className="flex-1 items-center justify-center">
              <CustomImage
                imageProps={{ source: require('@/assets/gif/loader.gif') }}
                className="w-40 h-40 rounded-8"
              />
            </View>
          </View>
        </BottomSheetView>
      );
    }

    return (
      <BottomSheetView style={{ flex: 1 }} enableFooterMarginAdjustment>
        <View className="flex-1 m-20 border border-grey-border rounded-12">
          <View className="rounded-12 overflow-hidden bg-grey-bgOne">
            {isLoading && (
              <View className="absolute top-0 right-0 h-full w-full flex items-center justify-center rounded-[10px] z-10">
                <ActivityIndicator size="large" color={colors.primary.main} />
              </View>
            )}
            <VideoView
              player={player}
              className="w-full h-full"
              contentFit={'cover'}
              nativeControls={true}
              allowsFullscreen={false}
              allowsPictureInPicture={false}
            />
          </View>
        </View>

        <View className="mb-20">
          <Row className="mx-20 mb-10">
            {markers.map((marker, index) => {
              return (
                <View key={index} style={{}}>
                  <BaseText
                    fontSize={10}
                    weight={marker.isStart || marker.isEnd ? 'bold' : 'regular'}
                    classes={`text-grey-textSecondary `}
                    style={{
                      // left: 10,
                      textAlign: 'center',
                    }}>
                    {formatTimeMarker(marker.time)}
                  </BaseText>
                </View>
              );
            })}
          </Row>

          <View className="mt-2 mx-20 rounded-12 overflow-hidden">
            {loading ? (
              <View className="h-40 bg-grey-bgTwo rounded-12 flex items-center justify-center">
                <BaseText className="text-xs text-grey-textSecondary mt-2">loading...</BaseText>
              </View>
            ) : (
              <View>
                <Row className="h-40" onLayout={event => setTrackWidth(event.nativeEvent.layout.width)}>
                  {thumbnails.map((thumbnail, index) => (
                    <CustomImage
                      key={index}
                      imageProps={{
                        source: { uri: thumbnail.uri },
                        contentFit: 'cover',
                      }}
                      className="h-full flex-1"
                    />
                  ))}
                </Row>

                <View style={StyleSheet.absoluteFill}>
                  {/* Left dim overlay */}
                  <Animated.View
                    style={[leftOverlayStyle]}
                    className="absolute top-0 bottom-0 left-0 bg-[#000000] opacity-60"
                  />

                  {/* Right dim overlay */}
                  <Animated.View
                    style={[rightOverlayStyle]}
                    className="absolute top-0 bottom-0 right-0 bg-[#000000] opacity-60"
                  />
                </View>

                {/* Selection Box Container */}
                <View style={StyleSheet.absoluteFill}>
                  <GestureHandlerRootView>
                    <GestureDetector gesture={boxDragGesture}>
                      <Animated.View style={[selectionBoxStyle]} className="absolute top-0 bottom-0">
                        {/* Selection Box Background */}
                        <View
                          className="absolute inset-0 border-2 rounded-sm"
                          style={{ borderColor: colors.primary.main }}>
                          {/* Top and bottom bars */}
                          <View
                            className="absolute top-0 left-0 right-0 h-1 rounded-t-sm"
                            style={{ backgroundColor: colors.primary.main }}
                          />
                          <View
                            className="absolute bottom-0 left-0 right-0 h-1 rounded-b-sm"
                            style={{ backgroundColor: colors.primary.main }}
                          />
                        </View>

                        {/* Left Handle */}
                        <GestureDetector gesture={leftHandleGesture}>
                          <Animated.View
                            style={[leftHandleStyle, { borderBottomLeftRadius: wp(4), borderTopLeftRadius: wp(4) }]}
                            className="absolute top-0 bottom-0 left-0 w-10 justify-center items-center bg-primary-main">
                            <View className="w-3 h-6 bg-white rounded-full" />
                          </Animated.View>
                        </GestureDetector>

                        {/* Right Handle */}
                        <GestureDetector gesture={rightHandleGesture}>
                          <Animated.View
                            style={[rightHandleStyle, { borderBottomRightRadius: wp(4), borderTopRightRadius: wp(4) }]}
                            className="absolute top-0 bottom-0 right-0 w-10 justify-center items-center bg-primary-main">
                            <View className="w-3 h-6 bg-white rounded-full" />
                          </Animated.View>
                        </GestureDetector>
                      </Animated.View>
                    </GestureDetector>
                  </GestureHandlerRootView>
                </View>
              </View>
            )}
          </View>

          <View className="mx-20 mt-10">
            <Row className="justify-between">
              <BaseText className="text-sm font-semibold text-grey-textSecondary">
                Start: {formatTime(trimStartTime)}
              </BaseText>
              <BaseText className="text-sm font-semibold text-grey-textSecondary">
                End: {formatTime(trimEndTime)}
              </BaseText>
            </Row>
          </View>
        </View>
      </BottomSheetView>
    );
  },
);

export default TrimVideo;

const generateTimeMarkers = (duration, trackWidth) => {
  if (!duration || !trackWidth || duration <= 0 || trackWidth <= 0) {
    return [];
  }

  // Determine appropriate time interval based on video duration
  const getTimeInterval = duration => {
    const durationInSeconds = duration / 1000;

    if (durationInSeconds <= 30) return 5000; // 5 seconds for videos ≤ 30s
    if (durationInSeconds <= 120) return 10000; // 10 seconds for videos ≤ 2min
    if (durationInSeconds <= 300) return 15000; // 15 seconds for videos ≤ 5min
    if (durationInSeconds <= 600) return 30000; // 30 seconds for videos ≤ 10min
    if (durationInSeconds <= 1800) return 60000; // 1 minute for videos ≤ 30min
    return 120000; // 2 minutes for longer videos
  };

  const interval = getTimeInterval(duration);
  const markers = [];

  // Always add marker at start (0:00)
  markers.push({
    time: 0,
    position: 0,
    isStart: true,
  });

  // Add intermediate markers
  for (let time = interval; time < duration; time += interval) {
    const position = (time / duration) * trackWidth;
    markers.push({
      time,
      position,
      isStart: false,
    });
  }

  // Always add marker at end
  if (duration > interval) {
    markers.push({
      time: duration,
      position: trackWidth,
      isStart: false,
      isEnd: true,
    });
  }

  return markers;
};

const formatTimeMarker = milliseconds => {
  const totalSeconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;

  if (minutes === 0) {
    return `${seconds}s`;
  }

  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};
