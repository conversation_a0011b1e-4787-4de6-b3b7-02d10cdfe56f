import { CouponItemInterface, GET_COUPON_ORDERS } from 'catlog-shared';
import {
  Box1,
  Calendar,
  DollarCircle,
  Layer,
  Moneys,
  PercentageCircle,
  Status,
  TicketDiscount,
} from 'iconsax-react-native/src';
import { View } from 'react-native';

import CouponOrdersModal from '../../coupon-orders-modal';
import ProductInfoRow from '../../product-info-row';

import { formatDate, toCurrency, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import { ButtonVariant, TextColor } from '@/components/ui/buttons/button';
import { ArrowUpRight } from '@/components/ui/icons';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import InfoRow from '@/components/ui/others/info-row';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import { useApi } from '@/hooks/use-api';
import useModals from '@/hooks/use-modals';
import colors from '@/theme/colors';

interface CouponDetailsModalProps extends Partial<BottomModalProps> {
  activeCoupon: CouponItemInterface;
  closeModal: VoidFunction;
  editCoupon: VoidFunction;
  deleteCoupon: VoidFunction;
}

const CouponDetailsModal = ({
  activeCoupon,
  deleteCoupon,
  closeModal,
  editCoupon,
  ...props
}: CouponDetailsModalProps) => {
  const { modals, toggleModal } = useModals(['ordersModal']);
  const getCouponOrdersRequest = useApi(
    {
      apiFunction: GET_COUPON_ORDERS,
      method: 'GET',
      key: 'get-coupon-orders',
      autoRequest: false,
      onSuccess(response) {
        console.log(response);
      },
    },
    {
      coupon_code: activeCoupon?.coupon_code,
    },
  );

  const handleGetCouponItems = async () => {
    await getCouponOrdersRequest.makeRequest({
      coupon_code: activeCoupon?.coupon_code,
    });
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      buttons={[
        { text: 'Delete Coupon', variant: ButtonVariant.LIGHT, textColor: TextColor.NEGATIVE, onPress: deleteCoupon },
        { text: 'Edit Coupon', onPress: editCoupon },
      ]}
      title="Coupon Details">
      <View className="px-20 border-t border-grey-border mt-15">
        <Row className="py-20">
          <View>
            <BaseText classes="text-black-muted">Coupon Code (Tap to copy)</BaseText>
            <BaseText fontSize={15} weight="bold" type="heading" classes="uppercase">
              {activeCoupon.coupon_code}
            </BaseText>
          </View>
          <CircledIcon iconBg="bg-accentRed-main">
            <TicketDiscount variant="Bold" size={wp(20)} color={colors.white} />
          </CircledIcon>
        </Row>
        <ProductInfoRow
          className="border-y border-grey-border py-15"
          leftItem={{
            icon: (
              <CircledIcon iconBg="bg-accentGreen-pastel">
                <PercentageCircle variant="Bold" size={wp(20)} color={colors.accentGreen.main} />
              </CircledIcon>
            ),
            value: activeCoupon?.percentage ? `${activeCoupon?.percentage}%` : '-',
            title: 'Percentage',
          }}
          rightItem={{
            icon: (
              <CircledIcon iconBg="bg-accentOrange-pastel">
                <DollarCircle variant="Bold" size={wp(15)} color={colors.accentOrange.main} />
              </CircledIcon>
            ),
            value: activeCoupon?.discount_cap ? `${toCurrency(activeCoupon?.discount_cap)}` : '-',
            title: 'Cap Amount',
          }}
        />
        <View className="mt-15">
          <InfoRow
            title="Status"
            icon={<Status size={wp(15)} color={colors.black.placeholder} />}
            valueElement={
              <StatusPill
                className="bg-grey-bgOne"
                statusType={activeCoupon?.active ? StatusType.SUCCESS : StatusType.DANGER}
                title={activeCoupon?.active ? 'ACTIVE' : 'EXPIRED'}
              />
            }
          />
          <InfoRow
            title="Expiry Date"
            icon={<Calendar size={wp(15)} color={colors.black.placeholder} />}
            value={activeCoupon?.end_date ? formatDate(activeCoupon?.end_date) : '-'}
          />
          <InfoRow
            title="Min. Purchase Amount"
            icon={<Moneys size={wp(15)} color={colors.black.placeholder} />}
            value={activeCoupon?.minimum_order_amount ? toCurrency(activeCoupon?.minimum_order_amount) : '-'}
          />
          <InfoRow
            title="Quantity Left"
            icon={<Layer size={wp(15)} color={colors.black.placeholder} />}
            value={`${activeCoupon?.quantity} left`}
          />
          <InfoRow
            title="Number of orders applied"
            icon={<Box1 size={wp(15)} color={colors.black.placeholder} />}
            valueElement={
              <WhiteCardBtn
                className="py-0 px-0 self-center"
                onPress={() => toggleModal('ordersModal')}
                icon={<ArrowUpRight size={wp(15)} strokeWidth={2} currentColor={colors.primary.main} />}>
                {activeCoupon?.orders?.length} Orders
              </WhiteCardBtn>
            }
          />
        </View>
      </View>
      <CouponOrdersModal
        orders={getCouponOrdersRequest?.response?.data || []}
        isLoading={getCouponOrdersRequest?.isLoading}
        onModalWillShow={handleGetCouponItems}
        isVisible={modals.ordersModal}
        productCardProps={{ listView: true }}
        closeModal={() => toggleModal('ordersModal', false)}
      />
    </BottomModal>
  );
};

export default CouponDetailsModal;
