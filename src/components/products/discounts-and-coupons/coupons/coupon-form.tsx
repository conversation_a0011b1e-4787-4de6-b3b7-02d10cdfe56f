import { ReactNode, useMemo } from 'react';
import { TouchableOpacity, View } from 'react-native';
import colors from '@/theme/colors';
import Pressable from '../../../ui/base/pressable';
import { BaseText } from '../../../ui';
import { Add, Calendar } from 'iconsax-react-native/src';
import { formatDate, getFieldvalues, hp, wp } from '@/assets/utils/js';
import Input from '../../../ui/inputs/input';
import SelectDropdown from '../../../ui/inputs/select-dropdown';
import CalendarModal from '../../../ui/modals/calendar-modal';
import * as Yup from 'yup';
import { FormikProps } from 'formik';
import useModals from '@/hooks/use-modals';
import Accordion from '@/components/ui/others/accordion';
import { CreateCouponParams, getRandString, UpdateCouponParams } from 'catlog-shared';

interface CouponFormProps {
  screen: CouponFormScreen;
  form: FormikProps<Omit<CreateCouponParams, 'id'>>;
}

export enum CouponFormScreen {
  CREATE_SCREEN = 'Create Screen',
  EDIT_SCREEN = 'Edit Screen',
}

const CouponForm = ({ screen, form, ...rest }: CouponFormProps) => {
  const { modals, toggleModal } = useModals(['calender']);

  const onSelectDurationOption = (value: string) => {
    form.setFieldValue('type', value);
  };

  const RightAccessory = () => (
    <View className="border-grey-bgOne rounded-full">
      <Calendar size={wp(16)} color={colors.grey.muted} />
    </View>
  );

  return (
    <View>
      <Input label={'Coupon Code'} {...getFieldvalues('coupon_code', form)} />
      {screen === CouponFormScreen.CREATE_SCREEN && suggestedCode && (
        <Pressable
          className="flex-row items-center mt-10"
          onPress={() => form.setFieldValue('coupon_code', suggestedCode)}>
          <BaseText weight="medium" classes="text-black-secondary">
            Suggested Code
          </BaseText>
          <View className="bg-primary-pastel py-4 px-6 rounded-4 ml-5">
            <BaseText weight="medium" classes="text-primary-main">
              {suggestedCode}
            </BaseText>
          </View>
        </Pressable>
      )}
      <View>
        <SelectDropdown
          selectedItem={form.values.type}
          onPressItem={onSelectDurationOption}
          items={couponTypes}
          label={'How should discounts be calculated'}
          showLabel={false}
          containerClasses="mt-15"
        />
        {form.errors.type && (
          <View className="mt-5">
            <BaseText fontSize={10} weight={'medium'} classes="text-accentRed-main">
              {form.errors.type}
            </BaseText>
          </View>
        )}
      </View>
      {form.values.type === 'percentage' && (
        <Input label={'Discount Percentage'} containerClasses="mt-15" {...getFieldvalues('percentage', form)} />
      )}
      {form.values.type === 'fixed' && (
        <Input label={'Discount Amount'} containerClasses="mt-15" {...getFieldvalues('discount_amount', form)} />
      )}
      {form.values.type === 'percentage' && (
        <Accordion title={'Maximum amount allowed for this coupon'}>
          <Input label={'Cap for this percent'} {...getFieldvalues('discount_cap', form)} />
        </Accordion>
      )}
      <Input
        label={'Coupon Quantity'}
        containerClasses="mt-15"
        keyboardType="number-pad"
        {...getFieldvalues('quantity', form)}
      />
      <Accordion title={'Maximum amount required for this coupon'}>
        <Input label={'Minimum order value (NGN)'} {...getFieldvalues('minimum_order_amount', form)} />
      </Accordion>
      <Pressable onPress={() => toggleModal('calender')} className="mt-15">
        <Input
          label={'Coupon Expiry'}
          editable={false}
          value={form.values.end_date ? formatDate(form.values.end_date, 'D MMM YYYY') : null}
          onPressIn={() => toggleModal('calender')}
          rightAccessory={<RightAccessory />}
        />
      </Pressable>
      {form.errors.end_date && (
        <View className="mt-5">
          <BaseText fontSize={10} weight={'medium'} classes="text-accentRed-main">
            {form.errors.end_date}
          </BaseText>
        </View>
      )}
      <CalendarModal
        isVisible={modals.calender}
        closeModal={() => toggleModal('calender', false)}
        startDate={new Date(form.values?.end_date)}
        singleSelectMode
        onDateChange={(date, type) => {
          form.setFieldValue('end_date', date);
        }}
      />
    </View>
  );
};

const couponTypes = [
  {
    value: 'percentage',
    label: 'By Percentage',
  },
  {
    value: 'fixed',
    label: 'Fixed Amount',
  },
];

const suggestedCode = getRandString(10).toUpperCase();

export const couponValidationSchema = Yup.object().shape({
  coupon_code: Yup.string().required('Coupon code is required'),
  type: Yup.string().required('Select how discount should be calculated'),
  end_date: Yup.string().required('Expiry date is required'),
  percentage: Yup.number().min(1, 'Percentage must be greater 0').max(100, 'Percentage must be at most 100'),
  discount_amount: Yup.string().when('type', {
    is: 'fixed',
    then: schema => schema.required('Discount amount is required'),
    otherwise: schema => schema.nullable(),
  }),
  discount_cap: Yup.number()
    .nullable()
    .transform(value => (isNaN(value) || value === null || value === '' ? null : value))
    .test('min', 'Discount cap must be greater than 0', value => {
      if (value === null || value === undefined) {
        return true;
      }
      return value >= 1;
    }),
  quantity: Yup.number().min(1, 'Quantity must be greater 0').required('Quantity is required'),
});

export default CouponForm;
