import useImageUploads from 'src/hooks/use-file-uploads';
import { Product } from './types';
import { AppMediaType, Image as ImageType } from 'src/@types/utils';
import { Image, View } from 'react-native';
import CircularProgress from 'react-native-circular-progress-indicator';
import colors from 'src/theme/colors';
import { showLoader, wp } from 'src/assets/utils/js';
import Pressable from '@/components/ui/base/pressable';
import { Add } from 'iconsax-react-native/src';
import classNames from 'classnames';
import { Close } from '@/components/ui/icons';
import { pickMultipleImages, pickMultipleMedia } from 'src/assets/utils/js/pick-multiple-images';
import { useEffect, useMemo, useState } from 'react';
import { Media, MediaType } from 'catlog-shared';
import useComputeThumbnails from 'src/hooks/use-compute-thumbnails';

interface Props {
  product: Product;
  thumbnail?: number;
  saveImages: (images: ImageType[] | ((currentImages: ImageType[]) => ImageType[])) => void;
  saveMedias: (medias: AppMediaType[]) => void;
  changeThumbnail: (id: number) => void;
  removePickedImage: (id: number) => void;
  selectPlaceholder?: VoidFunction;
}

const PickProductImages: React.FC<Props> = ({
  product,
  saveImages,
  saveMedias,
  changeThumbnail,
  selectPlaceholder,
  removePickedImage,
  thumbnail,
}) => {
  useImageUploads(product.images, saveImages);

  const [infoMessage, setInfoMessage] = useState(null);

  const productMedias: AppMediaType[] = useMemo(() => {
    const imgMedias = (product.images ?? [])?.map(i => ({ ...i, lastModified: 0, file: null, type: MediaType.IMAGE }));
    const videoMedias = (product.videos ?? [])?.map(i => ({ ...i, type: MediaType.VIDEO }));
    return [...imgMedias, ...videoMedias];
  }, [product]);

  const { imageThumbnails, videoThumbnails } = useComputeThumbnails(product);
  const thumbnails = [...imageThumbnails, ...videoThumbnails];

  useEffect(() => {
    if (infoMessage) {
      showLoader(infoMessage, false, true);
    }
  }, [infoMessage]);

  return (
    <View className="flex-row flex-wrap gap-x-15 gap-y-15">
      {thumbnails.map(({ name, src, url, isUploading, uploadProgress, error, meta }, index) => (
        // <ProductImage isThumbnail={item.thumbnail} showCloseBtn={!item.thumbnail} imageUri={item.image} />
        <View className="w-[65px] h-[65px] rounded-[10px] relative" key={index}>
          <Pressable onPress={() => changeThumbnail(index)}>
            <Image
              className={classNames('w-[65px] h-[65px] rounded-[10px]', {
                'border-[4px] border-accentGreen-main': thumbnail === index,
                'border-[0px]': thumbnail !== index,
              })}
              source={{
                uri: url ?? src,
              }}
              resizeMode={'cover'}
            />
          </Pressable>
          {isUploading && (
            <View
              className="absolute bottom-0 right-0 h-full w-full flex items-center justify-center rounded-[10px]"
              style={{ backgroundColor: '#292D321A' }}>
              <CircularProgress
                value={uploadProgress ?? 0}
                radius={wp(16)}
                duration={500}
                delay={600}
                activeStrokeWidth={wp(4)}
                inActiveStrokeWidth={wp(4)}
                strokeLinecap={'round'}
                activeStrokeColor={colors.accentGreen.main}
                inActiveStrokeColor={colors.white}
                maxValue={100}
                valueSuffix={'%'}
                progressValueStyle={{
                  fontSize: wp(8),
                  fontFamily: 'Inter-Bold',
                  color: colors.white,
                }}
              />
            </View>
          )}
          {!isUploading && thumbnail !== index && (
            <Pressable onPress={() => removePickedImage(index)} className="absolute top-[5px] right-[5px]">
              <View className="h-20 w-20 flex items-center justify-center rounded-full bg-black-secondary">
                <Close currentColor={colors.white} size={wp(10)} />
              </View>
            </Pressable>
          )}
        </View>
      ))}
      <Pressable
        className="rounded-[7px] border-grey-border border border-dashed h-[60] w-[60] flex items-center justify-center"
        onPress={() => pickMultipleMedia(productMedias, saveMedias, false, {}, setInfoMessage)}>
        <Add color={colors.black.placeholder} size={wp(20)} />
      </Pressable>
    </View>
  );
};

export default PickProductImages;
