import useImageUploads from 'src/hooks/use-file-uploads';
import { Product } from './types';
import { Image as ImageType } from 'src/@types/utils';
import { Image, View } from 'react-native';
import CircularProgress from 'react-native-circular-progress-indicator';
import colors from 'src/theme/colors';
import { wp } from 'src/assets/utils/js';
import Pressable from '@/components/ui/base/pressable';
import { Add } from 'iconsax-react-native/src';
import classNames from 'classnames';
import { Close } from '@/components/ui/icons';
import { pickMultipleImages } from 'src/assets/utils/js/pick-multiple-images';

interface Props {
  product: Product;
  thumbnail?: number;
  saveImages: (images: ImageType[] | ((currentImages: ImageType[]) => ImageType[])) => void;
  changeThumbnail: (id: number) => void;
  removePickedImage: (id: number) => void;
  selectPlaceholder?: VoidFunction;
}

const PickProductImages: React.FC<Props> = ({
  product,
  saveImages,
  changeThumbnail,
  selectPlaceholder,
  removePickedImage,
  thumbnail,
}) => {
  useImageUploads(product.images, saveImages);

  return (
    <View className="flex-row flex-wrap gap-x-15 gap-y-15">
      {product.images.map(({ name, src, url, isUploading, uploadProgress, error }, index) => (
        // <ProductImage isThumbnail={item.thumbnail} showCloseBtn={!item.thumbnail} imageUri={item.image} />
        <View className="w-[65px] h-[65px] rounded-[10px] relative" key={index}>
          <Pressable onPress={() => changeThumbnail(index)}>
            <Image
              className={classNames('w-[65px] h-[65px] rounded-[10px]', {
                'border-[4px] border-accentGreen-main': thumbnail === index,
                'border-[0px]': thumbnail !== index,
              })}
              source={{
                uri: src,
              }}
              resizeMode={'cover'}
            />
          </Pressable>
          {isUploading && (
            <View
              className="absolute bottom-0 right-0 h-full w-full flex items-center justify-center rounded-[10px]"
              style={{ backgroundColor: '#292D321A' }}>
              <CircularProgress
                value={uploadProgress ?? 0}
                radius={wp(16)}
                duration={500}
                delay={600}
                activeStrokeWidth={wp(4)}
                inActiveStrokeWidth={wp(4)}
                strokeLinecap={'round'}
                activeStrokeColor={colors.accentGreen.main}
                inActiveStrokeColor={colors.white}
                maxValue={100}
                valueSuffix={'%'}
                progressValueStyle={{
                  fontSize: wp(8),
                  fontFamily: 'Inter-Bold',
                  color: colors.white,
                }}
              />
            </View>
          )}
          {!isUploading && thumbnail !== index && (
            <Pressable onPress={() => removePickedImage(index)} className="absolute top-[5px] right-[5px]">
              <View className="h-20 w-20 flex items-center justify-center rounded-full bg-black-secondary">
                <Close currentColor={colors.white} size={wp(10)} />
              </View>
            </Pressable>
          )}
        </View>
      ))}
      <Pressable
        className="rounded-[7px] border-grey-border border border-dashed h-[60] w-[60] flex items-center justify-center"
        onPress={() => pickMultipleImages(product.images, saveImages)}>
        <Add color={colors.black.placeholder} size={wp(20)} />
      </Pressable>
    </View>
  );
};

export default PickProductImages;
