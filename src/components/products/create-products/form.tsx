import { Al<PERSON>, Dimensions, Image, ScrollView } from 'react-native';
import { View } from 'react-native';
import Button, { ButtonVariant, TextColor } from '@/components/ui/buttons/button';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { BaseText, CircledIcon, Container, Row, WhiteCardBtn } from '@/components/ui';
import { AddCircle, Flag, Minus, Trash } from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import { delay, getFieldvalues, hp, wp } from '@/assets/utils/js';
import SelectDropdown, { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import Input from '@/components/ui/inputs/input';
import Accordion from '@/components/ui/others/accordion';
import { Product, ProductForm as ProductFormType } from './types';
import useModals from 'src/hooks/use-modals';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { RootStackParamList } from 'src/@types/navigation';
import { AppMediaType, Image as ImageType, IVideo } from 'src/@types/utils';
import PickProductImages from './pick-product-images';
import Toast from 'react-native-toast-message';
import { ArrowRight, ArrowUpRight } from '@/components/ui/icons';
import ProductOptionsSection from './add-product-option/product-option-section';
import AccordionAnchor from '@/components/ui/others/accordion/accordion-anchor';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import cx from 'classnames';
import CalenderInput from '@/components/ui/inputs/calender-input';
import dayjs from 'dayjs';
import { Category, MediaType, StoreInterface } from 'catlog-shared';
import ManageCategoriesModal from '../manage-categories-modal';
import ListItemCard from 'src/components/ui/cards/list-item-card';
import { useMemo, useRef } from 'react';
import CustomImage from 'src/components/ui/others/custom-image';
import QuantityToggle from 'src/components/ui/buttons/quantity-toggle';
import useStatusbar from 'src/hooks/use-statusbar';
import { Platform } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import BaseScrollView from 'src/components/ui/base/base-scrollview';
import { NumericFormat } from 'react-number-format';
import MoneyInput from 'src/components/ui/inputs/money-input';
import AddVideo from './add-video';
import ManageProductVideos from '../manage-videos/manage-product-videos';
import Can from 'src/components/ui/can';
import { SCOPES } from 'src/assets/utils/js/permissions';

interface ProductFormProps {
  store: StoreInterface;
  product: Product;
  setForm: (form: ProductFormType) => void;
  index: number;
  form: ProductFormType;
  changeView: (dir: string) => void;
  submitForm: () => void;
  isLoading: boolean;
  isDuplication?: boolean;
  categories: Category[];
  allowVariants: boolean;
  isSetup: boolean;
  isEditing?: boolean;
  currentIndex?: number;
  variantTemplates?: Product['variants'][];
  removeProduct?: (index: number) => void;
}

const ProductsForm = (props: ProductFormProps) => {
  const {
    product,
    setForm,
    index,
    form,
    changeView,
    submitForm,
    isLoading,
    categories,
    isDuplication,
    allowVariants,
    isSetup,
    isEditing,
    currentIndex,
    variantTemplates,
    store,
    removeProduct,
  } = props;
  const { modals, toggleModal } = useModals([
    'categories',
    'manageCategories',
    'images',
    'variants',
    'v_explainer',
    'placeholders',
  ]);

  const navigation = useNavigation();

  const thisForm = useFormik<Product>({
    initialValues: {
      ...product,
      quantity: 10,
      // is_always_available: false,
    },
    validationSchema: validationSchema(
      Boolean(store?.configuration?.direct_checkout_enabled || store?.flags?.uses_chowbot),
    ),
    onSubmit: async values => {
      updateForm(values);
      if (index === form.products.length - 1) {
        submitForm();
        return;
      }
      if (!isEditing) {
        changeView('forward');
      }
    },
  });

  const hasVariants = thisForm.values.variants?.options ? thisForm.values.variants?.options?.length > 0 : false;

  const updateForm = (values: Product) => {
    const formCopy = { ...form };

    formCopy.products[index] = {
      ...values,
    };

    setForm({ ...formCopy });
  };

  const saveImages = (images: ImageType[] | ((currentImages: ImageType[]) => ImageType[])) => {
    if (typeof images === 'function') {
      thisForm.setFieldValue('images', images(thisForm.values.images));
      return;
    }
    thisForm.setFieldValue('images', images);
  };

  const saveVideos = (videos: IVideo[]) => {
    thisForm.setFieldValue('videos', videos);
  };

  const saveMedias = (medias: AppMediaType[]) => {
    // console.log(medias);
    thisForm.setFieldValue("images", [
      ...(thisForm.values.images ?? []),
      ...medias.filter((m) => m.type === MediaType.IMAGE),
    ]);

    const videos = [...(thisForm.values.videos ?? []), ...medias.filter((m) => m.type === MediaType.VIDEO)];

    thisForm.setFieldValue("videos", videos);
    updateForm({ ...thisForm.values, videos });
  };

  const removePickedImage = (id: number) => {
    if (id === thisForm.values.thumbnail) {
      alert(`Cannot delete thumbnail image`);
      return;
    }

    const imagesCopy = [...thisForm.values.images];
    const newThumbnail = thisForm.values.thumbnail > id ? thisForm.values.thumbnail - 1 : thisForm.values.thumbnail;

    imagesCopy.splice(id, 1);

    thisForm.setFieldValue('images', imagesCopy);
    thisForm.setFieldValue('thumbnail', newThumbnail);
  };

  const changeThumbnail = (id: number) => {
    thisForm.setFieldValue('thumbnail', id);
  };

  const addProductButtons = [
    {
      text: index > 0 ? 'Previous Product' : 'Back to Images',
      onPress: () => {
        updateForm(thisForm.values);
        changeView('backwards');
      },
      variant: ButtonVariant.LIGHT,
      textColor: TextColor.PRIMARY,
      isLoading,
      disabled: isLoading,
    },
    {
      text: index === form.products.length - 1 ? 'Upload Products' : 'Next Product',
      onPress: () => thisForm.handleSubmit(),
      isLoading,
      disabled: isLoading,
    },
  ];

  const editProductButtons = [
    {
      text: isDuplication ? 'Save Item' : 'Save Update',
      onPress: () => thisForm.handleSubmit(),
      isLoading: isLoading,
    },
  ];

  // const removeProduct = async (index: number) => {
  //   const formCopyProducts = JSON.parse(JSON.stringify(form.products)); //deep copy

  //   formCopyProducts.splice(index, 1);
  //   changeView('backwards');
  //   await delay(200);
  //   setForm({ ...form, products: formCopyProducts });

  //   Toast.show({ type: 'success', text1: 'Product removed' });
  // };

  const showDeletionConfirmationPopup = () => {
    Alert.alert(
      'Confirm Deletion',
      'Are you sure you want to delete this product?',
      [
        {
          text: 'Cancel',
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {
          text: 'OK',
          onPress: () => removeProduct(index),
          style: 'destructive',
        },
      ],
      { cancelable: false },
    );
  };

  const categoriesRef = useRef<DropDownMethods>(null);

  const openCreateNewCategoryModal = async () => {
    categoriesRef.current?.close();
    await delay(700);
    toggleModal('manageCategories', true);
  };

  const handleToggleQuantity = (action: '-' | '+') => {
    //handle increase quantity
    if (action === '+') {
      thisForm.setFieldValue('quantity', Number(thisForm.values.quantity) + 1);
      return;
    }

    //handle decrease quantity
    if (action === '-') {
      if (thisForm.values.quantity === null) {
        return;
      }
      if (thisForm.values.quantity === 0) {
        thisForm.setFieldValue('quantity', null);
      } else {
        thisForm.setFieldValue('quantity', Number(thisForm.values.quantity) - 1);
      }
      return;
    }
  };

  const productTotalQty = useMemo(() => {
    if (thisForm.values.variants.options.length > 0) {
      const quantitySummation = thisForm.values?.variants?.options?.reduce(
        (total, item) => total + (item.quantity || 0),
        0,
      );
      return quantitySummation;
    }

    return thisForm?.values?.quantity ?? 0;
  }, [thisForm]);


  return (
    <View className="flex-1">
      <AvoidKeyboard className="flex-1">
        <BaseScrollView className="flex-1">
          <View className="items-center pt-25 pb-30 px-20 bg-accentYellow-pastel">
            {thisForm.values?.images?.[thisForm.values.thumbnail]?.src && (
              <CustomImage
                className="w-[60px] h-[60px] rounded-[15px]"
                imageProps={{
                  source: {
                    uri: thisForm.values?.images?.[thisForm.values.thumbnail]?.src,
                  },
                  contentFit: 'cover',
                }}
              />
            )}
            <BaseText fontSize={22} classes="mt-10" type={'heading'}>
              {thisForm.values.name ? thisForm.values.name : ` Add Details - Product ${index + 1}`}
            </BaseText>
            {!isEditing && (
              <WhiteCardBtn
                className="self-center mt-10 rounded-full"
                icon={<Trash size={wp(12)} color={colors.accentRed.main} strokeWidth={1.5} />}
                onPress={showDeletionConfirmationPopup}>
                <BaseText fontSize={12} classes="text-accentRed-main font-interMedium mr-5">
                  Remove Item
                </BaseText>
              </WhiteCardBtn>
            )}
          </View>
          <Container className="mt-25 pb-25">
            {/* <View className="flex-row flex-wrap gap-x-10">
              {prodImages.map((item, index) => (
                <ProductImage isThumbnail={item.thumbnail} showCloseBtn={!item.thumbnail} imageUri={item.image} />
              ))}
              <Pressable className="rounded-[7px] border-grey-border border border-dashed h-[60] w-[60] flex items-center justify-center">
                <Add color={colors.black.placeholder} size={wp(20)} />
              </Pressable>
            </View> */}
            <PickProductImages
              selectPlaceholder={() => toggleModal('placeholders')}
              {...{
                product: thisForm.values,
                changeThumbnail,
                removePickedImage,
                saveImages,
                saveMedias,
                thumbnail: thisForm.values.thumbnail,
              }}
            />
            <ManageProductVideos product={thisForm.values} saveVideos={saveVideos} />
            <View className="mt-20">
              <Input label={'Product Name'} {...getFieldvalues('name', thisForm)} />
              <MoneyInput
                label={`Product price in ${store?.currencies?.default}`}
                containerClasses="mt-15"
                {...getFieldvalues('price', thisForm)}
              />
              {/* <Input
                label={'Product price in NGN (No commas)'}
                containerClasses="mt-15"
                {...getFieldvalues('price', thisForm)}
                keyboardType={'number-pad'}
              /> */}
              {/* <Accordion title={'Add Discount Price'}>

              </Accordion> */}
              <SelectDropdown
                onPressItem={value => thisForm.setFieldValue('category', value)}
                selectedItem={thisForm.values.category}
                ref={categoriesRef}
                items={[...(categories ?? [])].map(c => ({ label: `${c.emoji} ${c.name}`, value: c.id! }))}
                listAddOns={
                  <ListItemCard
                    showBorder={false}
                    title={'Create New Category'}
                    className="border-t border-t-grey-border"
                    titleProps={{ weight: 'medium' }}
                    onPress={openCreateNewCategoryModal}
                    leftElement={
                      <CircledIcon className="p-6 bg-white">
                        <AddCircle variant={'Bold'} size={wp(20)} color={colors.primary.main} />
                      </CircledIcon>
                    }
                    rightElement={
                      <CircledIcon className="p-10 bg-white">
                        <ArrowRight size={wp(16)} strokeWidth={2} currentColor={colors.primary.main} />
                      </CircledIcon>
                    }
                  />
                }
                label={'Select product category'}
                containerClasses="mt-15"
              />
              <Input
                label={'Describe this product'}
                multiline
                className="h-[120px]"
                containerClasses="mt-15"
                {...getFieldvalues('description', thisForm)}
              />
            </View>
            <View className="mt-15">
              <BaseText type="heading" fontSize={14}>
                Product Quantity
              </BaseText>
              <View className="rounded-12 border border-grey-border mt-15 overflow-hidden">
                <Row
                  className={cx('p-12 bg-grey-bgOne', {
                    'border-b border-b-grey-border': !thisForm.values?.is_always_available,
                  })}>
                  <BaseText fontSize={12} lineHeight={18} classes="text-black-muted">
                    Product is always in stock
                  </BaseText>
                  <CustomSwitch
                    value={thisForm.values?.is_always_available}
                    onValueChange={v => thisForm.setFieldValue('is_always_available', v)}
                  />
                </Row>
                {!thisForm.values?.is_always_available && (
                  <Row
                    className={cx('py-10 px-15', {
                      'm-10 bg-grey-bgOne rounded-12 border border-grey-border': hasVariants,
                    })}>
                    <BaseText lineHeight={18} classes="text-black-muted">
                      Product Quantity
                    </BaseText>
                    <QuantityToggle
                      quantity={productTotalQty}
                      showDelete={false}
                      onPressAdd={() => handleToggleQuantity('+')}
                      onPressMinus={() => handleToggleQuantity('-')}
                      disabled={thisForm.values?.is_always_available || hasVariants}
                      // showToggleButtons={!hasVariants}
                    />
                    {/* <Row className="justify-end gap-x-10">
                      <Pressable className="p-5 border-grey-border border rounded-full bg-white" onPress={() => {}}>
                        <Minus size={wp(18)} color={colors.black.placeholder} />
                      </Pressable>
                      <View className="py-7 px-10 border-grey-border border rounded-[6px] bg-white">
                        <BaseText fontSize={12} weight={'semiBold'} classes="text-black-muted">
                          {thisForm?.values?.quantity ?? 0}
                        </BaseText>
                      </View>
                      <Pressable className="p-5 border-grey-border border rounded-full bg-white" onPress={() => {}}>
                        <Add size={wp(18)} color={colors.black.placeholder} />
                      </Pressable>
                    </Row> */}
                  </Row>
                )}
                {hasVariants && !thisForm.values?.is_always_available && (
                  <View className="px-12 py-8 bg-accentYellow-pastel rounded-8 mt-8 mx-10 mb-10">
                    <BaseText fontSize={11} classes="text-black-muted" lineHeight={16}>
                      This quantity is a summation of the quantities you've set for each individual product options
                    </BaseText>
                  </View>
                )}
              </View>
            </View>
            <Can data={{ planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_OPTIONS }}>
              <View className="mt-15">
                <ProductOptionsSection
                  form={thisForm}
                  product={product}
                  saveVariants={variants => {
                    thisForm.setFieldValue('variants', variants);
                    updateForm({ ...thisForm.values, variants });
                  }}
                />
              </View>
            </Can>
            <Accordion
              anchorElement={status => <AccordionAnchor title={'Optional Product Settings'} isOpened={status} />}
              title={'Optional Product Settings'}>
              <Input
                label={`Discount price in ${store?.currencies?.default} (Optional)`}
                containerClasses="mt-15"
                keyboardType={'number-pad'}
                {...getFieldvalues('discount_price', thisForm)}
              />
              <Input
                label={'Minimum order quantity'}
                containerClasses="mt-15"
                keyboardType={'number-pad'}
                {...getFieldvalues('minimum_order_quantity', thisForm)}
              />
              <CalenderInput
                inputProps={{
                  label: 'Expiry Date',
                  containerClasses: 'mt-10',
                  value: thisForm.values.expiry_date ? dayjs(thisForm.values.expiry_date).format('DD, MMM YYYY') : null,
                }}
                calenderModalProps={{ singleSelectMode: true, startDate: thisForm.values.expiry_date }}
                onDateChange={(date, type) => {
                  thisForm.setFieldValue('expiry_date', date);
                }}
              />
              <Input
                label={'Cost Price'}
                containerClasses="mt-15"
                keyboardType={'number-pad'}
                {...getFieldvalues('cost_price', thisForm)}
              />
            </Accordion>
          </Container>
        </BaseScrollView>
      </AvoidKeyboard>
      <FixedBtnFooter buttons={isEditing ? editProductButtons : addProductButtons} />
      {modals.manageCategories && (
        <ManageCategoriesModal
          storeCategories={categories ? categories : []}
          isVisible={modals.manageCategories}
          closeModal={() => toggleModal('manageCategories', false)}
        />
      )}
    </View>
  );
};

const validationSchema = (direct_checkout_enabled: boolean) =>
  Yup.object().shape({
    name: Yup.string().required('Item name is required'),
    description: Yup.string().required('Item description is required'),
    price: Yup.string()
      .required('Item price is required')
      .test('digits', 'Item price should be a number', value => !Number.isNaN(Number(value))),
    discount_price: Yup.number()
      .optional()
      .test(
        'islesser',
        'Discount price should be lesser than item price',
        (value, ctx) => value === undefined || Number(ctx.parent.price) > Number(value),
      )
      .min(1, 'Price must be greater than 0')
      .integer('Price must be a number'),
    is_always_available: Yup.boolean().optional(),
    quantity: direct_checkout_enabled
      ? Yup.number().when('is_always_available', (isAlwaysAvailable, schema) => {
          if (Boolean(isAlwaysAvailable) === false) {
            return schema.required('Quantity must be provided');
          }
          return schema.optional();
        })
      : Yup.number().optional(),
  });

export default ProductsForm;
