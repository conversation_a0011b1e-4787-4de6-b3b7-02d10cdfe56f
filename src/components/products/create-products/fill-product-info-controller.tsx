import { Di<PERSON><PERSON>, ScrollView } from 'react-native';
import { View } from 'react-native';
import Button, { ButtonVariant, TextColor } from '@/components/ui/buttons/button';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { BaseText, CircledIcon, Container, Row, WhiteCardBtn } from '@/components/ui';
import { Edit, Instagram, Shop, Trash } from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import { hp, wp } from '@/assets/utils/js';
import AuthQuestion from '@/components/auth/auth-question';
import Pressable from '@/components/ui/base/pressable';
import { useRef, useState } from 'react';
import SelectDropdown, { DropDownItem, DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import { mockProducts, prodImages } from '@/constant/mock-data';
import SectionContainer from '@/components/ui/section-container';
import { Image } from 'react-native';
import { Add } from 'iconsax-react-native/src';
import { FillProductsInfoActionType } from '@/screens/products/storefront/create-products/fill-products-info';
import { ProductAddingScreenType } from './product-adding-method-controller';
import { RootStackParamList } from '@/@types/navigation';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import Input from '@/components/ui/inputs/input';
import Accordion from '@/components/ui/others/accordion';
import ProductImage from '../storefront-products/product-image';
import ProductCard from '../storefront-products/product-card';
import Separator from '@/components/ui/others/separator';
import BaseScrollView from '@/components/ui/base/base-scrollview';

interface FillProductInfoControllerProps {
  screen: ProductAddingScreenType;
}

const FillProductInfoController = ({
  screen = ProductAddingScreenType.ADD_PRODUCT,
}: FillProductInfoControllerProps) => {
  const navigation = useNavigation();
  const { params } = useRoute<RouteProp<RootStackParamList, 'FillProductsInfo'>>();

  const [selectProductCategory, setSelectProductCategory] = useState('');

  const discountDuration: DropDownItem[] = [
    {
      value: 'Food',
      label: 'Food',
    },
    {
      value: 'Phones',
      label: 'Food',
    },
  ];

  const onSelectProductCategory = (value: string) => {
    setSelectProductCategory(value);
  };

  const addProductButtons = [
    {
      text: 'Back to Images',
      onPress: () => {},
      variant: ButtonVariant.LIGHT,
      textColor: TextColor.PRIMARY,
    },
    {
      text: 'Next Product',
      onPress: () => {
        navigation.navigate('Feedback', {
          headerTitle: 'Product Uploaded',
          headerBg: 'bg-accentYellow-pastel',
          feedbackText: 'You have successfully \n     added 2 items',
          btnText: 'Go to products',
          onPressBtn: () => navigation.navigate('HomeTab'),
        });
      },
    },
  ];

  const editProductButtons = [
    {
      text: 'Save Update',
      onPress: () => {},
    },
  ];

  return (
    <View className="flex-1">
      <AvoidKeyboard>
        <BaseScrollView>
          <View className="items-center pt-25 pb-30 px-20 bg-accentYellow-pastel">
            <Image
              className="w-[60px] h-[60px] rounded-[15px]"
              source={{
                uri: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcScm3h5fAKg9F9ATCMTobLQngMc_ZoigQooSw&usqp=CAU',
              }}
              resizeMode={'cover'}
            />
            <BaseText fontSize={22} classes="mt-10" type={'heading'}>
              Sweet Sensation Pasta
            </BaseText>
            <WhiteCardBtn
              className="self-center mt-10 rounded-full"
              icon={<Trash size={wp(12)} color={colors.accentRed.main} />}>
              <BaseText fontSize={12} classes="text-accentRed-main font-interMedium mr-5">
                Remove Item
              </BaseText>
            </WhiteCardBtn>
          </View>
          <Container className="mt-25 pb-25">
            <View className="flex-row flex-wrap gap-x-10">
              {prodImages.map((item, index) => (
                <ProductImage isThumbnail={item.thumbnail} showCloseBtn={!item.thumbnail} imageUri={item.image} />
              ))}
              <Pressable className="rounded-[7px] border-grey-border border border-dashed h-[60] w-[60] flex items-center justify-center">
                <Add color={colors.black.placeholder} size={wp(20)} />
              </Pressable>
            </View>
            <View className="mt-20">
              <Input label={'Product Name'} />
              <Input label={'Product price in NGN (No commas)'} containerClasses="mt-15" />
              <Accordion title={'Add Discount Price'}>
                <Input label={'Discount price in NGN (optional)'} />
              </Accordion>
              <SelectDropdown
                selectedItem={selectProductCategory}
                onPressItem={onSelectProductCategory}
                items={discountDuration}
                label={'Select product category'}
                containerClasses="mt-15"
              />
              <Input
                label={'Describe your store - What you sell'}
                multiline
                className="h-[120px]"
                containerClasses="mt-15"
              />
            </View>
            <View className="mt-15">
              <Row className="justify-start gap-x-10">
                <WhiteCardBtn className="bg-grey-bgOne" icon={<Add color={colors.black.placeholder} size={wp(14)} />}>
                  Add Product Options
                </WhiteCardBtn>
                <BaseText>0 added.</BaseText>
                <BaseText classes="text-black-muted underline" style={{ textDecorationColor: colors.black.muted }}>
                  What are options?
                </BaseText>
              </Row>
              <SectionContainer>
                <ProductCard product={mockProducts[0]} listView />
                <Separator className="my-0 mx-0" />
                <BaseText fontSize={14} weight={'bold'} type={'heading'} classes="mt-15">
                  Provide quantities for each option
                </BaseText>
                <ProductCard product={mockProducts[0]} listView />
                <Separator className="my-0 mx-0" />
                <ProductCard product={mockProducts[0]} listView />
                <Separator className="my-0 mx-0" />
                <ProductCard product={mockProducts[0]} listView />
              </SectionContainer>
            </View>
          </Container>
        </BaseScrollView>
      </AvoidKeyboard>
      <FixedBtnFooter
        buttons={params?.action === FillProductsInfoActionType.EDIT ? editProductButtons : addProductButtons}
      />
    </View>
  );
};

export default FillProductInfoController;
