import useImageUploads from 'src/hooks/use-file-uploads';
import { Product } from './types';
import { Image as ImageType, IVideo, TrimData } from 'src/@types/utils';
import { Alert, Image, View } from 'react-native';
import CircularProgress from 'react-native-circular-progress-indicator';
import colors from 'src/theme/colors';
import { wp } from 'src/assets/utils/js';
import Pressable from '@/components/ui/base/pressable';
import { Add, DocumentUpload } from 'iconsax-react-native/src';
import classNames from 'classnames';
import { Close } from '@/components/ui/icons';
import { pickMultipleImages } from 'src/assets/utils/js/pick-multiple-images';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import { useState } from 'react';
import * as ImagePicker from 'expo-image-picker';
import { pickVideos } from 'src/assets/utils/js/pick-videos';
import { FFmpegKit } from 'ffmpeg-kit-react-native';
import useVideoCompression from 'src/hooks/use-video-compressor';
import TrimVideoModal from '../manage-videos/trim-video-modal';
import useModals from 'src/hooks/use-modals';
import { useVideoTrimmer } from 'src/hooks/use-video-trimmer';

interface Props {}

const AddVideo: React.FC<Props> = ({}) => {
  const { modals, toggleModal } = useModals(['trimVideo']);
  const [video, setVideo] = useState<IVideo[] | null>(null);
  const [selectedVideos, setSelectedVideos] = useState<IVideo[] | null>(null);

  const [activeVideo, setActiveVideo] = useState<IVideo | null>(null);

  const { compressVideo, cancelCompression, reset, isCompressing, progress, error } = useVideoCompression();

  const handlePickVideo = async () => {
    try {
      const result = await pickVideos();
      // console.log(result);

      if (result.length > 0) {
        setSelectedVideos(result);
        // setActiveVideo(result[0]);
        // console.log(result);
        // toggleModal('trimVideo', true);
      }
    } catch (error) {
      console.log(error);
    }
  };

  // const handleTrimVideo = async (trimData: Partial<TrimData>) => {
  //   try {
  //     const result = await trimVideo(activeVideo?.src!, trimData.startTime, trimData.endTime);
  //     if (result.success) {
  //       console.log(result);
  //       Alert.alert(
  //         'Success',
  //         `Trimming successful!`,
  //       );
  //     } else {
  //       Alert.alert('Error', result.error);
  //     }
  //   } catch (error) {
  //     console.log(error);
  //   }
  // };

  return (
    <View>
      <Pressable
        className="border border-grey-border rounded-8 items-center justify-center p-12 mt-10"
        onPress={handlePickVideo}>
        {/* <View className="absolute top-0 right-0 h-full w-full flex items-center justify-center rounded-[10px]">
              <CircularProgress
                  value={progress ?? 0}
                  radius={wp(16)}
                  duration={500}
                  delay={600}
                  activeStrokeWidth={wp(4)}
                  inActiveStrokeWidth={wp(4)}
                  strokeLinecap={'round'}
                  activeStrokeColor={colors.accentGreen.main}
                  inActiveStrokeColor={colors.white}
                  maxValue={100}
                  valueSuffix={'%'}
                  progressValueStyle={{
                    fontSize: wp(8),
                    fontFamily: 'Inter-Bold',
                    color: colors.white,
                  }}
                />
            </View> */}

        <Row disableSpread>
          <CircledIcon className="p-6 bg-grey-bgOne">
            <DocumentUpload size={wp(20)} variant="Bold" color={colors.black.muted} />
          </CircledIcon>
          <BaseText weight="medium" classes="text-black-placeholder ml-5">
            Upload Video
          </BaseText>
        </Row>
        <BaseText fontSize={12} classes="text-black-placeholder mt-5">
          MP4, MOV, HEIC (Max size - 15mb)
        </BaseText>
      </Pressable>
    </View>
  );
};

export default AddVideo;
