import useImageUploads from 'src/hooks/use-file-uploads';
import { Product } from './types';
import { Image as ImageType, IVideo, TrimData } from 'src/@types/utils';
import { Alert, Image, View } from 'react-native';
import CircularProgress from 'react-native-circular-progress-indicator';
import colors from 'src/theme/colors';
import { wp } from 'src/assets/utils/js';
import Pressable from '@/components/ui/base/pressable';
import { Add, DocumentUpload } from 'iconsax-react-native/src';
import classNames from 'classnames';
import { Close } from '@/components/ui/icons';
import { pickMultipleImages } from 'src/assets/utils/js/pick-multiple-images';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import { useState } from 'react';
import * as ImagePicker from 'expo-image-picker';
import { pickVideos } from 'src/assets/utils/js/pick-videos';
import { FFmpegKit } from 'ffmpeg-kit-react-native';
import useVideoCompression from 'src/hooks/use-video-compressor';
import TrimVideoModal from '../manage-videos/trim-video-modal';
import useModals from 'src/hooks/use-modals';
import { useVideoTrimmer } from 'src/hooks/use-video-trimmer';

interface Props {
  handlePickVideo: VoidFunction;
}

const AddVideo: React.FC<Props> = ({ handlePickVideo } : Props) => {

  return (
    <View>
      <Pressable
        className="border border-grey-border rounded-8 items-center justify-center p-12 mt-10"
        onPress={handlePickVideo}>
        <Row disableSpread>
          <CircledIcon className="p-6 bg-grey-bgOne">
            <DocumentUpload size={wp(20)} variant="Bold" color={colors.black.muted} />
          </CircledIcon>
          <BaseText weight="medium" classes="text-black-placeholder ml-5">
            Upload Video
          </BaseText>
        </Row>
        <BaseText fontSize={12} classes="text-black-placeholder mt-5">
          MP4, MOV, HEIC (Max size - 15mb)
        </BaseText>
      </Pressable>
    </View>
  );
};

export default AddVideo;
