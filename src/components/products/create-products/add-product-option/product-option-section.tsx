import { Add, Flag, Trash } from 'iconsax-react-native/src';
import * as React from 'react';
import { View } from 'react-native';
import { alertPromise, showError, wp } from 'src/assets/utils/js';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import { ArrowUpRight } from '@/components/ui/icons';
import colors from 'src/theme/colors';
import AddProductOptionModal from './add-product-option-modal';
import useModals from 'src/hooks/use-modals';
import { Product } from '../types';
import { FormikProps } from 'formik';
import { VariantForm, VariantItem } from 'catlog-shared';

interface ProductOptionsSectionProps {
  form?: FormikProps<Product>;
  product: Product;
  saveVariants: (variants: VariantForm) => void;
}

const ProductOptionsSection = ({ form, saveVariants }: ProductOptionsSectionProps) => {
  const { modals, toggleModal } = useModals(['addProductOption']);

  const variant = form?.values?.variants;
  const hasVariant = variant?.options.length > 0;

  const openAddOptionModal = () => {
    if (!form.values.name || !form.values.price) {
      showError(undefined, 'Please add a name and price before adding options');
      return;
    }

    toggleModal('addProductOption');
  };

  const removeAllOptions = async () => {
    const confirmed = await alertPromise(
      'Remove All Options',
      'Are you sure you want to remove all product options? This action cannot be undone.',
      'Yes, Remove All',
      'Cancel',
      true,
    );

    if (confirmed) {
      form.setFieldValue('variants', { options: [] });
    }
  };

  return (
    <View>
      <Row className="justify-start">
        <WhiteCardBtn
          className="bg-grey-bgOne mr-2.5"
          onPress={openAddOptionModal}
          icon={<Add color={colors.primary.main} size={wp(14)} strokeWidth={2} />}>
          {hasVariant ? 'Update' : 'Add'} Options
        </WhiteCardBtn>
        {hasVariant && (
          <WhiteCardBtn
            className="bg-grey-bgOne mr-2.5"
            onPress={removeAllOptions}
            icon={<Trash color={colors.accentRed.main} size={wp(14)} strokeWidth={2} />}>
            <BaseText fontSize={12} weight={'medium'} classes="text-accentRed-main">
              Remove all Options
            </BaseText>
          </WhiteCardBtn>
        )}
      </Row>
      <Row disableSpread className="mt-15">
        <BaseText>{variant?.options?.length ?? 0} added.</BaseText>
        <BaseText classes="text-black-muted underline ml-5" style={{ textDecorationColor: colors.black.muted }}>
          What are options?
        </BaseText>
      </Row>
      <AddProductOptionModal
        isVisible={modals.addProductOption}
        // variant={variant}
        product={form?.values!}
        // form={form}
        saveVariants={saveVariants}
        closeModal={() => toggleModal('addProductOption', false)}
      />
    </View>
  );
};

export default ProductOptionsSection;
