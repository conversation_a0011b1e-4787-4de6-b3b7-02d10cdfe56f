import { Image, IVideo } from 'src/@types/utils';

export interface Product {
  thumbnail: number;
  images: Image[];
  name: string;
  price: string;
  discount_price?: string;
  category: string;
  price_unit?: string;
  is_always_available?: boolean;
  quantity?: number;
  description: string;
  variants?: {
    type: string;
    is_template?: boolean;
    options: VariantItem[];
  };
  hasImages?: boolean;
  id?: string;
  upload_source?: ProductCreateMethod;
  minimum_order_quantity?: number;
  cost_price?: number;
  expiry_date?: Date;
  thumbnail_type?: string;
  videos?: IVideo[];
  temp_id?: string;
  info_blocks?: InfoBlockInterface[];
}

export enum ProductCreateMethod {
  MANUAL = 'MANUAL',
  CHOWDECK = 'CHOWDECK',
  STORE = 'STORE',
  MENU_IMAGE = 'MENU_IMAGE',
  INSTAGRAM = 'INSTAGRAM',
}

export type ProductUploadStep = 'method' | 'images' | 'import' | 'details' | 'response';
import { InfoBlockInterface, VariantItem, Video } from 'catlog-shared';

export interface ProductForm {
  products: Product[];
}
