import classNames from 'classnames';
import { Add } from 'iconsax-react-native/src';
import React, { useState } from 'react';
import { ActivityIndicator, Dimensions, Image, ScrollView, View } from 'react-native';
import CircularProgress from 'react-native-circular-progress-indicator';
import { Image as ImageType } from 'src/@types/utils';
import { generateSimpleUUID, wp } from 'src/assets/utils/js';
import { pickMultipleImages } from 'src/assets/utils/js/pick-multiple-images';
import { BaseText, CircledIcon } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import ListItemCard from '@/components/ui/cards/list-item-card';
import Container from '@/components/ui/container';
import { Close, ImageSelectorPlaceholder } from '@/components/ui/icons';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import Row from '@/components/ui/row';
import ScreenInfoHeader from '@/components/ui/screen-info-header';
import SectionContainer from '@/components/ui/section-container';
import { ColorPaletteType } from 'src/constant/static-data';
import useImageUploads from 'src/hooks/use-file-uploads';
import colors from 'src/theme/colors';
import { ProductForm, ProductUploadStep } from './types';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import BaseScrollView from 'src/components/ui/base/base-scrollview';
import CustomImage from 'src/components/ui/others/custom-image';

interface AddProductImagesProps {
  setForm: (form: ProductForm) => void;
  setCurrentStep: (step: ProductUploadStep) => void;
  form: ProductForm;
  currentStep: string;
  maxUploadable: number;
  pastProgress: ProductForm;
  images: ImageType[];
  setImages: React.Dispatch<React.SetStateAction<ImageType[]>>;
  showBackBtn?: boolean;
}

const { width } = Dimensions.get('window');
const cardWidth = (width - 40 - 20) / 2;

const AddProductImages = (props: AddProductImagesProps) => {
  const { setForm, setCurrentStep, form, currentStep, maxUploadable, pastProgress, images, setImages, showBackBtn } =
    props;

  const [imageSelectionLoading, setImageSelectionLoading] = useState(false);
  const [infoMessage, setInfoMessage] = useState('Launching selection tray...');
  useImageUploads(images, setImages);

  const areImagesUploading = images.some(image => image.isUploading || image.uploadProgress < 100);
  const uploadEnabled = images.length < maxUploadable;

  const handlePickImages = async () => {
    setImageSelectionLoading(true);
    await pickMultipleImages(
      images,
      setImages,
      false,
      { selectionLimit: maxUploadable - images.length },
      setInfoMessage,
    );
    setImageSelectionLoading(false);
  };

  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);

    setImages(newImages);
  };

  const finalizeSelection = () => {
    const productsWithInititalImages = images.map(image => {
      const prevData = form.products.find(item => item.images.findIndex(i => i.src === image.src) !== -1);

      return prevData
        ? prevData
        : {
            images: [image],
            videos: [],
            name: '',
            price: '',
            category: '',
            description: '',
            thumbnail: 0,
            price_unit: '',
            variants: {
              type: '',
              options: [],
            },
            id: generateSimpleUUID(),
          };
    });

    setForm({ ...form, products: productsWithInititalImages });
    setCurrentStep('details');
  };

  return (
    <>
      <BaseScrollView>
        <View className="flex-1">
          <ScreenInfoHeader
            pageTitleTop={'Add Images'}
            pageTitleBottom={'For your Products'}
            iconElement={
              <CustomImage
                imageProps={{ source: require('@/assets/images/add-product-image.png'), contentFit: 'cover' }}
                className="w-80 h-80"
              />
            }
            colorPalette={ColorPaletteType.YELLOW}
          />
          <Container className="flex-1 mx-0 w-full">
            <SectionContainer className="py-12 px-15 rounded-[10px] flex-none">
              <StatusPill title={'IMPORTANT'} className="self-start" statusType={StatusType.DANGER} />
              <BaseText fontSize={12} classes="text-black-secondary mt-8 leading-[15px]">
                {uploadEnabled
                  ? 'Upload one image for every product you want to upload. Maximum of 10 images allowed.'
                  : "Maximum of 10 images allowed. You've reached the limit of images allowed at this time"}
              </BaseText>
            </SectionContainer>

            {images.length === 0 && !imageSelectionLoading && (
              <View className="w-full flex justify-center items-center py-24">
                <Pressable
                  onPress={handlePickImages}
                  disabled={!uploadEnabled}
                  style={{ opacity: uploadEnabled ? 1 : 0.4 }}>
                  <ImageSelectorPlaceholder />
                </Pressable>
              </View>
            )}

            {images.length > 0 && (
              <View className="flex flex-row items-start justify-between flex-wrap mt-25">
                {images.map((image, index) => (
                  <ImageGridItem key={index} {...{ image, index, removeImage: () => removeImage(index) }} />
                ))}
                {uploadEnabled && (
                  <Pressable
                    className="bg-grey-bgOne self-start rounded-[10px] py-10 px-15 flex-shrink-0 mb-25"
                    onPress={handlePickImages}
                    style={{ width: cardWidth }}>
                    <Row className="justify-between">
                      <BaseText fontSize={12} weight="medium" classes="text-primary-main mr-12">
                        Add new image
                      </BaseText>
                      <CircledIcon className="p-5 bg-white">
                        <Add size={wp(20)} strokeWidth={2} variant="Linear" color={colors.primary.main} />
                      </CircledIcon>
                    </Row>
                  </Pressable>
                )}
              </View>
            )}
            {images.length < 1 && imageSelectionLoading && (
              <View className="flex-1 justify-center items-center min-h-[100px]">
                <ActivityIndicator size={'small'} color={colors.grey.mutedDark} />
                <BaseText fontSize={12} classes="text-black-muted mt-10 text-center">
                  {infoMessage}
                </BaseText>
              </View>
            )}
          </Container>
        </View>
      </BaseScrollView>
      <FixedBtnFooter
        buttons={[
          showBackBtn
            ? {
                text: 'Back',
                onPress: () => setCurrentStep('method'),
                variant: ButtonVariant.LIGHT,
              }
            : null,
          {
            text: 'Proceed',
            onPress: finalizeSelection,
            disabled: areImagesUploading || images.length === 0,
          },
        ].filter(Boolean)}
      />
    </>
  );
};

const ImageGridItem = ({
  image,
  index,
  removeImage,
}: {
  image: ImageType;
  index: number;
  removeImage: VoidFunction;
}) => {
  return (
    <ListItemCard
      className={classNames('flex-shrink-0 mb-25 py-0', { 'mr-10': index % 2 === 0, 'ml-10': index % 2 !== 0 })}
      style={{ maxWidth: cardWidth }}
      leftElement={
        <View className="w-[50px] h-[50px] rounded-[10px] relative">
          <Image
            className="w-[50px] h-[50px] rounded-[10px]"
            source={{
              uri: image.src,
            }}
            resizeMode={'cover'}
          />
          {image.isUploading && (
            <View
              className="absolute bottom-0 right-0 h-full w-full flex items-center justify-center rounded-[10px]"
              style={{ backgroundColor: '#292D321A' }}>
              <CircularProgress
                value={image.uploadProgress ?? 0}
                radius={wp(16)}
                duration={500}
                delay={600}
                activeStrokeWidth={wp(4)}
                inActiveStrokeWidth={wp(4)}
                strokeLinecap={'round'}
                activeStrokeColor={colors.accentGreen.main}
                inActiveStrokeColor={colors.white}
                maxValue={100}
                valueSuffix={'%'}
                progressValueStyle={{
                  fontSize: wp(8),
                  fontFamily: 'Inter-Bold',
                  color: colors.white,
                }}
              />
            </View>
          )}
        </View>
      }
      bottomElement={
        <View>
          <View className="h-[6px] bg-grey-bgOne mt-10 rounded-full" />
          <View className="h-[6px] w-3/4 bg-grey-bgOne mt-5 rounded-full" />
        </View>
      }
      title={`Product ${index + 1}`}
      titleProps={{ type: 'heading', classes: 'text-black-muted' }}
      titleAddon={
        !image.isUploading ? (
          <Pressable onPress={removeImage}>
            <View className="h-24 w-24 flex items-center justify-center rounded-full bg-grey-bgOne">
              <Close currentColor={colors.grey.muted} size={wp(10)} />
            </View>
          </Pressable>
        ) : undefined
      }
    />
  );
};

export default AddProductImages;
