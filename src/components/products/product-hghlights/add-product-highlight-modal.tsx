import {
  Add<PERSON>ircle,
  CloseCircle,
  Copy,
  DocumentUpload,
  InfoCircle,
  Instagram,
  TickCircle,
} from 'iconsax-react-native/src';
import { ActivityIndicator, ScrollView, View } from 'react-native';
import {
  cx,
  delay,
  generateSimpleUUID,
  getFieldvalues,
  hideLoader,
  hp,
  showError,
  showLoader,
  showSuccess,
  toCurrency,
  wp,
  Yup,
} from 'src/assets/utils/js';
import { BaseText, CircledIcon, Row, SelectionPill } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import SheetModal, { SheetModalProps } from 'src/components/ui/modals/sheet-modal';
import SectionContainer from 'src/components/ui/section-container';
import colors from 'src/theme/colors';
import Input from 'src/components/ui/inputs/input';
import But<PERSON>, { ButtonSize, ButtonVariant } from 'src/components/ui/buttons/button';
import { BottomSheetModalProvider, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { useApi } from 'src/hooks/use-api';
import {
  ADD_DOMAIN,
  CHECK_DOMAIN,
  CREATE_HIGHLIGHT,
  CreateHighlightParams,
  HighlightInterface,
  MediaType,
  UPDATE_HIGHLIGHT,
  UpdateHighlightParams,
  Video,
} from 'catlog-shared';
import { useEffect, useMemo, useRef, useState } from 'react';
import useStatusbar from 'src/hooks/use-statusbar';
import Shimmer from 'src/components/ui/shimmer';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import SelectDropdown, { DropDownItem, DropDownMethods } from 'src/components/ui/inputs/select-dropdown';
import { useFormik } from 'node_modules/formik/dist';
import ManageProductVideos, { ManageProductVideosRef } from '../manage-videos/manage-product-videos';
import { AppMediaType, IVideo } from 'src/@types/utils';
import { Image } from 'react-native-svg';
import ManageHighlightVideos from '../manage-videos/manage-highlight-videos';
import useStorefrontItems from 'src/hooks/use-storefront-items';
import useAuthContext from 'src/contexts/auth/auth-context';
import SelectSpecificProductsModal from '../storefront-products/select-specific-product-modal';
import useModals from 'src/hooks/use-modals';
import InstagramImportManager from '../add-product-controller/instagram-import/instagram-import-manager';
import { useVideoDownload } from 'src/hooks/use-download-video';
import ProgressModal from 'src/components/ui/progress-modal';
import InstagramImportProgress from './instagram-import-progress';

enum UPLOAD_METHOD {
  MANUALLY = 'Manually',
  SELECT_PRODUCTS = 'Product',
  INSTAGRAM = 'Instagram',
}

export interface FormHighlightVideo {
  video: IVideo;
  active?: boolean;
  products: string[];
  thumbnail: string;
}

export interface AddProductHighlightFormProps {
  title: string;
  videos: FormHighlightVideo[];
  active?: boolean;
  id: string;
}

interface Props extends Partial<BottomModalProps> {
  closeModal: () => void;
  isEdit?: boolean;
  highlightToEdit?: HighlightInterface;
  onSuccess: (highlight: HighlightInterface) => void;
}

const AddProductHighlightModal = ({ closeModal, isEdit, highlightToEdit, onSuccess, ...props }: Props) => {
  const { modals, toggleModal, switchModals } = useModals(['videoFromProduct', 'instagram', 'webview', 'progress']);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [selectedInstagramMedia, setSelectedInstagramMedia] = useState<string[]>([]);
  const dropDownRef = useRef<DropDownMethods>(null);
  // const { setStatusBarStyle } = useStatusbar();
  // setStatusBarStyle(props.visible ? 'light' : 'dark', true);

  const createHighlightReq = useApi<CreateHighlightParams>({
    apiFunction: CREATE_HIGHLIGHT,
    method: 'POST',
    key: CREATE_HIGHLIGHT.name,
  });
  const updateHighlightReq = useApi<UpdateHighlightParams>({
    apiFunction: UPDATE_HIGHLIGHT,
    method: 'PUT',
    key: UPDATE_HIGHLIGHT.name,
  });

  const manageVideosRef = useRef<ManageProductVideosRef>(null);

  const { store } = useAuthContext();
  const { items, fetchItemsReq, getItem } = useStorefrontItems(store.id, { videosOnly: true });

  const { downloadVideo } = useVideoDownload();

  const handleDownload = async (url: string, filename: string) => {
    try {
      console.log('Started download: ', url);
      const download = await downloadVideo(url, filename);
      return download;
    } catch (err) {
      throw new Error(err);
    }
  };

  const form = useFormik<AddProductHighlightFormProps>({
    initialValues: isEdit
      ? getInitialValues(highlightToEdit)
      : {
          title: '',
          videos: [],
          active: true,
          id: generateSimpleUUID(),
        },
    validationSchema,
    onSubmit: async values => {
      let result: any[];

      const isVideosUploaded = values.videos?.every(v => v.video.url !== undefined && v.video.url.trim() !== '');
      if (!isVideosUploaded) {
        showError(undefined, 'Please upload all videos or remove proceeding');
        return;
      }

      if (isEdit) {
        result = await updateHighlightReq.makeRequest({
          id: values.id,
          title: values.title,
          active: values.active,
          videos: values.videos.map(v => ({
            url: v.video.url,
            active: v.active,
            thumbnail: v.video.meta?.thumbnail?.url,
            products: v.products,
          })),
        });
      } else {
        result = await createHighlightReq.makeRequest({
          title: values.title,
          videos: values.videos.map(v => ({
            url: v.video.url,
            active: true,
            thumbnail: v.video.meta?.thumbnail?.url,
            products: v.products,
          })),
        });
      }

      showLoader(`Saving highlight...`);

      const [response, error] = result;

      hideLoader();
      await delay(700);

      if (error) {
        showError(error?.message ?? `Couldn't ${isEdit ? 'update' : 'create'} highlight`);
      } else {
        closeModal();
        await delay(700);
        showSuccess(`Highlight ${isEdit ? 'updated' : 'created'} successfully`);
        onSuccess?.(response.data);
      }
    },
  });

  useEffect(() => {
    if (isEdit) {
      console.log(highlightToEdit);
      form.setValues(getInitialValues(highlightToEdit));
    }
  }, [isEdit, highlightToEdit]);

  const handleSaveVideos = (medias: IVideo[]) => {
    console.log('handleSaveVideos');
    // const videos = [...medias.filter(m => m.type === MediaType.VIDEO)];

    const highlightValue = medias.map(m => ({
      video: m,
      products: [],
      thumbnail: '',
      active: true,
    }));

    console.log('form.values.videos: ', JSON.stringify(form.values.videos));
    console.log('highlightValue: ', JSON.stringify(highlightValue));
    console.log('medias: ', JSON.stringify(medias));
    form.setFieldValue('videos', [...form.values.videos, ...highlightValue]);
  };

  const handleRemoveVideo = (index: number) => {
    const formCopy = { ...form.values };
    formCopy.videos = formCopy.videos.filter((v, i) => i != index);

    form.setValues(formCopy);
  };

  // Function to trigger video picking from parent component
  const triggerVideoPicker = async () => {
    await delay(700);
    console.log('triggerVideoPicker');
    manageVideosRef.current?.handlePickVideo();
  };

  const handleUploadMethodSelect = async (v: UPLOAD_METHOD) => {
    if (v === UPLOAD_METHOD.MANUALLY) {
      await triggerVideoPicker();
    }

    if (v === UPLOAD_METHOD.SELECT_PRODUCTS) {
      await delay(700);
      toggleModal('videoFromProduct', true);
    }

    if (v === UPLOAD_METHOD.INSTAGRAM) {
      await delay(700);
      toggleModal('instagram', true);
    }
  };

  const extractFormVideosFromProducts = () => {
    const products = selectedProducts.map(id => getItem(id));

    const videos: FormHighlightVideo[] = products.flatMap(product => {
      if (product.videos.length > 0) {
        const productVideos = product.videos.map(video => ({
          video: generateVideoObjectFromUrl(video.url, video.thumbnail),
          products: [product.id],
          active: true,
          thumbnail: video.thumbnail,
        }));
        return productVideos;
      }
      return [];
    });

    form.setFieldValue('videos', [...form.values.videos, ...videos]);
  };

  const handleCompleteInstagramImport = async () => {
    console.log('Proceed clicked');
    toggleModal('instagram', false);

    const filesPromise = Promise.all(
      selectedInstagramMedia.map(async (url, index) => {
        const file = await handleDownload(url, `video-${index.toString()}.mp4`);
        return file.uri;
      }),
    );

    const files = await filesPromise;
    const videosCopy = form.values.videos;

    files.forEach(file => {
      videosCopy.push({
        video: generateVideoObjectFromUrl(file, '', true),
        products: [],
        active: true,
      });
    });

    form.setFieldValue('videos', videosCopy);
  };

  return (
    <BottomModal
      closeModal={closeModal}
      title="Create Product Highlight"
      useChildrenAsDirectChild
      onModalHide={() => form.resetForm()}
      // enableDynamicSizing
      customSnapPoints={[85]}
      buttons={[
        {
          text: isEdit ? 'Update highlight' : 'Save highlight',
          onPress: () => form.handleSubmit(),
          isLoading: createHighlightReq.isLoading || updateHighlightReq.isLoading,
        },
      ]}
      {...props}>
      <BottomSheetScrollView className="flex-1" style={{ paddingHorizontal: wp(20) }}>
        <Input
          label="Highlight Title"
          autoFocus
          useBottomSheetInput
          autoCapitalize="none"
          containerClasses="mt-15"
          {...getFieldvalues('title', form)}
        />
        <View>
          <ManageHighlightVideos
            ref={manageVideosRef}
            videos={form.values.videos.map(v => v.video)}
            isEdit={isEdit}
            saveVideos={handleSaveVideos}
            removeProductVideo={handleRemoveVideo}
            updateHighlights={highlights => form.setFieldValue('videos', highlights)}
            highlights={form.values.videos}
          />
          <Button
            text="+ Add New Video"
            variant={ButtonVariant.LIGHT}
            onPress={() => dropDownRef.current?.open()}
            className="mt-15"
          />
        </View>
        <View className="h-80" />
      </BottomSheetScrollView>
      <SelectDropdown
        ref={dropDownRef}
        showAnchor={false}
        items={addVideoOptions}
        label="Select Highlight Type"
        containerClasses="mt-15"
        onPressItem={v => handleUploadMethodSelect(v as UPLOAD_METHOD)}
        showRadio={false}
      />
      {modals.videoFromProduct && (
        <SelectSpecificProductsModal
          products={items}
          isVisible={modals.videoFromProduct}
          closeModal={() => toggleModal('videoFromProduct', false)}
          getProductsRequest={fetchItemsReq}
          selectedProducts={selectedProducts}
          setSelectedProducts={setSelectedProducts}
          onPressContinue={() => {
            extractFormVideosFromProducts();
            toggleModal('videoFromProduct', false);
          }}
        />
      )}

      <InstagramImportManager
        showManagerView={modals.instagram}
        showWebView={modals.webview}
        toggleManagerView={(s?: boolean) => toggleModal('instagram', s)}
        toggleWebView={(s?: boolean) => toggleModal('webview', s)}
        switchModal={() => switchModals('instagram', 'webview')}
        type="SELECT"
        selectedMedia={selectedInstagramMedia}
        setSelectedMedia={setSelectedInstagramMedia}
        onImportComplete={() => handleCompleteInstagramImport()}
      />
      <InstagramImportProgress
        show={modals.progress}
        toggle={() => toggleModal('progress')}
        stepCount={selectedInstagramMedia?.length}
      />
    </BottomModal>
  );
};

export default AddProductHighlightModal;

const addVideoOptions: DropDownItem[] = [
  {
    label: 'Upload Manually',
    value: UPLOAD_METHOD.MANUALLY,
    leftElement: (
      <CircledIcon className="p-8 bg-accentOrange-pastel">
        <DocumentUpload size={wp(16)} color={colors.accentOrange.main} />
      </CircledIcon>
    ),
  },
  {
    label: 'Select Products With Videos',
    value: UPLOAD_METHOD.SELECT_PRODUCTS,
    leftElement: (
      <CircledIcon className="p-8 bg-primary-pastel">
        <AddCircle size={wp(16)} color={colors.primary.main} />
      </CircledIcon>
    ),
  },
  {
    label: 'Upload from Instagram',
    value: UPLOAD_METHOD.INSTAGRAM,
    leftElement: (
      <CircledIcon className="p-8 bg-accentRed-pastel">
        <Instagram size={wp(16)} color={colors.accentRed.main} />
      </CircledIcon>
    ),
  },
];

const validationSchema = Yup.object({
  title: Yup.string().required('Please provide highlight title'),
  videos: Yup.array().of(
    Yup.object({
      products: Yup.array()
        .of(Yup.string().required())
        .min(1, 'Please select at least one product')
        .required('Please select at least one product'),
    }),
  ),
});

const ensureProductsAreStrings = (products: any[]) => {
  return typeof products[0] === 'string' ? products : products.map(p => p.id);
};

const getInitialValues = (highlight: HighlightInterface) => {
  return {
    title: highlight?.title,
    videos: highlight?.videos.map(v => ({
      video: generateVideoObjectFromUrl(v.url, v.thumbnail),
      products: ensureProductsAreStrings(v.products),
      thumbnail: v.thumbnail,
      active: v.active,
    })),
    active: highlight.active,
    id: highlight.id,
  };
};

const generateVideoObjectFromUrl = (url: string, thumbnail: string, isInstagram?: boolean) => {
  const videoFileName = url?.split?.('/')?.pop() ?? '';
  const thumbnailFileName = thumbnail?.split?.('/')?.pop() ?? '-';

  return {
    src: url,
    name: videoFileName,
    lastModified: null,
    file: {
      name: videoFileName,
      lastModified: null,
    } as any,
    isUploading: false,
    uploadProgress: isInstagram ? 0 : 100,
    url: isInstagram ? null : url,
    error: false,
    key: null,
    thumbnail: thumbnail,
    type: MediaType.VIDEO,
    meta: {
      id: generateSimpleUUID(),
      thumbnail: {
        src: thumbnail,
        name: thumbnailFileName,
        lastModified: null,
        file: {
          name: thumbnailFileName,
          lastModified: null,
        } as any,
        isUploading: false,
        uploadProgress: 100,
        url: thumbnail,
        error: false,
        key: null,
      },
    },
  };
};
