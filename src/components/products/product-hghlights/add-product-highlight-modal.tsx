import {
  Add<PERSON>ircle,
  CloseCircle,
  Copy,
  DocumentUpload,
  InfoCircle,
  Instagram,
  TickCircle,
} from 'iconsax-react-native/src';
import { ActivityIndicator, ScrollView, View } from 'react-native';
import {
  cx,
  delay,
  generateSimpleUUID,
  getFieldvalues,
  hideLoader,
  hp,
  showError,
  showLoader,
  showSuccess,
  toCurrency,
  wp,
  Yup,
} from 'src/assets/utils/js';
import { BaseText, CircledIcon, Row, SelectionPill } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import SheetModal, { SheetModalProps } from 'src/components/ui/modals/sheet-modal';
import SectionContainer from 'src/components/ui/section-container';
import colors from 'src/theme/colors';
import Input from 'src/components/ui/inputs/input';
import But<PERSON>, { ButtonSize, ButtonVariant } from 'src/components/ui/buttons/button';
import { BottomSheetModalProvider, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { useApi } from 'src/hooks/use-api';
import {
  ADD_DOMAIN,
  CHECK_DOMAIN,
  CREATE_HIGHLIGHT,
  CreateHighlightParams,
  HighlightInterface,
  UPDATE_HIGHLIGHT,
  UpdateHighlightParams,
  Video,
} from 'catlog-shared';
import { useEffect, useMemo, useRef, useState } from 'react';
import useStatusbar from 'src/hooks/use-statusbar';
import Shimmer from 'src/components/ui/shimmer';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import SelectDropdown, { DropDownItem, DropDownMethods } from 'src/components/ui/inputs/select-dropdown';
import { useFormik } from 'node_modules/formik/dist';

interface FormProps {
  title: string;
  videos: { video: Video; active?: boolean; products: string[] }[];
  active?: boolean;
  id: string;
}

interface Props extends Partial<SheetModalProps> {
  closeModal: () => void;
  isEdit?: boolean;
  highlightToEdit?: HighlightInterface;
  onSuccess: (highlight: HighlightInterface) => void;
}

const AddProductHighlightModal = ({ closeModal, isEdit, highlightToEdit, onSuccess, ...props }: Props) => {
  const dropDownRef = useRef<DropDownMethods>(null);
  const { setStatusBarStyle } = useStatusbar();
  setStatusBarStyle(props.visible ? 'light' : 'dark', true);

  const createHighlightReq = useApi<CreateHighlightParams>({
    apiFunction: CREATE_HIGHLIGHT,
    method: 'POST',
    key: CREATE_HIGHLIGHT.name,
  });
  const updateHighlightReq = useApi<UpdateHighlightParams>({
    apiFunction: UPDATE_HIGHLIGHT,
    method: 'PUT',
    key: UPDATE_HIGHLIGHT.name,
  });

  const form = useFormik<FormProps>({
    initialValues: isEdit
      ? getInitialValues(highlightToEdit)
      : {
          title: '',
          videos: [],
          active: true,
          id: generateSimpleUUID(),
        },
    validationSchema,
    onSubmit: async values => {
      let result: any[];

      if (isEdit) {
        result = await updateHighlightReq.makeRequest({
          id: values.id,
          title: values.title,
          active: values.active,
          videos: values.videos.map(v => ({
            url: v.video.url,
            active: v.active,
            thumbnail: v.video.thumbnail,
            products: v.products,
          })),
        });
      } else {
        result = await createHighlightReq.makeRequest({
          title: values.title,
          videos: values.videos.map(v => ({
            url: v.video.url,
            active: true,
            thumbnail: v.video.thumbnail,
            products: v.products,
          })),
        });
      }

      const [response, error] = result;

      if (error) {
        showError(error?.message ?? `Couldn't ${isEdit ? 'update' : 'create'} highlight`);
      } else {
        onSuccess(response.data);
      }
    },
  });

  const addVideoOptions: DropDownItem[] = [
    {
      label: 'Upload Manually',
      value: 'Upload Manually',
      onPress: () => {},
      leftElement: (
        <CircledIcon className="p-8 bg-accentOrange-pastel">
          <DocumentUpload size={wp(16)} color={colors.accentOrange.main} />
        </CircledIcon>
      ),
    },
    {
      label: 'Select Products With Videos',
      value: 'Select Products With Videos',
      onPress: () => {},
      leftElement: (
        <CircledIcon className="p-8 bg-primary-pastel">
          <AddCircle size={wp(16)} color={colors.primary.main} />
        </CircledIcon>
      ),
    },
    {
      label: 'Upload from Instagram',
      value: 'Upload from Instagram',
      onPress: () => {},
      leftElement: (
        <CircledIcon className="p-8 bg-accentRed-pastel">
          <Instagram size={wp(16)} color={colors.accentRed.main} />
        </CircledIcon>
      ),
    },
  ];

  return (
    <SheetModal closeModal={closeModal} title="Create Product Highlight" {...props}>
      <ScrollView className="flex-1" style={{ paddingHorizontal: wp(20) }}>
        <Input
          label="Highlight Title"
          autoFocus
          autoCapitalize="none"
          containerClasses="mt-15"
          {...getFieldvalues('title', form)}
        />
        <View className="mt-15">
          <Button text="+ Add New Video" variant={ButtonVariant.LIGHT} onPress={() => dropDownRef.current?.open()} />
          {/* <VideoCard /> */}
        </View>
        <View className="h-80" />
      </ScrollView>
      <FixedBtnFooter
        buttons={[
          {
            text: 'Continue',
            onPress: () => {},
          },
        ]}
      />
      <SelectDropdown
        ref={dropDownRef}
        showAnchor={false}
        items={addVideoOptions}
        label="Select Highlight Type"
        containerClasses="mt-15"
        onPressItem={() => null}
        showRadio={false}
      />
    </SheetModal>
  );
};

export default AddProductHighlightModal;

const VideoCard = () => {
  return (
    <View className="bg-grey-bgOne rounded-12">
      <View className="p-12">
        <BaseText weight="medium" classes="text-black-placeholder">
          Upload Video file
        </BaseText>
      </View>
      <View className="bg-white border border-grey-border p-12">
        <Pressable className="border border-grey-border rounded-8 border-dashed items-center justify-center h-[155px]">
          <CircledIcon className="p-10 bg-grey-bgOne">
            <DocumentUpload size={wp(26)} variant="Bold" color={colors.black.muted} />
          </CircledIcon>
          <BaseText weight="medium" classes="text-primary-main underline mt-12">
            Choose a file
          </BaseText>
          <BaseText fontSize={12} classes="text-black-placeholder mt-5">
            MP4, MOV, HEIC (Max size - 15mb)
          </BaseText>
        </Pressable>
      </View>
    </View>
  );
};

const validationSchema = Yup.object({
  title: Yup.string().required("Please provide highlight title"),
  videos: Yup.array().of(
    Yup.object({
      products: Yup.array()
        .of(Yup.string().required())
        .min(1, "Please select at least one product")
        .required("Please select at least one product"),
    })
  ),
});

const ensureProductsAreStrings = (products: any[]) => {
  return typeof products[0] === "string" ? products : products.map((p) => p.id);
};

const getInitialValues = (highlight: HighlightInterface) => {
  return {
    title: highlight?.title,
    videos: highlight?.videos.map((v) => ({
      video: generateVideoObjectFromUrl(v.url, v.thumbnail),
      products: ensureProductsAreStrings(v.products),
      thumbnail: v.thumbnail,
      active: v.active,
    })),
    active: highlight.active,
    id: highlight.id,
  };
};



const generateVideoObjectFromUrl = (url: string, thumbnail: string) => {
  const videoFileName = url?.split?.("/")?.pop() ?? '';
  const thumbnailFileName = thumbnail?.split?.("/")?.pop() ?? '-';

  return {
    src: url,
    name: videoFileName,
    lastModified: null,
    file: {
      name: videoFileName,
      lastModified: null,
    } as any,
    isUploading: false,
    uploadProgress: 100,
    url: url,
    error: false,
    key: null,
    thumbnail: thumbnail,
    meta: {
      id: generateSimpleUUID(),
      thumbnail: {
        src: thumbnail,
        name: thumbnailFileName,
        lastModified: null,
        file: {
          name: thumbnailFileName,
          lastModified: null,
        } as any,
        isUploading: false,
        uploadProgress: 100,
        url: thumbnail,
        error: false,
        key: null,
      },
    },
  };
};
