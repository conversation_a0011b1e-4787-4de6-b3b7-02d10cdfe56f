import React, { Fragment, useContext, useEffect, useState } from 'react';
import { useApi } from 'src/hooks/use-api';
import { InstagramWebView } from './instagram-webview';
import ConnectToInstagramScreen from './connect-screen';
import BottomModal from '@/components/ui/modals/bottom-modal';
import useAuthContext from 'src/contexts/auth/auth-context';
import { ImportForm, InstagramMedia, SelectInstagramPosts } from './select-products';
import { useFormik } from 'formik';
import { ActivityIndicator, Dimensions, View } from 'react-native';
import useProgress from 'src/hooks/useProgess';
import { FlipType, manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import { Image } from 'src/@types/utils';
import Toast from 'react-native-toast-message';
import useImageUploads from 'src/hooks/use-file-uploads';
import ProgressModal from '@/components/ui/progress-modal';
import { Product } from '../../create-products/types';
import { useLocalObject } from 'src/hooks/useLocalState';
import {
  CHECK_INSTAGRAM_TOKEN,
  GENERATE_INSTAGRAM_ACCESS_TOKEN,
  GET_MULTIPLE_INSTAGRAM_ALBUM_MEDIA,
  GET_PRODUCT_DETAILS_FROM_MULTIPLE_CAPTIONS,
  GetInstagramAccessTokenParams,
  GetMultipleInstagramAlbumMediaParams,
  GetProductDetailsFromMultipleCaptionsParams,
  ProductCreateMethod,
  getRandString,
  ProductItemInterface,
} from 'catlog-shared';
import { delay, hp } from 'src/assets/utils/js';
import BaseText from 'src/components/ui/base/base-text';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Modal from 'react-native-modal';

const redirect_uri = `https://app.catlog.shop/products/create`;

interface Props {
  showWebView: boolean;
  toggleWebView: (s?: boolean) => void;
  showManagerView: boolean;
  toggleManagerView: (s?: boolean) => void;
  switchModal: VoidFunction;
  onImportComplete: (products: Product[]) => void;
}
const InstagramImportManager: React.FC<Props> = ({
  showManagerView,
  toggleManagerView,
  toggleWebView,
  showWebView,
  switchModal,
  onImportComplete,
}) => {
  const { user } = useAuthContext();
  const [step, setStep] = useState<'connect' | 'select' | 'import'>('connect');

  const tokenRequest = useApi<GetInstagramAccessTokenParams>({
    apiFunction: GENERATE_INSTAGRAM_ACCESS_TOKEN,
    method: 'GET',
    key: 'get-instagram-token',
    autoRequest: false,
  });

  const checkTokenRequest = useApi<GetInstagramAccessTokenParams>({
    apiFunction: CHECK_INSTAGRAM_TOKEN,
    method: 'GET',
    key: 'check-instagram-token',
  });

  const isLoading = checkTokenRequest.isLoading || tokenRequest.isLoading;

  useEffect(() => {
    if (checkTokenRequest.error) {
      setStep('connect');
    }

    if (checkTokenRequest.response && !checkTokenRequest.error) {
      setStep('select');
    }
  }, [checkTokenRequest]); //Todo: @kayode i'm wondering why we're checking "user" here, also checking just .response means that when .error changes we won't know

  const onInstagramAuthComplete = async (code: string) => {
    toggleWebView(false);
    const [res, err] = await tokenRequest.makeRequest({ access_code: code, redirect_uri });
    if (res) {
      setStep('select');
      setTimeout(() => {
        toggleManagerView(true);
      }, 100);
      await checkTokenRequest.refetch();
    }

    if (err) {
      console.log(err);
    }
  };

  if (isLoading) return <Loader show={isLoading && showManagerView} />;

  return (
    <>
      <InstagramWebView onComplete={onInstagramAuthComplete} show={showWebView} toggle={toggleWebView} />
      <ImportInstagramProducts
        step={step}
        setStep={setStep}
        show={showManagerView}
        toggle={toggleManagerView}
        toggleWebView={switchModal}
        onImportComplete={onImportComplete}
      />
    </>
  );
};

export default InstagramImportManager;

interface ImportInstagramProductsProps {
  show: boolean;
  toggle: (s?: boolean) => void;
  toggleWebView: (s?: boolean) => void;
  step: 'connect' | 'select' | 'import';
  setStep: (step: 'connect' | 'select' | 'import') => void;
  onImportComplete: (products: Product[]) => void;
}
export const ImportInstagramProducts: React.FC<ImportInstagramProductsProps> = ({
  show,
  step,
  setStep,
  toggle,
  toggleWebView,
  onImportComplete,
}) => {
  const progressSteps = useProgress(instagramProgressSteps);
  const [images, setImages] = useState<Image[]>([]);
  const [captionMap, setCaptionMap] = useState<{ [key: string]: string }>({});
  const [mediaCache, setMediaCache] = useLocalObject<{ [key: string]: string }>('instagram-media-cache');

  useImageUploads(images, setImages);

  useEffect(() => {
    if (progressSteps.currentKey === 'GENERATE') {
      processCreateProductsFromImagesStep();
    }
  }, [progressSteps.currentKey]);

  useEffect(() => {
    if (progressSteps.currentKey === 'UPLOADING') {
      const allImagesUploaded = images.every(i => i.uploadProgress === 100 && !i.isUploading) && images.length > 0;
      if (allImagesUploaded) {
        setMediaCache({
          ...mediaCache,
          ...images.reduce((p, c) => {
            p[c.key] = c.url;
            return p;
          }, {} as any),
        });
        progressSteps.nextStep();
      } else {
        const imageUploadProgress =
          images.reduce((prev, cur) => {
            return prev + (cur.uploadProgress || 0);
          }, 0) / images.length;
        progressSteps.setStepProgress(imageUploadProgress);
      }
    }
  }, [images, progressSteps.currentKey]);
  // Todo: @kayode i think this useEffect and the one before can be combined into one useEffect

  const getAlbumMediaRequest = useApi<GetMultipleInstagramAlbumMediaParams>({
    apiFunction: GET_MULTIPLE_INSTAGRAM_ALBUM_MEDIA,
    method: 'POST',
    key: 'get-multiple-album-media',
    autoRequest: false,
  });

  const getProductDetailsRequest = useApi<GetProductDetailsFromMultipleCaptionsParams>({
    apiFunction: GET_PRODUCT_DETAILS_FROM_MULTIPLE_CAPTIONS,
    method: 'POST',
    key: 'get-product-details-from-captions',
    autoRequest: false,
  });

  const form = useFormik<ImportForm>({
    initialValues: {
      selected_posts: {},
      autogenerate_details: true,
    },
    onSubmit: () => {},
    validationSchema: null,
  });
  // Todo: @kayode ideally state definitions should come before the useEffects, so we need to move getAlbumMediaRequest, getProductDetailsRequest & form to the top

  const processCreateProductsFromImagesStep = async () => {
    const captions: { key: string; caption: string }[] = Object.keys(captionMap).map(key => ({
      key,
      caption: captionMap[key]?.replace(/(\r\n|\n|\r)/gm, '') ?? '',
    }));
    const products: Product[] = [];
    let productData: { price: string; name: string; description: string; variants?: any[]; variants_type?: string };

    if (captions.length > 0) {
      const [res, error] = await getProductDetailsRequest.makeRequest({ captions });
      const data = res?.data;

      if (error) {
        Toast.show({ type: 'error', text1: 'Something went wrong!', text2: "Couldn't load some images" });
      } else if (Array.isArray(data) && data.length > 0) {
        data.forEach((result, i) => {
          const progress = 100.0 * ((i + 1) / data.length);
          productData = result?.data;

          const price = cleanUpNumberString(productData?.price);

          const imgs = images.filter(i => i.meta?.postId == result?.key);

          products.push({
            images: imgs,
            name: productData?.name ?? '',
            price: isNaN(price) ? '0' : String(price),
            category: '',
            description: productData?.description ?? '',
            thumbnail: 0,
            price_unit: '',
            variants: {
              type: 'custom',
              is_template: false,
              options: [],
            },
            upload_source: ProductCreateMethod.INSTAGRAM,
          });
          progressSteps.setStepProgress(progress);
        });
      }
    }
    toggle(false);
    setStep('select');
    onImportComplete(products);
    progressSteps.reset();
  };

  const processImportImagesAndCaptionsStep = async () => {
    const posts = Object.values(form.values.selected_posts);
    const images: Image[] = [];
    const captions = {} as any;
    const carouselPosts: string[] = [];

    for (const post of posts) {
      captions[post.id] = post.caption;
      const meta = { postId: post.id };
      if (post.media_type === 'CAROUSEL_ALBUM') {
        carouselPosts.push(post.id);
      } else {
        const cachedUrl = mediaCache?.[post.id];
        const mediaImage = await resizeImageAndGetFilePath(
          post.thumbnail_url ?? post.media_url,
          post.id,
          cachedUrl,
          meta,
        );
        images.push(mediaImage);
      }
    }

    if (carouselPosts.length > 0) {
      const [res, err] = await getAlbumMediaRequest.makeRequest({ media_ids: carouselPosts });
      if (err) {
        Toast.show({ type: 'error', text1: 'Something went wrong!', text2: "Couldn't load some images" });
      } else {
        const medias = res?.data as InstagramMedia[];

        for (const m of medias) {
          const image = await resizeImageAndGetFilePath(m?.thumbnail_url ?? m.media_url, m.id, undefined, {
            postId: m?.post_id,
          });
          images.push(image);
        }
      }
    }
    setImages(images);
    setCaptionMap(captions);
  };

  const handleSelectComplete = async () => {
    toggle(false);
    await delay(700);
    setStep('import');
    progressSteps.setStepIsLoading();
    // return;
    await processImportImagesAndCaptionsStep();
    progressSteps.nextStep();
  };

  const handleOpenConnectAccountModal = async () => {
    await delay(500);
    toggleWebView();
  };

  return (
    <Fragment>
      {step === 'connect' && (
        <ConnectToInstagramScreen {...{ show, toggle }} toggleWebView={handleOpenConnectAccountModal} />
      )}
      {step === 'select' && (
        <BottomModal
          showButton
          isVisible={show}
          useChildrenAsDirectChild
          // enableDynamicSizing
          customSnapPoints={[95]}
          title="Select posts to upload as products"
          buttons={[
            {
              text: 'Proceed',
              onPress: handleSelectComplete,
              isLoading: false,
              disabled: Object.keys(form.values?.selected_posts)?.length === 0,
            },
          ]}
          closeModal={() => toggle(false)}>
          <SelectInstagramPosts form={form} maxUploadable={10} />
        </BottomModal>
      )}
      {step === 'import' && (
        <ProgressModal
          show={step === 'import'}
          toggle={toggle}
          currentStep={progressSteps.currentStep}
          steps={progressSteps.steps}
        />
      )}
    </Fragment>
  );
};

const instagramProgressSteps = [
  { key: 'IMPORT', label: 'Importing images from instagram...', isLoading: true, complete: false },
  { key: 'UPLOADING', label: 'Uploading images to our servers...', progress: 0 },
  { key: 'GENERATE', label: 'Generating product details...', progress: 0 },
];

async function resizeImageAndGetFilePath(src: string, key: string, url?: string, meta?: any): Promise<Image> {
  const isUploaded = url !== undefined;

  const { uri } = await manipulateAsync(src, [{ resize: { width: 1080 } }], {
    compress: 0.5,
    format: SaveFormat.JPEG,
    base64: true,
  });

  return {
    file: null,
    url: isUploaded ? url : undefined,
    key,
    isUploading: false,
    uploadProgress: isUploaded ? 100 : 0,
    meta,
    name: getRandString(10),
    src: uri,
  };
}

function cleanUpNumberString(number: any) {
  return Number(String(number).replace(',', ''));
}

const Loader = ({ show }: { show: boolean }) => {
  const insertTop = useSafeAreaInsets().top + hp(12);
  const translateX = useSharedValue(-300);

  useEffect(() => {
    translateX.value = withRepeat(
      withTiming(300, {
        duration: 700,
        easing: Easing.inOut(Easing.ease),
      }),
      -1,
      true,
    );
  }, []);

  // Create the animated style for the loading bar
  const progressStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
    };
  });

  return (
    <Modal
      isVisible={show}
      backdropColor={'#1E1E1E80'}
      animationIn={'fadeInDown'}
      animationOut={'fadeOutUp'}
      style={[{ flex: 1, justifyContent: 'flex-start', margin: 0, padding: 0 }]}>
      <View
        style={[{ justifyContent: 'flex-end', marginTop: insertTop }]}
        className="bg-white mx-20 rounded-12 px-20 py-20">
        <BaseText fontSize={15} type="heading">
          Loading...
        </BaseText>
        <View className="w-full bg-grey-bgTwo rounded-full overflow-hidden mt-15">
          <Animated.View className={`rounded-full h-6 py-1 bg-primary-main`} style={progressStyle}></Animated.View>
        </View>
      </View>
    </Modal>
  );
};
