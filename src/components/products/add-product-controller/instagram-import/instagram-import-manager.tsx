import React, { Fragment, useContext, useEffect, useState } from 'react';
import { useApi } from 'src/hooks/use-api';
import { InstagramWebView } from './instagram-webview';
import ConnectToInstagramScreen from './connect-screen';
import BottomModal from '@/components/ui/modals/bottom-modal';
import useAuthContext from 'src/contexts/auth/auth-context';
import { ImportForm, InstagramMedia, SelectInstagramPosts } from './select-products';
import { useFormik } from 'formik';
import { ActivityIndicator, Dimensions, View } from 'react-native';
import useProgress from 'src/hooks/useProgess';
import { FlipType, manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import { AppMediaType, Image } from 'src/@types/utils';
import Toast from 'react-native-toast-message';
import useImageUploads from 'src/hooks/use-file-uploads';
import ProgressModal from '@/components/ui/progress-modal';
import { Product } from '../../create-products/types';
import { useLocalObject } from 'src/hooks/useLocalState';
import {
  CHECK_INSTAGRAM_TOKEN,
  GENERATE_INSTAGRAM_ACCESS_TOKEN,
  GET_MULTIPLE_INSTAGRAM_ALBUM_MEDIA,
  GET_PRODUCT_DETAILS_FROM_MULTIPLE_CAPTIONS,
  GetInstagramAccessTokenParams,
  GetMultipleInstagramAlbumMediaParams,
  GetProductDetailsFromMultipleCaptionsParams,
  ProductCreateMethod,
  getRandString,
  ProductItemInterface,
  MediaType,
  Media,
} from 'catlog-shared';
import { delay, generateSimpleUUID, hp, showError } from 'src/assets/utils/js';
import BaseText from 'src/components/ui/base/base-text';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Modal from 'react-native-modal';
import { useVideoDownload } from 'src/hooks/use-download-video';
import { SelectInstagramMedias } from './select-medias';

const redirect_uri = `https://app.catlog.shop/products/create`;

interface Props {
  showWebView: boolean;
  toggleWebView: (s?: boolean) => void;
  showManagerView: boolean;
  toggleManagerView: (s?: boolean) => void;
  switchModal: VoidFunction;
  onImportComplete?: (products: Product[]) => void;
  medias?: AppMediaType[];
  setMedias?: React.Dispatch<React.SetStateAction<AppMediaType[]>>;
  type?: 'IMPORT' | 'SELECT';
  selectedMedia?: string[];
  setSelectedMedia?: React.Dispatch<React.SetStateAction<string[]>>;
}
const InstagramImportManager: React.FC<Props> = ({
  showManagerView,
  toggleManagerView,
  toggleWebView,
  showWebView,
  switchModal,
  onImportComplete,
  medias,
  setMedias,
  type = 'IMPORT',
  selectedMedia,
  setSelectedMedia,
}) => {
  const { user } = useAuthContext();
  const [step, setStep] = useState<'connect' | 'select' | 'import'>('connect');

  const tokenRequest = useApi<GetInstagramAccessTokenParams>({
    apiFunction: GENERATE_INSTAGRAM_ACCESS_TOKEN,
    method: 'GET',
    key: 'get-instagram-token',
    autoRequest: false,
  });

  const checkTokenRequest = useApi<GetInstagramAccessTokenParams>({
    apiFunction: CHECK_INSTAGRAM_TOKEN,
    method: 'GET',
    key: 'check-instagram-token',
  });

  const isLoading = checkTokenRequest.isLoading || tokenRequest.isLoading;

  useEffect(() => {
    if (checkTokenRequest.error) {
      setStep('connect');
    }

    if (checkTokenRequest.response && !checkTokenRequest.error) {
      console.log('checkTokenRequest.response && !checkTokenRequest.error');
      setStep('select');
    }
  }, [checkTokenRequest.response, checkTokenRequest.error]); //Todo: @kayode i'm wondering why we're checking "user" here, also checking just .response means that when .error changes we won't know

  const onInstagramAuthComplete = async (code: string) => {
    toggleWebView(false);
    const [res, err] = await tokenRequest.makeRequest({ access_code: code, redirect_uri });
    if (res) {
      setStep('select');
      setTimeout(() => {
        toggleManagerView(true);
      }, 100);
      await checkTokenRequest.refetch();
    }

    if (err) {
      console.log(err);
    }
  };

  if (isLoading) return <Loader show={isLoading && showManagerView} />;

  return (
    <>
      <InstagramWebView onComplete={onInstagramAuthComplete} show={showWebView} toggle={toggleWebView} />
      <ImportInstagramProducts
        step={step}
        setStep={setStep}
        show={showManagerView}
        toggle={toggleManagerView}
        toggleWebView={switchModal}
        onImportComplete={onImportComplete}
        medias={medias}
        setMedias={setMedias}
        type={type}
        selectedMedia={selectedMedia}
        setSelectedMedia={setSelectedMedia}
      />
    </>
  );
};

export default InstagramImportManager;

interface ImportInstagramProductsProps {
  show: boolean;
  toggle: (s?: boolean) => void;
  toggleWebView: (s?: boolean) => void;
  step: 'connect' | 'select' | 'import';
  setStep: (step: 'connect' | 'select' | 'import') => void;
  onImportComplete: (products: Product[]) => void;
  medias: AppMediaType[];
  setMedias: React.Dispatch<React.SetStateAction<AppMediaType[]>>;
  type?: 'IMPORT' | 'SELECT';
  selectedMedia?: string[];
  setSelectedMedia?: React.Dispatch<React.SetStateAction<string[]>>;
}
export const ImportInstagramProducts: React.FC<ImportInstagramProductsProps> = ({
  show,
  step,
  medias = [],
  setMedias = () => {},
  setStep,
  toggle,
  toggleWebView,
  onImportComplete,
  type = 'IMPORT',
  selectedMedia = [],
  setSelectedMedia = () => {},
}) => {
  const progressSteps = useProgress(instagramProgressSteps);
  // const [images, setImages] = useState<Image[]>([]);
  const [videosMap, setVideosMap] = useState<{ [key: string]: string[] }>({});
  const [captionMap, setCaptionMap] = useState<{ [key: string]: string }>({});
  const [mediaCache, setMediaCache] = useLocalObject<{ [key: string]: string }>('instagram-media-cache');

  useImageUploads(medias, setMedias);

  useEffect(() => {
    if (progressSteps.currentKey === 'GENERATE') {
      processCreateProductsFromImagesStep();
    }
  }, [progressSteps.currentKey]);

  useEffect(() => {
    if (progressSteps.currentKey === 'UPLOADING') {
      const imgs = medias.filter(i => i.type === MediaType.IMAGE) ?? [];
      const allImagesUploaded = imgs?.every(i => i.uploadProgress === 100 && !i.isUploading) && imgs.length > 0;
      const hasVideos = Object.keys(videosMap ?? {})?.length > 0;
      const canGoNext = allImagesUploaded || (imgs?.length === 0 && hasVideos);

      if (canGoNext) {
        setMediaCache({
          ...mediaCache,
          ...imgs.reduce((p, c) => {
            p[c.key] = c.url;
            return p;
          }, {} as any),
        });
        progressSteps.nextStep();
      } else {
        const imageUploadProgress =
          imgs.reduce((prev, cur) => {
            return prev + (cur.uploadProgress || 0);
          }, 0) / imgs.length;
        progressSteps.setStepProgress(imageUploadProgress);
      }
    }
  }, [medias, videosMap, progressSteps.currentKey]);
  // Todo: @kayode i think this useEffect and the one before can be combined into one useEffect

  const getAlbumMediaRequest = useApi<GetMultipleInstagramAlbumMediaParams>({
    apiFunction: GET_MULTIPLE_INSTAGRAM_ALBUM_MEDIA,
    method: 'POST',
    key: 'get-multiple-album-media',
    autoRequest: false,
  });

  const getProductDetailsRequest = useApi<GetProductDetailsFromMultipleCaptionsParams>({
    apiFunction: GET_PRODUCT_DETAILS_FROM_MULTIPLE_CAPTIONS,
    method: 'POST',
    key: 'get-product-details-from-captions',
    autoRequest: false,
  });

  const { downloadVideo } = useVideoDownload();

  const handleDownload = async (url: string, filename: string) => {
    try {
      console.log('Started download: ', url);
      const download = await downloadVideo(url, filename);
      return download;
    } catch (err) {
      throw new Error(err);
    }
  };

  const form = useFormik<ImportForm>({
    initialValues: {
      selected_posts: {},
      autogenerate_details: true,
    },
    onSubmit: () => {},
    validationSchema: null,
  });
  // Todo: @kayode ideally state definitions should come before the useEffects, so we need to move getAlbumMediaRequest, getProductDetailsRequest & form to the top

  const processCreateProductsFromImagesStep = async () => {
    try {
      const captions: { key: string; caption: string }[] = Object.keys(captionMap).map(key => ({
        key,
        caption: captionMap[key]?.replace(/(\r\n|\n|\r)/gm, '') ?? '',
      }));
      const products: Product[] = [];
      let productData: { price: string; name: string; description: string; variants?: any[]; variants_type?: string };

      if (captions.length > 0) {
        const [res, error] = await getProductDetailsRequest.makeRequest({ captions });
        const data = res?.data;

        const videoFileMap: {
          [key: string]: {
            type: 'mp4' | 'mov' | 'avi' | 'mkv';
            uri: string;
            size: number;
            mimeType: string;
          }[];
        } = {};
        const videoKeys = Object.keys(videosMap);

        if (videoKeys.length > 0) {
          for (let i = 0; i < videoKeys.length; i++) {
            const k = videoKeys[i];
            const progress = 50.0 * ((i + 1) / videoKeys.length);
            const urls = videosMap[k];

            // const files = await Promise.all(
            //   urls.map(async url => {
            //     const blob = await fetch(EXPO_PUBLIC_API_URL + `/utils/download-file?url=${url}`, {
            //       mode: 'cors',
            //     }).then(r => r.blob());
            //     const file = new File([blob], `video-${k}.mp4`, { type: 'video/mp4' });
            //     return file;
            //   }),
            // );

            const files = await Promise.all(urls.map(async url => handleDownload(url, `video-${k}.mp4`)));

            console.log('files: ', files);

            videoFileMap[k] = [...(videoFileMap[k] ?? []), ...files];
            // setStepProgress(progress, currentStep);
          }
        }

        if (error) {
          Toast.show({ type: 'error', text1: 'Something went wrong!', text2: "Couldn't load some images" });
        } else if (Array.isArray(data) && data.length > 0) {
          const imgs = medias.filter(i => i.type === MediaType.IMAGE);

          data.forEach((result, i) => {
            const progress = 100.0 * ((i + 1) / data.length);
            productData = result?.data;
            const price = cleanUpNumberString(productData?.price);
            const videoFiles = videoFileMap[result?.key] ?? [];

            console.log('videoFiles: ', videoFiles);

            const currentTime = new Date().getTime();

            products.push({
              images: [...imgs.filter(i => i.meta?.postId == result?.key)] as Image[],
              name: productData?.name ?? '',
              price: isNaN(price) ? '0' : String(price),
              category: '',
              description: productData?.description ?? '',
              thumbnail: 0,
              price_unit: '',
              variants: {
                type: 'custom',
                is_template: false,
                options: [],
              },
              upload_source: ProductCreateMethod.INSTAGRAM,
              videos: videoFiles.map(f => ({
                name: currentTime.toString(),
                key: currentTime.toString(),
                lastModified: null,
                type: MediaType.VIDEO,
                file: null,
                url: null,
                src: f.uri,
                fileSize: f.size,
                thumbnail: null,
                isUploading: false,
                meta: {
                  id: generateSimpleUUID(),
                  thumbnail: {
                    src: null,
                    name: '',
                    lastModified: null,
                    file: null,
                    isUploading: false,
                    uploadProgress: 0,
                    url: null,
                    error: false,
                    key: '',
                  },
                },
                uploadProgress: 0,
              })),
            });

            console.log('products: ', JSON.stringify(products));

            progressSteps.setStepProgress(progress);
          });
        }
      }
      toggle(false);
      setStep('select');
      onImportComplete(products);
      progressSteps.reset();
    } catch (error) {
      showError(error);
    }
  };

  const processImportImagesAndCaptionsStep = async () => {
    const posts = Object.values(form.values.selected_posts);
    const images: Partial<AppMediaType>[] = [];
    const videos: { [key: string]: string[] } = {};
    const captions = {} as any;
    const carouselPosts: string[] = [];

    for (const post of posts) {
      captions[post.id] = post.caption;
      const meta = { postId: post.id };
      if (post.media_type === 'CAROUSEL_ALBUM') {
        carouselPosts.push(post.id);
      } else {
        if (post.media_type === 'VIDEO') {
          videos[post.id] = [...(videos[post.id] ?? []), post.media_url];
        } else {
          const cachedUrl = mediaCache?.[post.id];
          const mediaImage = await resizeImageAndGetFilePath(
            post.thumbnail_url ?? post.media_url,
            post.id,
            cachedUrl,
            meta,
          );
          images.push({ ...mediaImage, type: MediaType.IMAGE });
        }
      }
    }

    if (carouselPosts.length > 0) {
      const [res, err] = await getAlbumMediaRequest.makeRequest({ media_ids: carouselPosts });
      if (err) {
        Toast.show({ type: 'error', text1: 'Something went wrong!', text2: "Couldn't load some images" });
      } else {
        const medias = res?.data as InstagramMedia[];

        for (const m of medias) {
          if (m.media_type === 'VIDEO') {
            videos[m.post_id] = [...(videos[m.post_id] ?? []), m.media_url];
          } else {
            const cachedUrl = mediaCache?.[m.id];
            const image = await resizeImageAndGetFilePath(m?.thumbnail_url ?? m.media_url, m.id, cachedUrl, {
              postId: m?.post_id,
            });
            images.push({ ...image, type: MediaType.IMAGE });
          }
        }
      }
    }
    // setImages(images);
    console.log('images: ', images);
    console.log('videos: ', videos);
    console.log('captions: ', captions);

    setMedias(images);
    setVideosMap(videos);
    setCaptionMap(captions);
  };

  useEffect(() => {
    console.log('step', step);
  }, [step]);

  const handleSelectComplete = async () => {
    toggle(false);
    await delay(700);
    setStep('import');
    progressSteps.setStepIsLoading();
    await processImportImagesAndCaptionsStep();
    progressSteps.nextStep();
  };

  const handleOpenConnectAccountModal = async () => {
    await delay(500);
    toggleWebView();
  };

  return (
    <Fragment>
      {step === 'connect' && (
        <ConnectToInstagramScreen {...{ show, toggle }} toggleWebView={handleOpenConnectAccountModal} />
      )}
      {step === 'select' && (
        <>
          {type === 'IMPORT' && (
            <BottomModal
              showButton
              isVisible={show}
              useChildrenAsDirectChild
              // enableDynamicSizing
              customSnapPoints={[95]}
              title="Select posts to upload as products"
              buttons={[
                {
                  text: 'Proceed',
                  onPress: handleSelectComplete,
                  isLoading: false,
                  disabled: Object.keys(form.values?.selected_posts)?.length === 0,
                },
              ]}
              closeModal={() => toggle(false)}>
              <SelectInstagramPosts form={form} maxUploadable={10} />
            </BottomModal>
          )}
          {type === 'SELECT' && (
            <BottomModal
              showButton
              wrapChildren
              childrenWrapperStyle={{ maxHeight: Dimensions.get('screen').height * 0.6 }}
              isVisible={show}
              useChildrenAsDirectChild
              title="Select Instagram media"
              buttons={[
                {
                  text: 'Proceed',
                  onPress: () => onImportComplete([]),
                  isLoading: false,
                  disabled: selectedMedia?.length === 0,
                },
              ]}
              closeModal={() => toggle(false)}>
              <SelectInstagramMedias
                selectedMedia={selectedMedia}
                setSelectedMedia={setSelectedMedia}
                maxUploadable={10}
                mediaType="VIDEO"
              />
            </BottomModal>
          )}
        </>
      )}
      {step === 'import' && type !== 'SELECT' && (
        <ProgressModal
          show={step === 'import'}
          toggle={toggle}
          currentStep={progressSteps.currentStep}
          steps={progressSteps.steps}
        />
      )}
    </Fragment>
  );
};

const instagramProgressSteps = [
  { key: 'IMPORT', label: 'Importing images from instagram...', isLoading: true, complete: false },
  { key: 'UPLOADING', label: 'Uploading images to our servers...', progress: 0 },
  { key: 'GENERATE', label: 'Generating product details...', progress: 0 },
  { key: 'ASSIGN', label: 'Downloading videos and assigning details...', progress: 0 },
];

async function resizeImageAndGetFilePath(src: string, key: string, url?: string, meta?: any): Promise<Image> {
  const isUploaded = url !== undefined;

  const { uri } = await manipulateAsync(src, [{ resize: { width: 1080 } }], {
    compress: 0.5,
    format: SaveFormat.JPEG,
    base64: true,
  });

  return {
    file: null,
    url: isUploaded ? url : undefined,
    key,
    isUploading: false,
    uploadProgress: isUploaded ? 100 : 0,
    meta,
    name: getRandString(10),
    src: uri,
  };
}

function cleanUpNumberString(number: any) {
  return Number(String(number).replace(',', ''));
}

const Loader = ({ show }: { show: boolean }) => {
  const insertTop = useSafeAreaInsets().top + hp(12);
  const translateX = useSharedValue(-300);

  useEffect(() => {
    translateX.value = withRepeat(
      withTiming(300, {
        duration: 700,
        easing: Easing.inOut(Easing.ease),
      }),
      -1,
      true,
    );
  }, []);

  // Create the animated style for the loading bar
  const progressStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
    };
  });

  return (
    <Modal
      isVisible={show}
      backdropColor={'#1E1E1E80'}
      animationIn={'fadeInDown'}
      animationOut={'fadeOutUp'}
      style={[{ flex: 1, justifyContent: 'flex-start', margin: 0, padding: 0 }]}>
      <View
        style={[{ justifyContent: 'flex-end', marginTop: insertTop }]}
        className="bg-white mx-20 rounded-12 px-20 py-20">
        <BaseText fontSize={15} type="heading">
          Loading...
        </BaseText>
        <View className="w-full bg-grey-bgTwo rounded-full overflow-hidden mt-15">
          <Animated.View className={`rounded-full h-6 py-1 bg-primary-main`} style={progressStyle}></Animated.View>
        </View>
      </View>
    </Modal>
  );
};
