import { useEffect, useState } from 'react';
import { ActivityIndicator, Dimensions, ScrollView, View } from 'react-native';
import WebView, { WebViewNavigation } from 'react-native-webview';
import { cx } from 'src/assets/utils/js';
import { BaseText } from '@/components/ui';
import BottomModal from '@/components/ui/modals/bottom-modal';
import { paramsFromObject } from 'catlog-shared';
import CustomImage from 'src/components/ui/others/custom-image';
import { BottomSheetView } from '@gorhom/bottom-sheet';

interface InstagramWebViewProps {
  show: boolean;
  toggle: (s?: boolean) => void;
  onComplete: (code: string) => void;
}

const IG_AUTH_URL = 'https://www.instagram.com/oauth/authorize';
const redirectUri = 'https://app.catlog.shop/products/create';

export const InstagramWebView: React.FC<InstagramWebViewProps> = ({ show, toggle, onComplete }) => {
  const [siteLoaded, setSiteLoaded] = useState(false);

  useEffect(() => {
    if (show == false) setSiteLoaded(false);
  }, [show]);

  const params = paramsFromObject({
    client_id: '1078947597341793', //@feranmi: This should be an environment variable
    redirect_uri: redirectUri, //@feranmi: This should be an environment variable
    scope: 'instagram_business_basic',
    response_type: 'code',
    enable_fb_login: '0',
    force_authentication: '0',
  });

  const url = `${IG_AUTH_URL}?${params}`;

  // const authUri = `https://api.instagram.com/oauth/authorize?${params}`;

  const handleNavigationChange = (nav: WebViewNavigation) => {
    if (nav.url.includes('code=')) {
      const code = nav.url.split('code=')[1].replace('#_', '');
      onComplete(code);
    }
  };

  return (
    <BottomModal
      contentContainerClass="p-0 m-0"
      isVisible={show}
      customSnapPoints={[90]}
      useChildrenAsDirectChild
      closeModal={() => toggle(false)}>
      <BottomSheetView style={{ flex: 1 }} className="flex-1" enableFooterMarginAdjustment>
        <WebView
          onNavigationStateChange={handleNavigationChange}
          onLoad={() => setSiteLoaded(true)}
          className={cx('flex-1 w-full h-full p-0')}
          source={{ uri: url }}
        />
        {!siteLoaded && <ContainerLoader message="Loading Webpage..." />}
      </BottomSheetView>
      {/*Todo: @kayode the "Loading webpage" text should be centrally aligned on the modal, it currently sits at the bottom of the modal */}
    </BottomModal>
  );
};

interface ContainerLoaderProps {
  message: string;
}
export const ContainerLoader: React.FC<ContainerLoaderProps> = ({ message }) => {
  return (
    <View className="flex-1 bg-white justify-center items-center absolute h-full w-full">
      <CustomImage imageProps={{ source: require('@/assets/gif/loader.gif') }} className="w-40 h-40 rounded-8" />
    </View>
  );
};
