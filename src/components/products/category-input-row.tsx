import { Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import Input, { InputProps } from '../ui/inputs/input';
import Row from '../ui/row';
import Pressable from '../ui/base/pressable';
import { BaseText, CircledIcon } from '../ui';
import { Close } from '../ui/icons';
import { wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { Ref, forwardRef, useCallback, useImperativeHandle, useMemo, useState } from 'react';
import { Add, Calendar, Grammerly } from 'iconsax-react-native/src';
import EmojiPicker, { EmojiType } from 'rn-emoji-keyboard';
import CategoryInput, { CategoryInputProps } from '../ui/inputs/category-input';
import { Category } from 'catlog-shared';

export enum CategoryInputRowType {
  'ADD_CATEGORY',
  'VIEW_CATEGORY',
}

export interface CategoryInputMethod {
  setEmoji: (emojiType: EmojiType) => void;
  getCategoryData: () => { emoji: string | null; name: string | null };
  reset: VoidFunction;
}

interface CategoryInputRowProps extends Partial<BottomModalProps> {
  categoryInputProps?: Partial<InputProps>;
  categoryData?: Category;
  categoryInputRowType?: CategoryInputRowType;
  onPressBtn?: VoidFunction;
  updateCategory?: (category: Category) => void;
}

const CategoryInputRow = (
  {
    categoryInputProps,
    categoryInputRowType = CategoryInputRowType.VIEW_CATEGORY,
    categoryData,
    updateCategory,
    onPressBtn,
  }: CategoryInputRowProps,
  ref: Ref<CategoryInputMethod>,
) => {
  const [isOpen, setIsOpen] = useState(false);

  const [emoji, setEmoji] = useState<string | null>(null);
  const [name, setName] = useState<string | null>(null);

  const handleSetEmoji = (emojiObject: EmojiType) => {
    setEmoji(emojiObject.emoji);
  };

  const handleGetCategoryData = () => {
    return { emoji, name, id: '' };
  };

  const handleResetCategoryData = () => {
    setEmoji(null);
    setName(null);
  };

  useImperativeHandle(ref, () => ({
    setEmoji: handleSetEmoji,
    getCategoryData: handleGetCategoryData,
    reset: handleResetCategoryData,
  }));

  const displayEmoji = categoryData?.emoji ?? emoji;
  const displayName = categoryData?.name ?? name;

  const buttonsVariant = {
    [CategoryInputRowType.VIEW_CATEGORY]: {
      button: (
        <Pressable onPress={onPressBtn}>
          <CircledIcon className="bg-grey-bgOne p-12">
            <Close size={wp(12)} strokeWidth={1} currentColor={colors.black.placeholder} />
          </CircledIcon>
        </Pressable>
      ),
    },
    [CategoryInputRowType.ADD_CATEGORY]: {
      button: (
        <Pressable onPress={onPressBtn}>
          <CircledIcon className="bg-primary-main p-12">
            <Add size={wp(15)} color={colors.white} />
          </CircledIcon>
        </Pressable>
      ),
    },
  };

  const RightAccessory = useCallback(
    () => (
      <Pressable
        className="w-28 h-28 m-6 items-center justify-center bg-grey-bgOne rounded-full "
        onPress={() => setIsOpen(true)}>
        {displayEmoji ? (
          <BaseText fontSize={12}>{displayEmoji}</BaseText>
        ) : (
          // <emoji size={wp(18)} color={colors.grey.muted} />
          <BaseText fontSize={12}>😀</BaseText>
        )}
      </Pressable>
    ),
    [emoji, displayEmoji, categoryData],
  );

  return (
    <View>
      <Row className="gap-10 mt-15">
        <View className="flex-1">
          <Input
            rightAccessory={<RightAccessory />}
            placeholder={'Enter category name'}
            value={displayName}
            useBottomSheetInput
            onChangeText={text => {
              setName(text);
              updateCategory({ ...categoryData, name: text });
            }}
            {...categoryInputProps}
            containerClasses={`py-0 px-0 pl-16`}
          />
        </View>
        {buttonsVariant[categoryInputRowType].button}
      </Row>
      <EmojiPicker
        enableSearchBar
        onEmojiSelected={e => {
          console.log(e);
          updateCategory({ ...categoryData, emoji: e.emoji });
          setIsOpen(false);
        }}
        open={isOpen}
        onClose={() => setIsOpen(false)}
        expandable={false}
      />
    </View>
  );
};

export default forwardRef(CategoryInputRow);
