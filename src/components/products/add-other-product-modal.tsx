import { ScrollView, Text, View } from 'react-native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { BaseText, Row } from '../ui';
import CustomSwitch from '../ui/inputs/custom-switch';
import { useFormik } from 'formik';
import { useApi } from '@/hooks/use-api';
import { delay, getFieldvalues, showError, showSuccess, updateOrDeleteItemFromList } from 'src/assets/utils/js';
import Input from '../ui/inputs/input';
import * as Yup from 'yup';
import useAuthContext from 'src/contexts/auth/auth-context';
import Toast from 'react-native-toast-message';
import {
  DiscountItemInterface,
  CREATE_CUSTOM_ITEM,
  CreateCustomItemParams,
  UPDATE_CUSTOM_ITEM,
  UPDATE_DISCOUNT,
  UpdateCustomItemParams,
  UpdateDiscountParams,
  CustomItemInterface,
  getRandString,
} from 'catlog-shared';
import MoneyInput from '../ui/inputs/money-input';

interface AddOtherProductModalProps extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
  onPressSave?: VoidFunction;
  activeItem?: CustomItemInterface;
  isEdit: boolean;
  setOtherProducts: React.Dispatch<React.SetStateAction<CustomItemInterface[]>>;
}

const AddOtherProductModal = ({
  isEdit,
  closeModal,
  onPressSave,
  setOtherProducts,
  activeItem,
  ...props
}: AddOtherProductModalProps) => {
  const updateCustomItemRequest = useApi<UpdateCustomItemParams>({
    key: 'update-custom-item',
    apiFunction: UPDATE_CUSTOM_ITEM,
    method: 'PUT',
  });
  const createCustomItemRequest = useApi<CreateCustomItemParams>({
    key: 'create-custom-item',
    apiFunction: CREATE_CUSTOM_ITEM,
    method: 'POST',
  });

  const { store } = useAuthContext();
  const form = useFormik<UpdateCustomItemParams>({
    initialValues: {
      id: activeItem?.id!,
      name: activeItem?.name ?? '',
      price: activeItem?.price ? Number(activeItem?.price) : 0,
    },
    validationSchema,
    enableReinitialize: true, //todo: this would automatically refresh the initial data
    onSubmit: async values => {
      let response;
      let error;
      if (isEdit) {
        const reqData = {
          ...values,
          price: Number(values.price),
        };
        [response, error] = await updateCustomItemRequest.makeRequest(reqData);
      }
      if (!isEdit) {
        [response, error] = await createCustomItemRequest.makeRequest({
          name: values?.name!,
          price: Number(values?.price)!,
        });
      }
      if (response) {
        const newData = {
          name: values.name!,
          price: String(values.price),
          id: values.id ?? response?.data?.id,
        };
        closeModal();
        await delay(700)
        if (isEdit) {
          setOtherProducts?.(prev => updateOrDeleteItemFromList(prev, 'id', activeItem?.id, newData));
          showSuccess('Item updated successfully')
        } else {
          setOtherProducts?.(prev => [...prev, newData]);
          showSuccess('Item Created successfully')
        }
        form.resetForm();
      }
      if (error) {
        showError(error)
      }
      return [response, error];
    },
  });

  const handleSaveUpdates = () => {
    form.handleSubmit();
  };

  return (
    <BottomModal
      {...props}
      onModalShow={() => form.resetForm()}
      closeModal={closeModal}
      size='midi'
      buttons={[
        {
          text: isEdit ? 'Update Item' : 'Create Item',
          onPress: handleSaveUpdates,
          isLoading: updateCustomItemRequest?.isLoading || createCustomItemRequest?.isLoading,
          loadingText: isEdit ? 'Updating Item...' : 'Creating Item...',
        },
      ]}
      title={`Edit Item ${activeItem?.name ? activeItem?.name : ''}`}>
      <View>
        <ScrollView>
          <View className="px-20 pb-20">
            <Input label={'Product Name'} autoFocus useBottomSheetInput containerClasses="mt-20" {...getFieldvalues('name', form)} />
            <MoneyInput
              label={`Product price in ${store?.currencies?.default}`}
              useBottomSheetInput
              containerClasses="mt-15"
              {...getFieldvalues('price', form)}
            />
          </View>
        </ScrollView>
      </View>
    </BottomModal>
  );
};

export const validationSchema = Yup.object().shape({
  name: Yup.string().required('Label is required'),
  price: Yup.string().required('Label is required'),
});

export default AddOtherProductModal;
