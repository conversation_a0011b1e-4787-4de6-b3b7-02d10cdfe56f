import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import Input from '../ui/inputs/input';
import Row from '../ui/row';
import Pressable from '../ui/base/pressable';
import { BaseText, CircledIcon } from '../ui';
import { Close } from '../ui/icons';
import { delay, hideLoader, showError, showLoader, showSuccess, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Add, Calendar, Grammerly } from 'iconsax-react-native/src';
import CategoryInputRow, { CategoryInputMethod, CategoryInputRowType } from './category-input-row';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useApi } from '@/hooks/use-api';
import useAuthContext from '@/contexts/auth/auth-context';
import useAuthStore from '@/contexts/auth/store';
import {
  Category,
  CREATE_STORE_CATEGORIES,
  CreateStoreCategoriesParams,
  DELETE_STORE_CATEGORY,
  GET_STORE_CATEGORIES,
} from 'catlog-shared';
import useKeyboard from 'src/hooks/use-keyboard';
import { FlatList } from 'react-native-gesture-handler';
import InfoBadge from '../store-settings/info-badge';
import { BottomSheetFlatList, BottomSheetFlatListMethods } from '@gorhom/bottom-sheet';

interface ManageCategoriesModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  storeCategories: Category[];
  updateCallback?: (c: Category[]) => void;
}

const ManageCategoriesModal = ({
  closeModal,
  updateCallback,
  storeCategories = [],
  ...props
}: ManageCategoriesModalProps) => {
  const { store, updateStore } = useAuthContext();
  // const [categories, setCategories] = useState<Category[]>([...storeCategories , { id: '', name: '', emoji: '' }]);

  // useEffect(() => {
  //   if (storeCategories.length > 0) {
  //     const dataToAdd = storeCategories.filter(
  //       category => !categories.some(c => c.id === category.id || c.name === category.name),
  //     );

  //     const modifiedCategories = [...categories].map(c => {
  //       const category = storeCategories.find(sC => sC.name === c.name);

  //       return category || c;
  //     });

  //     setCategories([...dataToAdd, ...modifiedCategories]);
  //   }
  // }, [storeCategories]);

  // useEffect(() => {
  //   if (storeCategories) {
  //     setCategories([...storeCategories , { id: '', name: '', emoji: '' }]);
  //   }
  // }, [storeCategories]);

  const scrollRef = useRef<BottomSheetFlatListMethods>(null);

  const { storeId } = useAuthContext();

  const deleteCategoriesRequest = useApi({
    apiFunction: DELETE_STORE_CATEGORY,
    key: DELETE_STORE_CATEGORY.name,
    method: 'DELETE',
  });

  const createCategoriesRequest = useApi({
    apiFunction: CREATE_STORE_CATEGORIES,
    method: 'POST',
    key: 'create-categories',
  });

  const form = useFormik<CreateStoreCategoriesParams>({
    initialValues: {
      id: storeId!,
      categories: [...storeCategories, { id: '', name: '', emoji: '' }],
    },
    validationSchema: validationSchema,
    onSubmit: async values => {
      const [response, error] = await createCategoriesRequest.makeRequest({ ...values, id: store.id });

      if (response) {
        closeModal();
        await delay(600);
        showSuccess('Categories updated successfully');
        updateStore({ categories: response?.data });
        updateCallback?.(response?.data);
      }

      if (error) {
        showError(error);
      }
    },
  });

  useEffect(() => {
    // form.setFieldValue('categories', categories);
    form.setFieldValue('id', storeId);
  }, [storeId]);

  const deleteCategory = async (index: number) => {
    const categoriesCopy = [...form.values?.categories];
    const category = categoriesCopy[index];

    if (categoriesCopy.length < 2) {
      showError(null, 'You cannot delete this category, leave it empty instead');
      return;
    }

    if (category.items_count && category.items_count > 0) {
      showError(null, 'You cannot delete a category with products, edit the details instead');
      return;
    }

    const deletedItem = categoriesCopy.splice(index, 1);

    // setCategories(categoriesCopy);

    const deleteCategoryReq = async () => {
      showLoader('Deleting category...');
      const [response, error] = await deleteCategoriesRequest.makeRequest({
        id: storeId || store.id,
        category_id: deletedItem[0].id,
      });
      hideLoader();

      if (error) {
        categoriesCopy.splice(index, 1, deletedItem[0]);
        // setCategories([...categoriesCopy]);
        form.setFieldValue('categories', categoriesCopy);
        // showError("Couldn't delete category, please try again");
        showError(error);
        return;
      }

      showSuccess('Category deleted');

      // updateStoreCategories([...categoriesCopy].filter((c) => c.name));
      updateStore({ categories: categoriesCopy });
      updateCallback?.(categoriesCopy);
    };

    if (deletedItem[0].id) {
      await deleteCategoryReq();
    } else form.setFieldValue('categories', categoriesCopy);
  };

  const isKeyboardActive = useKeyboard();

  const updateCategory = (index: number, category: Category) => {
    const categoriesCopy = [...form.values?.categories];

    categoriesCopy[index] = category;

    form.setFieldValue('categories', categoriesCopy);
  };

  const addCategory = () => {
    const categoriesCopy = [...form.values?.categories];
    categoriesCopy.push({ id: '', name: '', emoji: '' });

    form.setFieldValue('categories', categoriesCopy);
    setTimeout(() => {
      scrollRef.current?.scrollToEnd({ animated: true });
    }, 400);
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      useScrollView={false}
      // enableDynamicSizing
      showButton
      size='lg'
      useChildrenAsDirectChild
      showFooterOnKeyboard
      onModalShow={() => scrollRef.current.scrollToEnd()}
      buttons={[
        {
          text: 'Save product categories',
          onPress: () => form.handleSubmit(),
          isLoading: createCategoriesRequest.isLoading || deleteCategoriesRequest.isLoading,
        },
      ]}
      title={'Manage Product Categories'}>
        <BottomSheetFlatList
          data={form?.values?.categories}
          ref={scrollRef}
          enableFooterMarginAdjustment
          contentContainerStyle={{ paddingBottom: 30, paddingHorizontal: wp(20) }}
          // keyExtractor={(item) => item.name}
          ListHeaderComponent={() => <InfoBadge className='-mb-10' text="You can also select an optional emoji for each category" />}
          renderItem={({ item, index }) => (
            <CategoryInputRow
              categoryData={item}
              key={index}
              categoryInputProps={{
                error: form.errors?.categories?.[index]?.name,
                hasError: form.errors?.categories?.[index] !== undefined,
              }}
              updateCategory={c => updateCategory(index, c)}
              onPressBtn={() => deleteCategory(index)}
            />
          )}
          // ListFooterComponent={
          //   <CategoryInputRow
          //     ref={addCategoryInputRef}
          //     handleToggleEmoji={handleToggleEmoji}
          //     onPressBtn={() => handleReturnCategoryData()}
          //     categoryInputRowType={CategoryInputRowType.ADD_CATEGORY}
          //   />
          // }
          ListFooterComponent={
            <Pressable onPress={addCategory} className="mt-15 items-end">
              <CircledIcon className="bg-grey-bgOne p-12">
                <Add size={wp(15)} color={colors.black.main} />
                <BaseText className="text-black-main ml-6">Add New Category</BaseText>
              </CircledIcon>
            </Pressable>
          }
        />
    </BottomModal>
  );
};

export const validationSchema = Yup.object().shape({
  id: Yup.string().required('Coupon code is required'),
  categories: Yup.array()
    .of(
      Yup.object().shape({
        id: Yup.string(),
        emoji: Yup.string(),
        name: Yup.string().required('Name is required'),
        items_count: Yup.string(),
      }),
    )
    .required()
    .min(1, 'Please create at least one category'),
});

// id?: string;
//     emoji: string;
//     name: string;
//     items_count?: number;

export default ManageCategoriesModal;
