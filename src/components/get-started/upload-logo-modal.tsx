import { ScrollView, View } from 'react-native';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import Container from 'src/components/ui/container';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import { BaseText, CircledIcon } from 'src/components/ui';
import UploadImageBtn from '../ui/buttons/upload-image-btn';
import { Image as ImageIcon, TickCircle } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import { delay, showError, showSuccess, wp } from 'src/assets/utils/js';
import { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import Pressable from '../ui/base/pressable';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import { UPDATE_STORE_DETAILS } from 'catlog-shared';
import { useState } from 'react';
import CustomImage from '../ui/others/custom-image';
import { BlurView } from 'expo-blur';

interface Props extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
}

const UploadLogoModal = ({ closeModal, ...props }: Props) => {
  const [logoUrl, setLogoUrl] = useState<string | null>(null);
  const [heroImageUrl, setHeroImageUrl] = useState<string | null>(null);
  const { store, updateStore } = useAuthContext();

  const updateStoreRequest = useApi({
    apiFunction: UPDATE_STORE_DETAILS,
    key: 'update-store-request',
    method: 'PUT',
  });

  const saveImages = async () => {
    if (!logoUrl && !heroImageUrl) return;
    const images = { logo: logoUrl, hero_image: heroImageUrl };
    const reqData = { id: store?.id, ...images };
    const [res, err] = await updateStoreRequest.makeRequest(reqData);

    if (err) {
      showError('Error uploading photo');
    }

    if (res) {
      updateStore(images);
      closeModal();
      await delay(1000);
      showSuccess('Photo uploaded successfully');
    }
  };

  return (
    <BottomModal
      {...props}
      title="Add logo and Cover Image"
      enableDynamicSizing
      closeModal={closeModal}
      buttons={[
        {
          text: 'Done',
          onPress: saveImages,
          isLoading: updateStoreRequest.isLoading,
          disabled: !logoUrl && !heroImageUrl,
        },
      ]}>
      <BottomSheetScrollView>
        <View className="px-20 pb-40">
          <UploadImageBtn setTempImage={() => null} setImageUrl={url => setLogoUrl(url)} />
          <UploadImageBtn
            setTempImage={() => null}
            setImageUrl={url => setHeroImageUrl(url)}
            customBtn={(pickImage, isUploading) => (
              <UploadCoverImageElement {...{ pickImage, isUploading, heroImageUrl }} />
            )}
          />
        </View>
      </BottomSheetScrollView>
    </BottomModal>
  );
};

export default UploadLogoModal;

const UploadCoverImageElement = ({
  pickImage,
  isUploading,
  heroImageUrl,
}: {
  heroImageUrl: string;
  pickImage: any;
  isUploading: boolean;
}) => {
  console.log('test', heroImageUrl);
  return (
    <Pressable
      onPress={pickImage}
      className="h-[200px] rounded-[14px] border border-grey-border border-dashed mt-15 overflow-hidden items-center justify-center">
      <CustomImage imageProps={{ source: { uri: heroImageUrl } }} className="w-full h-full absolute" />
      {!heroImageUrl && (
        <View>
          <CircledIcon iconBg="bg-primary-pastel self-center">
            <View className="items-center justify-center bg-primary-main rounded-full p-5">
              <ImageIcon variant={'Bold'} color={colors.white} size={wp(9)} />
            </View>
          </CircledIcon>
          <View className="mt-10">
            <BaseText
              classes="text-black-secondary text-center"
              weight="medium"
              fontSize={12}
              numberOfLines={1}
              ellipsizeMode="tail">
              {!heroImageUrl ? 'Click to upload cover image' : ''}
              {heroImageUrl && isUploading ? 'Uploading...' : ''}
            </BaseText>
            <BaseText classes="text-black-placeholder text-center mt-4" fontSize={11}>
              {!heroImageUrl ? 'PNG, JPG | 10MB max.' : ''}
              {heroImageUrl && !isUploading ? 'Tap to change' : ''}
              {/* {heroImageUrl && isUploading ? `${image.uploadProgress}% Completed` : ''} */}
            </BaseText>
          </View>
        </View>
      )}
      {heroImageUrl && (
        <Pressable onPress={pickImage} className="absolute bottom-20">
          <BlurView tint="dark" intensity={40} className="py-6 px-8 rounded-8 overflow-hidden bg-[#00000070]">
            <BaseText weight="medium" className="text-white">
              Select another image
            </BaseText>
          </BlurView>
        </Pressable>
      )}
    </Pressable>
  );
};
