import { Animated, Dimensions, FlatList, Linking, View } from 'react-native';
import { Book1, VideoCircle } from 'iconsax-react-native/src';
import Row from '@/components/ui/row';
import colors from '@/theme/colors';
import { BaseText, CircledIcon } from '@/components/ui';
import { cx, hexToRgba, hp, openLinkInBrowser, wp } from '@/assets/utils/js';
import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import SectionContainer, { ContainerType } from '@/components/ui/section-container';
import ListItemCard, { ListCard } from '../ui/cards/list-item-card';
import { ArrowUpRight } from '../ui/icons';
import { CONTACT_SUPPORT_WA_LINK } from 'src/assets/utils/js/constants';
import AccordionGroup from '../ui/others/accordion/accordion-group';
import AccordionAnchor from '../ui/others/accordion/accordion-anchor';
import Accordion from '../ui/others/accordion';
import { useFeatureFlags } from 'src/contexts/feature-flags/use-feature-flags';

const FaqSection = ({
  hideIcon = false,
  hideOthers = false,
  hideFaqList = false,
}: {
  hideIcon?: boolean;
  hideOthers?: boolean;
  hideFaqList?: boolean;
}) => {
  const { isFeatureEnabled } = useFeatureFlags();

  const faqs = [
    {
      title: 'What is Catlog',
      link: 'https://catlog-help-center.notion.site/What-is-Catlog-0ce8ab77c0a54836aed2cf7f3d501b12',
      onPress: () => {},
    },
    {
      title: 'How much does Catlog cost',
      link: 'https://catlog-help-center.notion.site/How-much-does-Catlog-cost-26dc2a0dc3c545f0990ac9e5f3afd9f1',
      content: `For growing businesses looking to handle their sales better, NGN 4,750 monthly. For bigger businesses looking to sell more efficiently NGN 9,750 monthly.`,
      onPress: () => {},
    },
    ...(isFeatureEnabled('subscriptions')
      ? [
          {
            title: 'How do I renew my subscription',
            link: 'https://catlog-help-center.notion.site/How-to-renew-your-subscription-7c0d8475ab284084a6a23c6d776dc285',
            onPress: () => {},
          },
        ]
      : []),
    {
      title: 'How do I customize my store link',
      link: 'https://catlog-help-center.notion.site/How-do-I-create-a-custom-store-link-for-my-store-8c6b3eb70b2a453e80fb52f1dc71cda2#e38c876490184258bd8f445a2f944511',
      onPress: () => {},
    },
    {
      title: 'Does catlog have a mobile phone',
      link: 'https://catlog-help-center.notion.site/Does-Catlog-have-a-mobile-app-3c25e7f6917e4d668f97b2db5edbffac',
      onPress: () => {},
    },
  ];

  return (
    <View>
      <View className="px-20 pt-24 pb-0">
        <Row className="justify-start">
          {!hideIcon && (
            <CircledIcon iconBg="" classes="p-5" style={{ backgroundColor: hexToRgba(colors.accentYellow.main, 0.1) }}>
              <Book1 variant={'Bulk'} size={wp(16)} color={colors.accentYellow.main} />
            </CircledIcon>
          )}
          <BaseText fontSize={14} type={'heading'} weight={'bold'} classes={cx({ 'ml-6': !hideIcon })}>
            Frequently asked questions
          </BaseText>
        </Row>
      </View>
      <View className="px-20">
        {/* {!hideFaqList && (
          <ListCard
            titleClasses={'text-primary-main'}
            titleProps={{ weight: 'medium', fontSize: wp(12) }}
            containerType={ContainerType.OUTLINED}
            staticRightElement={
              <CircledIcon iconBg="bg-grey-bgOne">
                <ArrowUpRight size={wp(16)} currentColor={colors.primary.main} strokeWidth={2} />
              </CircledIcon>
            }
            items={faqs.map(item => ({ ...item, onPress: () => openLinkInBrowser(item.link) }))}
          />
        )} */}
        {!hideFaqList && (
          <SectionContainer className="pb-12">
            <AccordionGroup addSeparator>
              {faqs.map(item => (
                <Accordion
                  key={item.title}
                  anchorElement={status => (
                    <AccordionAnchor title={item.title} isOpened={status} useWhiteBgForAnchor />
                  )}>
                  {item?.content && (
                    <View>
                      <BaseText lineHeight={25} classes="text-black-muted">
                        {item.content}
                      </BaseText>
                    </View>
                  )}
                </Accordion>
              ))}
            </AccordionGroup>
          </SectionContainer>
        )}
        {!hideOthers && (
          <ListCard
            classes="mt-24"
            titleProps={{ weight: 'medium', fontSize: wp(12) }}
            staticRightElement={
              <CircledIcon iconBg="bg-grey-bgOne">
                <ArrowUpRight size={wp(16)} currentColor={colors.primary.main} strokeWidth={2} />
              </CircledIcon>
            }
            items={othersFaqs.map(item => ({ ...item, onPress: () => openLinkInBrowser(item.link) }))}
          />
        )}
      </View>
    </View>
  );
};

const othersFaqs = [
  {
    title: 'Got questions?',
    link: 'https://www.notion.so/49b13cc6f3904725b8f29aa337f7d73b?pvs=4',
    onPress: () => {},
  },
  {
    title: 'Need help?',
    link: CONTACT_SUPPORT_WA_LINK,
    onPress: () => {},
  },
];

export default FaqSection;
