export enum PaymentSettingsTabs {
  PAYMENT_OPTIONS = 'PAYMENT_OPTIONS',
  SECURITY_PIN = 'SECURITY_PIN',
}

export enum StoreConfigurationTabs {
  CUSTOMIZATION = 'Customization',
  OPENING_CLOSING_HOURS = 'Opening & Closing Hours',
  CURRENCIES = 'Currencies',
  PRODUCT_CONFIGURATIONS = 'Product Configurations',
  STORE_MESSAGES = 'Store Messages',
  // STORE_CONFIGURATIONS = 'Store Configurations',
  CUSTOM_DOMAINS = 'Custom Domains',
  // STORE_CONFIGURATIONS = 'Store Configurations',
}

export enum StoreInformationTabs {
  BASIC_DETAILS = 'Basic Details',
  LOCATION_DETAILS = 'Location Details',
  SOCIAL_LINKS = 'Social Links',
  EXTRA_INFO = 'Extra Info',
  BUSINESS_CATEGORY = 'Business category',
  ABOUT_US = 'About Us',
  FAQS = 'FAQs',
  THIRD_PARTY_INTEGRATIONS = 'Third Party Integrations',
}
