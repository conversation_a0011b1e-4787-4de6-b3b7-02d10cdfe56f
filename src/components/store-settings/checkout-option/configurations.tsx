import { <PERSON><PERSON>View, View } from 'react-native';
import { Add, Box, InfoCircle, Instagram, LocationTick, Message, MoneyTick, Whatsapp } from 'iconsax-react-native/src';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import colors from '@/theme/colors';
import { alertPromise, getFieldvalues, showLoader, showSuccess, wp } from '@/assets/utils/js';
import ListItemCard from '@/components/ui/cards/list-item-card';
import { useNavigation } from '@react-navigation/native';
import InfoBadge from '@/components/store-settings/info-badge';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import Input from '@/components/ui/inputs/input';
import Separator from '@/components/ui/others/separator';
import LeftLabelledInput from '@/components/ui/inputs/left-labelled-input';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import * as Yup from 'yup';
import { FormikProps, useFormik } from 'formik';
import { Fragment, useRef, useState } from 'react';
import WhatsappOption from '@/components/store-settings/checkout-option/whatsapp-option';
import { phoneValidation } from 'src/assets/utils/js/common-validations';
import Toast from 'react-native-toast-message';
import {
  UPDATE_CHECKOUT_CHANNELS,
  UPDATE_STORE_DETAILS,
  UpdateStoreParams,
  InstagramCheckoutOption,
  StoreInterface,
  WhatsappCheckoutOption,
  getRandString,
  removeCountryCode,
} from 'catlog-shared';

export interface CustomUpdateStoreParams extends Omit<UpdateStoreParams, 'id'> {
  whatsapp?: WhatsappCheckoutOption[];
  instagram?: InstagramCheckoutOption;
}

type f = Partial<CustomUpdateStoreParams>;

export interface SharedStoreConfigProps {
  form: FormikProps<any>;
}

function Configurations() {
  const [error, setError] = useState('');
  const { store, updateStore } = useAuthContext();
  const navigation = useNavigation();

  const scrollRef = useRef<ScrollView>(null);

  const updateStoreDetailsRequest = useApi({
    key: 'update-checkout-details',
    apiFunction: UPDATE_STORE_DETAILS,
    method: 'PUT',
  });

  const updateCheckoutOptionRequest = useApi({
    key: 'update-checkout-channels',
    apiFunction: UPDATE_CHECKOUT_CHANNELS,
    method: 'PUT',
  });

  const getDeliveryTimeline = () => {
    const timlineArray = store?.extra_info?.delivery_timeline?.split(' ') ?? [];

    return {
      count: timlineArray?.length > 0 ? timlineArray[0].toLocaleLowerCase() : '',
      unit: timlineArray?.length > 1 ? timlineArray[1].toLocaleLowerCase() : '',
    };
  };

  const form = useFormik<any>({
    initialValues: {
      whatsapp: [...store.checkout_channels.whatsapp, generateOption()],
      instagram: store.checkout_channels.instagram ?? {
        enabled: false,
        username: null,
        id: getRandString(10),
        type: 'INSTAGRAM',
      },
      // delivery_timeline: store?.configuration.average_delivery_timeline,
      pickup_enabled: store?.configuration?.customer_pickup_enabled ?? false,
      pickup_address: store?.pickup_address?.formatted_address,
      confirm_order_before_payment: store?.configuration?.confirm_order_before_payment ?? false,
      require_delivery_info: store?.configuration?.require_delivery_info,
      require_emails: store?.configuration?.require_emails,
      payment_validates_order: store?.configuration?.payment_validates_order,
      payment_timeout: store?.configuration?.payment_timeout ?? 20,
      extra_info: {
        ...store.extra_info,
        delivery_timeline: getDeliveryTimeline(),
      },
    },
    onSubmit: async values => {
      const { extra_info, instagram, whatsapp, ...others } = values;

      const whatsappContacts = whatsapp ?? [];

      const configuration = {
        average_delivery_timeline: values?.delivery_timeline,
        customer_pickup_enabled: values?.pickup_enabled,
        confirm_order_before_payment: values.confirm_order_before_payment,
        require_delivery_info: values.require_delivery_info,
        payment_timeout: values.payment_timeout,
        require_emails: values.require_emails,
        payment_validates_order: values.payment_validates_order,
      };

      const [response, error] = await updateStoreDetailsRequest.makeRequest({
        id: store?.id,
        pickup_address: values?.pickup_address,
        configuration,
        extra_info: {
          ...extra_info,
          delivery_timeline: `${extra_info.delivery_timeline.count} ${extra_info.delivery_timeline.unit}`.trim(),
        },
      });

      if (error) {
        Toast.show({ type: 'error', text1: 'Store detail updated successfully' });
        return;
      }

      updateStore({
        configuration: {
          ...store?.configuration,
          ...values?.configuration,
          confirm_order_before_payment: values?.configuration?.confirm_order_before_payment,
        },
        extra_info: {
          ...extra_info,
          delivery_timeline: `${extra_info.delivery_timeline.count} ${extra_info.delivery_timeline.unit}`.trim(),
        },
      } as Partial<StoreInterface>);

      const validWhatsappOptions = whatsappContacts.filter(o => o.label !== '' && o.phone !== '');
      const hasSomeValidPhoneNumber = validWhatsappOptions.some(
        o => o?.phone?.split('-')[1] !== undefined && o?.phone?.split('-')[1].trim() !== '',
      );
      const phonesWithNoLabel = whatsappContacts.some(
        o => o?.phone?.split('-')[1] !== undefined && o.phone.split('-')[1].trim() !== '' && o.label === '',
      );

      if (phonesWithNoLabel) {
        setError('Some phone numbers have no label');
        scrollRef.current?.scrollTo({ y: 0, x: 0, animated: true });
      } else if (validWhatsappOptions.length < 1 || !hasSomeValidPhoneNumber) {
        setError('Please provide at least one whatsapp number');
        scrollRef.current?.scrollTo({ y: 0, x: 0, animated: true });
      } else {
        setError('');
        const data = {
          store: store?.id,
          data: {
            whatsapp: whatsapp?.filter(o => o.label !== '' && o.phone !== ''),
            instagram: {
              ...instagram,
              enabled: instagram?.enabled && Boolean(instagram.username),
            },
          },
        };

        const [res, error] = await updateCheckoutOptionRequest.makeRequest(data);
        if (res) {
          Toast.show({ text1: 'Checkout option saved successfully' });
        }
      }
    },
    validateOnBlur: true,
    validationSchema,
  });

  const toggleWhatsAppCheckout = async (state: boolean) => {
    if (state === false) {
      const can = await alertPromise(
        'Disable Whatsapp',
        "We don't recommend disabling whatsapp because most customers want to speak with you before making payments. Are you sure you want to disable this?",
        'Disable',
        'Cancel',
        true,
      );
      if (!can) return;
    }
    
    showLoader('Updating store...', false, true);
    const [res, err] = await updateStoreDetailsRequest.makeRequest({
      id: store?.id,
      configuration: {
        ...store.configuration,
        whatsapp_checkout_enabled: state,
      },
    });

    if (err) {
      return Promise.reject(err);
    }

    showSuccess('Updated store successfully');

    updateStore({
      configuration: {
        ...store.configuration,
        whatsapp_checkout_enabled: state,
      },
    } as Partial<StoreInterface>);

    return Promise.resolve(res);
  };

  const settings = [
    {
      leftIcon: (
        <CircledIcon iconBg="bg-accentYellow-pastel p-10">
          <Message variant={'Bold'} size={wp(16)} color={colors.accentYellow.main} />
        </CircledIcon>
      ),
      rightIcon: (
        <CustomSwitch
          value={form.values?.require_emails}
          onValueChange={value => form.setFieldValue('require_emails', value)}
        />
      ),
      title: 'Require Emails',
      description: 'Collect email from customers at checkout',
    },
    {
      leftIcon: (
        <CircledIcon iconBg="bg-accentGreen-pastel p-10">
          <Box variant={'Bold'} size={wp(16)} color={colors.accentGreen.main} />
        </CircledIcon>
      ),
      rightIcon: (
        <CustomSwitch
          value={form.values?.require_delivery_info}
          onValueChange={value => form.setFieldValue('require_delivery_info', value)}
        />
      ),
      title: 'Enable Delivery',
      description: 'Use this if your business makes deliveries',
    },
  ];

  return (
    <View className="flex-1">
        <ScrollView className="flex-1" keyboardShouldPersistTaps={'handled'} ref={scrollRef}>
      <AvoidKeyboard>
          <View className="flex-1 px-20 pb-20">
            <InfoBadge text={'Configure how customers can checkout from your storefront'} />
            {error && (
              <View className="mt-20 rounded-8 bg-accentRed-pastel px-10 py-8">
                <BaseText fontSize={12} classes="text-center text-accentRed-main">
                  {error}
                </BaseText>
              </View>
            )}
            {settings?.map(item => (
              <ListItemCard
                key={item?.title}
                leftElement={item?.leftIcon}
                rightElement={item?.rightIcon}
                className="py-24"
                titleProps={{ fontSize: 15, weight: 'bold', type: 'heading' }}
                title={item?.title}
                description={item?.description}
                descriptionProps={{ fontSize: 12, weight: 'regular' }}
                disabled={true}
                showBorder={true}
              />
            ))}
            {/* //Pickup Address */}
            <PickupAddress form={form} />
            <PaymentValidateOrder form={form} />
            <WhatsappOption store={store} toggleWhatsAppCheckout={toggleWhatsAppCheckout} form={form} />
            <Separator className="mx-0 mt-0 mb-20" />
            <ListItemCard
              leftElement={
                <CircledIcon iconBg="bg-accentRed-pastel p-10">
                  <Instagram size={wp(16)} color={colors.accentRed.main} />
                </CircledIcon>
              }
              rightElement={
                <CustomSwitch
                  value={form.values.instagram?.enabled}
                  onValueChange={value => form.setFieldValue('instagram.enabled', value)}
                />
              }
              className="py-0"
              titleProps={{ fontSize: 15, weight: 'bold', type: 'heading' }}
              title={'Instagram Checkout'}
              description={'Allow customers send orders to instagram'}
              descriptionProps={{ fontSize: 12, weight: 'regular' }}
              disabled={true}
              showBorder={false}
            />
            {form.values.instagram?.enabled && (
              <Fragment>
                <LeftLabelledInput
                  containerClasses="mt-20"
                  leftText={'instagram.com/'}
                  label={'Your instagram username'}
                  {...getFieldvalues('instagram.username', form)}
                  value={form?.values?.instagram?.username}
                />
                <InfoBadge
                  className="bg-white"
                  customIcon={<InfoCircle variant={'Bold'} size={wp(14)} color={colors.accentYellow.main} />}
                  text={"Please make sure your account isn't set to private"}
                />
              </Fragment>
            )}
          </View>
      </AvoidKeyboard>
        </ScrollView>
      <FixedBtnFooter
        buttons={[
          {
            text: 'Save Checkout Options',
            onPress: () => form.submitForm(),
            isLoading: updateCheckoutOptionRequest?.isLoading || updateStoreDetailsRequest.isLoading,
          },
        ]}
      />
    </View>
  );
}

const PickupAddress = ({ form }: SharedStoreConfigProps) => {
  return (
    <View>
      <ListItemCard
        leftElement={
          <CircledIcon iconBg="bg-accentOrange-pastel p-10">
            <LocationTick variant={'Bold'} size={wp(16)} color={colors.accentOrange.main} />
          </CircledIcon>
        }
        rightElement={
          <CustomSwitch
            value={form.values?.pickup_enabled}
            onValueChange={value => form.setFieldValue('pickup_enabled', value)}
          />
        }
        className="py-24"
        titleProps={{ fontSize: 15, weight: 'bold', type: 'heading' }}
        title={'Enable Pick Up'}
        description={'Allow customers pick up from your address'}
        descriptionProps={{ fontSize: 12, weight: 'regular' }}
        disabled={true}
        showBorder={false}
      />
      {form.values?.pickup_enabled && (
        <View className="mb-20">
          <Input label={'Address'} {...getFieldvalues('pickup_address', form)} />
        </View>
      )}
      <Separator className="my-0 mx-0" />
    </View>
  );
};

const PaymentValidateOrder = ({ form }: SharedStoreConfigProps) => {
  return (
    <View>
      <ListItemCard
        leftElement={
          <CircledIcon iconBg="bg-accentRed-pastel p-10">
            <MoneyTick variant={'Bold'} size={wp(16)} color={colors.accentRed.main} />
          </CircledIcon>
        }
        rightElement={
          <CustomSwitch
            value={form.values?.payment_validates_order}
            onValueChange={value => form.setFieldValue('payment_validates_order', value)}
          />
        }
        className="py-24"
        titleProps={{ fontSize: 15, weight: 'bold', type: 'heading' }}
        title={'Payment Validates Order'}
        description={'Orders not paid for within specific time will be cancelled'}
        descriptionProps={{ fontSize: 12, weight: 'regular' }}
        disabled={true}
      />
      {form.values?.payment_validates_order && (
        <View className="mb-20">
          <Input
            label={'Payment Timeout (mins)'}
            keyboardType={'number-pad'}
            {...getFieldvalues('payment_timeout', form)}
          />
        </View>
      )}
      <Separator className="my-0 mx-0" />
    </View>
  );
};

export const generateOption = (countryCode?: string): WhatsappCheckoutOption => {
  const code = countryCode ? countryCode : '+234';

  return {
    id: getRandString(10),
    label: '',
    type: 'WHATSAPP',
    phone: `${code}-`,
    primary: false,
  };
};

const validationSchema = Yup.object().shape({
  // extra_info: isChowbotSetup
  //   ? Yup.object().shape({
  //       delivery_timeline: Yup.object().shape({
  //         count: Yup.string().required('Timeline count is required'),
  //         unit: Yup.string().required('Timeline duration is required'),
  //       }),
  //     })
  //   : null,
  configuration: Yup.object().shape({
    average_delivery_timeline: Yup.string(),
    customer_pickup_enabled: Yup.boolean(),
    require_delivery_info: Yup.boolean(),
    // payment_timeout: Yup.string(),
    payment_timeout: Yup.string().when('payment_validates_order', ([payment_validates_order], schema) => {
      if (payment_validates_order === true) return Yup.string().required('Please enter payment timeout');
      return schema;
    }),
    require_emails: Yup.boolean(),
    payment_validates_order: Yup.boolean(),
    whatsapp_checkout_enabled: Yup.boolean(),
  }),
  pickup_address: Yup.string().when('configuration.customer_pickup_enabled', ([customer_pickup_enabled], schema) => {
    if (customer_pickup_enabled === true) return Yup.string().required('Please enter pick up address');
    return schema;
  }),
  whatsapp: Yup.array().when('configuration', ([whatsapp_checkout_enabled], schema) => {
    if (whatsapp_checkout_enabled === true)
      return Yup.array()
        .of(
          Yup.object().shape({
            id: Yup.string().required(),
            label: Yup.string().required('Label is required'),
            phone: phoneValidation('phone'),
            type: Yup.string().required('Type is required'),
            primary: Yup.boolean(),
          }),
        )
        .required('Please enter pick up address');
    return schema;
  }),
});

export default Configurations;
