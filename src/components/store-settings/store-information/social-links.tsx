import { ScrollView } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { Container } from '@/components/ui';
import InfoBadge from '../info-badge';
import LeftLabelledInput from '../../ui/inputs/left-labelled-input';
import { FormikProps } from 'formik';
import { getFieldvalues } from 'src/assets/utils/js';
import { CustomUpdateStoreInformationParams } from 'src/screens/store-settings/store-information';
import { UpdateStoreParams } from 'catlog-shared';

interface SocialLinksProps {
  form: FormikProps<CustomUpdateStoreInformationParams>;
  isLoading: boolean;
}

const SocialLinks = ({ form, isLoading }: SocialLinksProps) => {
  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge text={'Here’s a summary of the information you provided'} />
          <View>
            <LeftLabelledInput
              leftText={'instagram.com'}
              label={'Your instagram username'}
              containerClasses="mt-20"
              {...getFieldvalues('socials.instagram', form)}
              value={form?.values?.socials?.instagram}
            />
            <LeftLabelledInput
              leftText={'facebook.com'}
              label={'Your facebook username'}
              containerClasses="mt-15"
              {...getFieldvalues('socials.facebook', form)}
              value={form?.values?.socials?.facebook}
            />
            <LeftLabelledInput
              leftText={'twitter.com'}
              label={'Your twitter username'}
              containerClasses="mt-15"
              {...getFieldvalues('socials.twitter', form)}
              value={form?.values?.socials?.twitter}
            />
          </View>
        </Container>
      </ScrollView>
      <FixedBtnFooter buttons={[{ text: 'Update Changes', onPress: () => form.submitForm(), isLoading }]} />
    </View>
  );
};

export default SocialLinks;
