import { ScrollView } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { BaseText, Container, Row } from '@/components/ui';
import InfoBadge from '../info-badge';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import { SharedStoreConfigProps } from 'src/screens/store-settings/store-configurations';

interface ProductConfigurationsProps extends SharedStoreConfigProps {}

const ProductConfigurations = ({ form, isLoading }: ProductConfigurationsProps) => {
  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge text={'How you want your products to appear on your store'} />
          <View className="mt-20 pb-30">
            <Row>
              <BaseText weight={'medium'} fontSize={12} classes="text-black-secondary">
                Show latest products first
              </BaseText>
              <CustomSwitch
                value={form.values.configuration.sort_by_latest_products}
                onValueChange={value => form.setFieldValue('configuration.sort_by_latest_products', value)}
              />
            </Row>
            <Row className="mt-24">
              <BaseText weight={'medium'} fontSize={12} classes="text-black-secondary">
                Show unavailable products
              </BaseText>
              <CustomSwitch
                value={form.values.configuration.show_unavailable_products}
                onValueChange={value => form.setFieldValue('configuration.show_unavailable_products', value)}
              />
            </Row>
          </View>
        </Container>
      </ScrollView>
      <FixedBtnFooter buttons={[{ text: 'Update Changes', onPress: () => form.submitForm(), isLoading }]} />
    </View>
  );
};

export default ProductConfigurations;
