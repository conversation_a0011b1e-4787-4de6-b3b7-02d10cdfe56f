import { <PERSON><PERSON>View } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { BaseText, Container, Row, WhiteCardBtn } from '@/components/ui';
import InfoBadge from '../info-badge';
import { Check, GhanaFlag, Grid, List, NigeriaFlag, Swipe } from '../../ui/icons';
import SectionContainer from '@/components/ui/section-container';
import colors from '@/theme/colors';
import { Menu, Refresh, Refresh2 } from 'iconsax-react-native/src';
import { hp, wp } from '@/assets/utils/js';
import Button, { ButtonSize, ButtonVariant, TextColor } from '@/components/ui/buttons/button';
import SelectColorModal from './select-color-modal';
import { useMemo, useState } from 'react';
import ListItemCard from '@/components/ui/cards/list-item-card';
import Radio from '@/components/ui/buttons/radio';
import SelectDropdown from '@/components/ui/inputs/select-dropdown';
import { SharedStoreConfigProps } from 'src/screens/store-settings/store-configurations';
import Pressable from '@/components/ui/base/pressable';
import useModals from 'src/hooks/use-modals';
import { trimArray } from 'catlog-shared';

interface CustomizationProps extends SharedStoreConfigProps {}

const CATLOG_PRIMARY_COLOR = colors.primary.main;
const Customization = ({ form, isLoading }: CustomizationProps) => {
  const { modals, toggleModal } = useModals(['manualColor']);

  const viewModeList = [
    {
      value: 'grid',
      label: 'Grid View',
    },
    {
      value: 'swiping',
      label: 'Swiping Card',
    },
    {
      value: 'horizontal',
      label: 'Horizontal View',
    },
  ];

  const selectedColor = form.values.configuration?.color;
  const initialSuggestions = useMemo(
    () =>
      trimArray(
        Array.from(new Set([...(selectedColor ? [selectedColor] : []), CATLOG_PRIMARY_COLOR, ...defaultSuggestions])),
        18,
      ),
    [],
  );

  const handleSetColor = (selection?: string) => {
    // if (!canCustomizeColor) return;

    form.setFieldValue('configuration.color', selection);
    if (selection) {
      // setColorhistory(Array.from(new Set([selection, ...(colorHistory ?? [])])));
    }
  };

  const handleSetColorManually = (selection?: string) => {
    // if (!canCustomizeColor) return;

    form.setFieldValue('configuration.color', selection);
    toggleModal('manualColor', false);
    if (selection) {
      // setColorhistory(Array.from(new Set([selection, ...(colorHistory ?? [])])));
    }
  };

  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge text={'How you want your store to appear to customers'} />
          <View className="pb-30">
            <SectionContainer className="px-0">
              <Row className="px-20 py-15 border-b border-b-grey-border">
                <BaseText fontSize={14} weight={'bold'} type={'heading'}>
                  Brand color
                </BaseText>
                <View className="rounded-8 p-4 bg-white">
                  <View
                    className="h-20 w-20 rounded-[4px]"
                    style={{ backgroundColor: form.values.configuration.color }}
                  />
                </View>
              </Row>
              <View className="px-20 pt-15">
                <Row>
                  <BaseText fontSize={12} weight={'bold'} type={'heading'} classes="text-black-muted">
                    Suggestions
                  </BaseText>
                  <WhiteCardBtn
                    className="bg-grey-bgOne p-0"
                    icon={<Refresh2 size={wp(13)} color={colors.primary.main} strokeWidth={2} className="ml-1" />}>
                    RESET
                  </WhiteCardBtn>
                </Row>
                <Row className="mt-15 flex-wrap justify-between" style={{ columnGap: wp(13), rowGap: hp(10) }}>
                  {initialSuggestions.map(item => (
                    <Pressable
                      key={item}
                      className="rounded-[4px] p-3 border border-grey-border"
                      onPress={() => handleSetColor(item)}>
                      <View
                        className="h-16 w-16 items-center justify-center rounded-[2px]"
                        style={{ backgroundColor: item }}>
                        {selectedColor === item && <Check size={wp(10)} strokeWidth={2} currentColor={colors.white} />}
                      </View>
                    </Pressable>
                  ))}
                </Row>
                <View className="self-end my-15">
                  <Button
                    text={'Manually Pick Color'}
                    variant={ButtonVariant.WHITE}
                    textColor={TextColor.PRIMARY}
                    size={ButtonSize.SMALL}
                    onPress={() => toggleModal('manualColor')}
                  />
                </View>
              </View>
            </SectionContainer>
            <View className="mt-25">
              <BaseText fontSize={14} weight={'bold'} type={'heading'}>
                View Modes
              </BaseText>
              <SectionContainer classes="p-0">
                <ListItemCard
                  showBorder={true}
                  className="px-20"
                  title={'Grid View'}
                  titleProps={{ weight: 'regular' }}
                  titleClasses={'text-black-muted'}
                  onPress={() =>
                    form.setFieldValue('configuration.view_modes.grid', !form?.values?.configuration?.view_modes?.grid)
                  }
                  leftElement={<Grid size={wp(15)} />}
                  rightElement={<Radio active={form?.values?.configuration?.view_modes?.grid ?? false} />}
                />
                <ListItemCard
                  showBorder={true}
                  className="px-20"
                  title={'Swiping Card'}
                  titleProps={{ weight: 'regular' }}
                  titleClasses={'text-black-muted'}
                  onPress={() =>
                    form.setFieldValue('configuration.view_modes.card', !form?.values?.configuration?.view_modes?.card)
                  }
                  leftElement={<Swipe size={wp(15)} />}
                  rightElement={<Radio active={form?.values?.configuration?.view_modes?.card ?? false} />}
                />
                <ListItemCard
                  showBorder={false}
                  className="px-20"
                  title={'Horizontal View'}
                  titleProps={{ weight: 'regular' }}
                  titleClasses={'text-black-muted'}
                  onPress={() =>
                    form.setFieldValue(
                      'configuration.view_modes.horizontal',
                      !form?.values?.configuration?.view_modes?.horizontal,
                    )
                  }
                  leftElement={<List size={wp(15)} />}
                  rightElement={<Radio active={form?.values?.configuration?.view_modes?.horizontal ?? false} />}
                />
              </SectionContainer>
              <SelectDropdown
                modalProps={{size:"sm"}}
                items={viewModeList}
                label={'Select default mode'}
                selectedItem={form?.values?.configuration?.view_modes?.default}
                onPressItem={value => form.setFieldValue('configuration.view_modes.default', value)}
                containerClasses="mt-15"
              />
            </View>
          </View>
        </Container>
      </ScrollView>
      <SelectColorModal
        isVisible={modals.manualColor}
        selectedColor={form.values?.configuration?.color ?? colors.primary.main}
        closeModal={() => toggleModal('manualColor', false)}
        onPressButton={color => handleSetColorManually(color)}
      />
      <FixedBtnFooter buttons={[{ text: 'Update Changes', onPress: () => form.submitForm(), isLoading }]} />
    </View>
  );
};

const defaultSuggestions = [
  '#744253',
  '#54428E',
  '#7A306C',
  '#E86252',
  '#6279B8',
  '#A18276',
  '#157A6E',
  '#3E363F',
  '#772E6C',
  '#DE3C4B',
  '#001514',
  '#26547C',
  '#086470',
  '#363020',
  '#EF476F',
  '#9B7D2F',
  '#70877F',
];

export default Customization;
