import { <PERSON><PERSON>View } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { BaseText, Container, Row } from '@/components/ui';
import InfoBadge from '../info-badge';
import Input from '@/components/ui/inputs/input';
import { DomainData, GET_DOMAINS, UpdateStoreConfigParams } from 'catlog-shared';
import SectionContainer from 'src/components/ui/section-container';
import Button, { ButtonSize, ButtonVariant } from 'src/components/ui/buttons/button';
import { hp, wp } from 'src/assets/utils/js';
import Separator from 'src/components/ui/others/separator';
import DomainCard from './domain-card';
import { useApi } from 'src/hooks/use-api';
import { useState } from 'react';
import ConnectDomainModal from 'src/screens/store-settings/connect-domain-modal';
import useModals from 'src/hooks/use-modals';
import DomainSkeletonLoader from './domain-skeleton-loader';
import BuyDomainModal from 'src/screens/store-settings/buy-domain-modal';
import Can from 'src/components/ui/can';
import { SCOPES } from 'src/assets/utils/js/permissions';

interface CustomDomainProps {}

const CustomDomain = ({}: CustomDomainProps) => {
  const { modals, toggleModal } = useModals(['connectDomain', 'buyDomain']);
  const [domains, setDomains] = useState<DomainData[]>([]);

  const getDomainRequest = useApi({
    apiFunction: GET_DOMAINS,
    key: 'get-store-domains',
    method: 'GET',
    onSuccess: response => {
      setDomains(response.data);
    },
  });

  const onAddDomainCompleted = () => {
    getDomainRequest.refetch();
  };

  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge text={'Change your website name or use a custom domain'} />
          <View className="mt-25">
            <BaseText type="heading" fontSize={14}>
              Your Domains
            </BaseText>
            <SectionContainer className="p-12">
              <BaseText classes="text-black-muted">
                Find your domains, buy a new one, or connect an existing domain
              </BaseText>
              <Can
                data={{
                  planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_CUSTOM_DOMAINS,
                }}>
                <Row className="mt-15" style={{ gap: wp(10) }}>
                  <Button
                    size={ButtonSize.MEDIUM}
                    text="Buy Domain"
                    className="flex-1 bg-white"
                    onPress={() => toggleModal('buyDomain', true)}
                  />
                  <Button
                    size={ButtonSize.MEDIUM}
                    text="Connect Domain"
                    variant={ButtonVariant.LIGHT}
                    className="flex-1"
                    btnStyle="bg-white"
                    onPress={() => toggleModal('connectDomain', true)}
                  />
                </Row>
              </Can>
              {getDomainRequest.isLoading && (
                <>
                  <Separator className='mx-0' />
                  <DomainSkeletonLoader count={1} />
                </>
              )}
              {domains.length > 0 && !getDomainRequest.isLoading && (
                <View>
                  <Separator />
                  <View style={{ gap: hp(10) }}>
                    {domains.map(domain => (
                      <DomainCard domainInfo={domain} key={domain.domain} setDomains={setDomains} />
                    ))}
                  </View>
                </View>
              )}
            </SectionContainer>
          </View>
        </Container>
      </ScrollView>
      <ConnectDomainModal
        isVisible={modals.connectDomain}
        closeModal={() => toggleModal('connectDomain', false)}
        onAddDomainCompleted={onAddDomainCompleted}
      />
      <BuyDomainModal visible={modals.buyDomain} closeModal={() => toggleModal('buyDomain', false)} />
    </View>
  );
};

export default CustomDomain;
