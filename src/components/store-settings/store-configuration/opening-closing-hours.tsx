import { <PERSON><PERSON>View } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { BaseText, CircledIcon, Container, Row } from '@/components/ui';
import InfoBadge from '../info-badge';
import CustomizationCard from '@/components/ui/cards/customization-card';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import Pressable from '@/components/ui/base/pressable';
import { Add } from 'iconsax-react-native/src';
import { wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { SharedStoreConfigProps } from 'src/screens/store-settings/store-configurations';
import { Fragment, useEffect, useMemo, useState } from 'react';
import HourCard from './hour-card';
import { getRandString } from 'catlog-shared';

interface OpeningClosingHoursProps extends SharedStoreConfigProps {}
export type Section = { days: string[]; hours: [string, string] };

const defaultHours = '08:00AM-08:00PM';
const OpeningClosingHours = ({ form, isLoading }: OpeningClosingHoursProps) => {
  const [sections, setSections] = useState<{ [name: string]: Section }>({});

  useEffect(() => {
    const selectedHours = Object.values(hours);
    const hoursNotEmpty = selectedHours.length > 0;

    if (hoursNotEmpty && !defaultMode) {
      const uniqueHours = Array.from(new Set(selectedHours));
      const sectionDays = uniqueHours.map(u => {
        return { days: Object.keys(hours).filter(d => hours[d] === u), hours: u.split('-') };
      });
      const newSections: { [name: string]: Section } = {};
      sectionDays.forEach(days => (newSections[getRandString(10)] = days as Section));
      setSections(newSections);
    }
  }, []);

  const hours = form.values?.configuration?.hours ?? {}; //todo: @silas check this
  const notAllDaysSelected = Object.keys(hours).length < 7;
  const selectedHours = Object.values(hours);
  const defaultMode = useMemo(
    () => selectedHours.length === 7 && selectedHours.every(h => h === defaultHours),
    [form.values.configuration.hours],
  );

  const handleToggleDefaultMode = (value: boolean) => {
    if (value === true) {
      const hours: { [key: string]: string } = {};

      days.forEach(day => (hours[day.value] = defaultHours));
      form.setFieldValue('configuration.hours', hours);
      return;
    }
    calculateHoursFromSections();
  };

  const calculateHoursFromSections = () => {
    const calculatedHours: { [key: string]: string } = {};
    Object.values(sections).forEach(({ days, hours }) => {
      days.forEach(d => (calculatedHours[d] = hours.join('-')));
    });

    form.setFieldValue('configuration.hours', calculatedHours);
  };

  const addNewSection = () => {
    notAllDaysSelected
      ? setSections({
          ...sections,
          [getRandString(10)]: { days: [], hours: getHoursFromString(defaultHours) },
        })
      : null;
  };

  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge text={'Use the default hours or add your own hours'} />
          <View className="mt-20">
            <BaseText fontSize={14} weight={'bold'} type={'heading'}>
              Default Hours
            </BaseText>
            <CustomizationCard
              textTop={'08AM - 8PM'}
              className="mt-10"
              textBottom={'Monday - Sunday'}
              textTopProps={{ weight: 'medium' }}
              showTools={false}
              rightElement={<CustomSwitch value={defaultMode} onValueChange={handleToggleDefaultMode} />}
              textBottomProps={{ weight: 'regular', classes: 'text-black-placeholder' }}
            />
          </View>
          {!defaultMode && (
            <View className="mt-20">
              <BaseText fontSize={14} weight={'bold'} type={'heading'}>
                Custom Hours
              </BaseText>
              {Object.keys(sections).map(sectionKey => {
                const sectionData = sections[sectionKey];
                return (
                  <HourCard
                    sections={sections}
                    sectionKey={sectionKey}
                    sectionData={sectionData}
                    setSections={setSections}
                    calculateHoursFromSections={calculateHoursFromSections}
                  />
                );
              })}
              {notAllDaysSelected === true && (
                <Fragment>
                  <Pressable className="self-start py-8 mt-5" onPress={addNewSection}>
                    <Row className=" justify-start">
                      <Add size={wp(14)} color={colors.primary.main} />
                      <BaseText weight={'medium'} classes="text-primary-main ml-2">
                        Add Hours
                      </BaseText>
                    </Row>
                  </Pressable>
                </Fragment>
              )}
            </View>
          )}
        </Container>
      </ScrollView>
      <FixedBtnFooter buttons={[{ text: 'Update Changes', onPress: () => form.submitForm(), isLoading }]} />
    </View>
  );
};

const getHoursFromString = (hours: string) => hours.split('-') as [string, string];
const days = [
  {
    value: 'sunday',
    label: 'Sunday',
  },
  {
    value: 'monday',
    label: 'Monday',
  },
  {
    value: 'tuesday',
    label: 'Tuesday',
  },
  {
    value: 'wednesday',
    label: 'Wednesday',
  },
  {
    value: 'thursday',
    label: 'Thursday',
  },
  {
    value: 'friday',
    label: 'Friday',
  },
  {
    value: 'saturday',
    label: 'Saturday',
  },
];

export default OpeningClosingHours;
