import { useEffect, useState } from 'react';
import { ActivityIndicator, Image, ScrollView, Text, View } from 'react-native';
import ColorPicker, { HueSlider, OpacitySlider, Panel1, Preview, Swatches } from 'reanimated-color-picker';
import { hp, wp } from '@/assets/utils/js';
import { BaseText, Row, SelectionPill } from '@/components/ui';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import colors from '@/theme/colors';
import InfoBadge from '../info-badge';
import { Check, ThinkingFace } from '@/components/ui/icons';
import Pressable from '@/components/ui/base/pressable';
import { CloseCircle } from 'iconsax-react-native/src';
import * as Animatable from 'react-native-animatable';
import useAuthContext from 'src/contexts/auth/auth-context';
import { uploadImage } from 'src/assets/utils/js/upload-files';
import UploadImageBtn from '@/components/ui/buttons/upload-image-btn';
import * as ImagePicker from 'expo-image-picker';
import useImagePicker from 'src/hooks/use-image-picker';
import { isColorDarkEnoughForWhiteText } from 'catlog-shared';

export interface ColorFromImages {
  dominant_color: string;
  palette: string[];
}

interface SelectColorModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressButton: (color: string) => void;
  selectedColor: string;
}

const SelectColorModal = ({ closeModal, onPressButton, selectedColor, ...props }: SelectColorModalProps) => {
  const [selectorType, setSelectorType] = useState('colorPicker');
  const [color, setColor] = useState(selectedColor);
  const [colorFromImage, setColorFromImage] = useState<ColorFromImages | null>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const { store } = useAuthContext();

  const { pickImage } = useImagePicker();

  useEffect(() => {
    setColor(selectedColor);
  }, [selectedColor]);

  const onSelectColor = ({ hex }: { hex: string }) => {
    setColor(hex);
  };

  useEffect(() => {
    handleExtractColorFromStoreLogo();
  }, []);

  const handleExtractColorFromStoreLogo = async () => {
    try {
      const response = await uploadImage<{ data: ColorFromImages }>(
        undefined,
        'utils/extract-colors',
        () => {},
        'image',
        { image_url: store?.logo! },
        true,
      );
      setColorFromImage(response?.data);
    } catch (error) {}
  };

  const handleExtractColorFromImage = async () => {
    try {
      const image = await pickImage();
      if (image === undefined) return;

      const filePath = image[0].uri;
      setSelectedImage(filePath);
      const response = await uploadImage<{ data: ColorFromImages }>(
        filePath,
        'utils/extract-colors',
        () => {},
        'image',
        undefined,
        true,
      );
      setColorFromImage(response?.data);
    } catch (error) {}
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      title={'Select Color'}
      buttons={[{ text: 'Done', onPress: () => onPressButton(color) }]}>
      <View className="px-20">
        <Row className="justify-start mt-20">
          <SelectionPill
            title={'Color Picker'}
            selected={selectorType === 'colorPicker'}
            onPress={() => setSelectorType('colorPicker')}
          />
          <SelectionPill
            title={'Select color from Image'}
            selected={selectorType === 'fromImage'}
            onPress={() => setSelectorType('fromImage')}
          />
        </Row>
        {!isColorDarkEnoughForWhiteText(color) && (
          <Animatable.View animation={'bounceIn'} duration={200} className="mt-20">
            <InfoBadge
              customIcon={<ThinkingFace />}
              text={
                <BaseText fontSize={11}>
                  We suggest you{' '}
                  {
                    <BaseText weight={'medium'} classes="text-primary-main">
                      select a darker color,
                    </BaseText>
                  }{' '}
                  to ensure customers have the best experience on your store
                </BaseText>
              }
            />
          </Animatable.View>
        )}
        {selectorType === 'colorPicker' && (
          <View>
            <ColorPicker value={color} style={{ gap: 20, marginTop: hp(20) }} onComplete={onSelectColor}>
              <Panel1 boundedThumb thumbSize={wp(16)} style={{ borderRadius: wp(15), height: hp(162) }} />
              <HueSlider boundedThumb thumbSize={wp(16)} sliderThickness={wp(10)} />
              <OpacitySlider boundedThumb thumbSize={wp(16)} sliderThickness={wp(10)} />
            </ColorPicker>
            <View className="p-10 border border-grey-border rounded-[4px] items-center justify-center mt-10">
              <BaseText fontSize={12} weight={'medium'} style={{ textTransform: 'capitalize' }}>
                {color}
              </BaseText>
            </View>
            <BaseText fontSize={12} weight={'medium'} classes="text-grey-muted text-center mt-10 mb-20">
              HEX
            </BaseText>
          </View>
        )}
        {selectorType === 'fromImage' && (
          <View>
            <View className="p-14 border border-grey-muted border-dashed rounded-[15px] items-center justify-center mt-10">
              <Image
                source={{ uri: selectedImage ?? store?.logo }}
                className="w-full h-[174px]"
                resizeMode={'contain'}
              />
              <Pressable
                className="absolute top-14 left-10"
                onPress={() => {
                  setSelectedImage(null);
                  handleExtractColorFromStoreLogo();
                }}>
                <CloseCircle variant={'Bold'} color={colors.accentRed.main} />
              </Pressable>
              <Pressable
                className="absolute py-5 px-8 rounded-[4px] bottom-30 bg-[#04030352]"
                onPress={handleExtractColorFromImage}>
                <BaseText classes="text-white">Click to select new image</BaseText>
              </Pressable>
              {/* <View className='absolute w-full h-full flex items-center justify-center'>
                <ActivityIndicator size={'small'} color={colors.primary.main} />
              </View> */}
            </View>
            <Row className="my-20 justify-center" style={{ gap: wp(10) }}>
              {colorFromImage?.palette?.map(item => (
                <Pressable
                  key={item}
                  className="rounded-[4px] p-3 border border-grey-border"
                  onPress={() => setColor(item)}>
                  <View
                    className="h-20 w-20 items-center justify-center rounded-[2px]"
                    style={{ backgroundColor: item }}>
                    {color === item && <Check size={wp(12)} strokeWidth={2} currentColor={colors.white} />}
                  </View>
                </Pressable>
              ))}
            </Row>
          </View>
        )}
      </View>
    </BottomModal>
  );
};

const storColors = [
  colors.primary.main,
  '#3E363F',
  '#6E2B61',
  '#001514',
  '#157A6E',
  '#363020',
  '#6279B8',
  '#70877F',
  '#744253',
  '#9B7D2F',
  '#A18276',
];

export default SelectColorModal;
