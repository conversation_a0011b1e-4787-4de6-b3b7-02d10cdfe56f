import { ScrollView, View } from 'react-native';
import { useEffect, useState } from 'react';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import { BaseText, Row } from '../../ui';
import { Copy } from 'iconsax-react-native/src';
import Pressable from '../../ui/base/pressable';
import { copyToClipboard, wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import { Gtbank } from '../../ui/icons';
import { Image } from 'react-native';
import useWalletContext from 'src/contexts/wallet/wallet-context';
import { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { CURRENCIES } from 'catlog-shared';
import InfoBadge from '../../store-settings/info-badge';
import classNames from 'node_modules/classnames';

interface AccountsModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressProceed?: () => void;
  activeCurrency: CURRENCIES;
}

const AccountsModal = ({ children, closeModal, onPressProceed, activeCurrency, ...props }: AccountsModalProps) => {
  const { wallets } = useWalletContext();
  const wallet = wallets.find(wallet => wallet?.currency === activeCurrency);
  const accounts = wallet.accounts ?? [];

  const AccountsList = () => (
    <View className="mx-20 mb-20">
      <InfoBadge text="Use these accounts to receive payments to your wallet" classes="mb-10" />
      <View className="flex flex-col bg-grey-bgOne rounded-12">
        {accounts?.map((account, index) => (
          <Pressable onPress={() => copyToClipboard(account.account_number)} key={index}>
            <Row className={classNames('p-14 items-start', { 'border-t border-grey-border': index > 0 })}>
              <View>
                <BaseText fontSize={10} classes="text-black-secondary">
                  {account?.bank_name}
                </BaseText>
                <Row className="justify-start">
                  <BaseText fontSize={14} lineHeight={19} weight="semiBold">
                    {account?.account_number}
                  </BaseText>
                  <View className="p-4 rounded-4 bg-primary-pastel ml-5">
                    <Copy size={wp(10)} color={colors.primary.main} />
                  </View>
                </Row>
                <BaseText fontSize={12} classes="text-black-secondary mt-10">
                  {account?.account_name}
                </BaseText>
              </View>
              <Image
                className="w-[35px] h-[35px]"
                source={{
                  uri: account.image,
                }}
                resizeMode={'cover'}
              />
            </Row>
          </Pressable>
        ))}
      </View>
    </View>
  );

  return (
    <BottomModal size="sm" closeModal={closeModal} title={'Your Accounts'} showButton={false} {...props}>
      {wallet.has_completed_kyc && accounts.length > 0 && <AccountsList />}

      {!wallet.has_completed_kyc && (
        <View className="flex-1 justify-center items-center">
          <BaseText>Complete KYC</BaseText>
        </View> //todo: @silas come back to this / Need designs from @joseph
      )}

      {wallet.has_completed_kyc && accounts.length < 1 && (
        <View className="flex-1 justify-center items-center">
          <BaseText>No Accounts</BaseText>
        </View> //todo: @silas come back to this
      )}
    </BottomModal>
  );
};

export default AccountsModal;
