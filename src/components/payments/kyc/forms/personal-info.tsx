import Container from '@/components/ui/container';
import { useFormik } from 'formik';
import { ForwardedRef, forwardRef, useEffect, useImperativeHandle } from 'react';
import * as Animatable from 'react-native-animatable';
import { getFieldvalues, Yup } from 'src/assets/utils/js';
import InfoBadge from '@/components/store-settings/info-badge';
import Input from '@/components/ui/inputs/input';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import { useApi } from 'src/hooks/use-api';
import { GenericFormRef } from 'src/screens/payments/payment-links/payment-link-form';
import { KYC_STATUSES, SAVE_KYC_BASIC_INFO } from 'catlog-shared';
import { SharedKYCStepProps } from '../types';
import CustomImage from 'src/components/ui/others/custom-image';

interface Props extends SharedKYCStepProps {
  user: any;
}

const PersonalInfo = (
  { user, kycInfo, setKycInfo, next, setIsLoading, formSteps, setCTAEnabled }: Props,
  ref: ForwardedRef<GenericFormRef>,
) => {
  const saveBasicKYCInfo = useApi({
    apiFunction: SAVE_KYC_BASIC_INFO,
    key: SAVE_KYC_BASIC_INFO.name,
    method: 'POST',
  });

  const canEditNameInfo =
    !kycInfo || (!kycInfo?.bvn && [KYC_STATUSES.IN_PROGRESS, KYC_STATUSES.NO_KYC].includes(kycInfo?.status));

  const form = useFormik({
    initialValues: {
      first_name: kycInfo?.first_name ?? user?.name.split(' ')[0] ?? '',
      last_name: kycInfo?.last_name ?? user?.name.split(' ')[1] ?? '',
    },
    onSubmit: async values => {
      if (!canEditNameInfo) {
        formSteps.next();
        return;
      }

      setIsLoading(true);
      const [res] = await saveBasicKYCInfo.makeRequest(values);
      if (res) {
        setKycInfo(res?.data);
        setIsLoading(false);
        formSteps.next();
      }
    },
    validationSchema,
  });

  const bvnExists = Boolean(kycInfo?.bvn);

  useImperativeHandle(ref, () => ({ submitForm: () => form.submitForm() }), []);
  useEffect(() => {
    if (Object.keys(form.errors).length < 1) {
      setCTAEnabled(true);
    } else {
      setCTAEnabled(false);
    }
  }, [form.errors]);

  return (
    <Animatable.View animation={'fadeIn'} duration={100}>
      {/* PLACEHOLDER IMAGE */}
      <ScreenInfoHeader
        colorPalette={ColorPaletteType.GREEN}
        pageTitleTop={'Get Started,'}
        iconElement={
          <CustomImage
            imageProps={{ source: require('@/assets/images/user_profile.png'), contentFit: 'cover' }}
            className="w-80 h-80"
          />
        }
        pageTitleBottom={'What is your name?'}
      />
      <Container className="mt-16">
        <Animatable.View animation={'fadeIn'}>
          <InfoBadge text={'Please enter your legal name as appeared in your IDs'} />
        </Animatable.View>
        <StatusPill title={saveBasicKYCInfo.error?.message} className="mt-15" statusType={StatusType.DANGER} />
        <Input
          {...getFieldvalues('first_name', form)}
          label={'First Name'}
          containerClasses="mt-15"
          editable={!bvnExists}
        />
        <Input
          {...getFieldvalues('last_name', form)}
          label={'Last Name'}
          containerClasses="mt-15"
          editable={!bvnExists}
        />
      </Container>
    </Animatable.View>
  );
};

export default forwardRef(PersonalInfo);

const validationSchema = Yup.object().shape({
  first_name: Yup.string().required('First name is required'),
  last_name: Yup.string().required('Last name is required'),
});
