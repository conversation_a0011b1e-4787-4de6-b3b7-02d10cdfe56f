import { BaseText, CircledIcon, Row } from '@/components/ui';
import React, { memo, ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import EmptyState from '@/components/ui/empty-states/empty-state';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import usePagination from '@/hooks/use-pagination';
import { RefreshControl, View } from 'react-native';
import Animated, { ScrollHandlerProcessed } from 'react-native-reanimated';
import { cx, delay, ensureUniqueItems, toCurrency, toNaira, wp } from 'src/assets/utils/js';
import TransactionSkeletonLoader from './transaction-skeleton-loader';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import { ArrowCircleRight2, ArrowRight, DocumentText, SearchNormal1 } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import dayjs from 'dayjs';
import Column from '../ui/column';
import Pressable from '../ui/base/pressable';
import { StoreInterface, GET_STORE_TRANSACTIONS, TransactionSearchParams, Transaction, Wallet } from 'catlog-shared';
import useWalletContext from 'src/contexts/wallet/wallet-context';
import { SearchFiltersTransactionParams } from '../search/search-transactions-filter-modal';
import { useNavigation } from '@react-navigation/native';

interface Props {
  scrollHandler?: ScrollHandlerProcessed<Record<string, unknown>>;
  listHeader?: ReactNode;
  wallet: Wallet;
  filters?: SearchFiltersTransactionParams;
  isSearchPage?: boolean;
}

export interface TransactionsResponse extends ResponseWithPagination<{ store: StoreInterface; transactions: any[] }> {}
const PER_PAGE = 10;

const TransactionHistory: React.FC<Props> = ({ scrollHandler, isSearchPage = false, listHeader, filters, wallet }) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const { fetchWallets } = useWalletContext();
  const { currentPage, goNext, setPage } = usePagination();
  const navigation = useNavigation();
  const [isRetrying, setIsRetrying] = useState(false);

  const prevWalletId = useRef(wallet?.id);

  useEffect(() => {
    console.log('Remount transaction history');
  }, []);

  useEffect(() => {
    console.log(wallet.id);
  }, [wallet]);

  const filtersToApply = useMemo(() => {
    return Object.keys(filters ?? {}).length > 0 ? filters : { type: '', search: '' };
  }, [filters]);

  useEffect(() => {
    console.log('filtersToApply changed: ', filtersToApply);
  }, [filtersToApply]);

  useEffect(() => {
    const handleReFetchOnWalletChange = async () => {
      // if (wallet?.id) {
      //   if (wallet?.id === prevWalletId.current) {
      //     return;
      //   }
      // }

      setPage(1);
      getTransactionsRequest.makeRequest({
        filter: filtersToApply,
        page: currentPage,
        per_page: PER_PAGE,
        sort: 'asc',
        wallet: wallet?.id,
      });
      await delay(1000);
      setTransactions([]);
    };
    handleReFetchOnWalletChange();
  }, [wallet, filtersToApply]);

  useEffect(() => {
    console.log({currentPage});
    const handleReFetchOnWalletChange = async () => {
      getTransactionsRequest.makeRequest({
        filter: filtersToApply,
        page: currentPage,
        per_page: PER_PAGE,
        sort: 'asc',
        wallet: wallet?.id,
      });
      // await delay(1000);
    };
    handleReFetchOnWalletChange();
  }, [currentPage]);

  const getTransactionsRequest = useApi<TransactionSearchParams, TransactionsResponse>(
    {
      key: 'GET_STORE_TRANSACTIONS_API',
      apiFunction: GET_STORE_TRANSACTIONS,
      method: 'GET',
      autoRequest: false,
      onSuccess: response => {
        setTransactions(prev =>
          ensureUniqueItems(
            response?.page === 1 ? response?.data?.transactions : [...prev, ...response?.data?.transactions],
          ),
        );
      },
    },
    {
      filter: filtersToApply,
      page: currentPage,
      per_page: PER_PAGE,
      sort: 'asc',
      wallet: wallet?.id,
    },
  );

  // console.log('getTransactionsRequest.isLoading: ', getTransactionsRequest?.isLoading)

  useEffect(() => {
    const fn = async () => {
      await delay(1000);
      setTransactions([]);
      setPage(1);
      getTransactionsRequest.refetch();
    };
    fn();
  }, [filters]);

  const transactionsByDay = useMemo(() => {
    const grouped = transactions.reduce(
      (groupedTransactions, transaction) => {
        const date = transaction.created_at.split('T')[0];

        if (!groupedTransactions[date]) {
          groupedTransactions[date] = [];
        }

        groupedTransactions[date].push(transaction);
        return groupedTransactions;
      },
      {} as { [date: string]: Transaction[] },
    );

    return Object.entries(grouped)
      .map(([dateStr, transactions]) => ({
        date: new Date(dateStr),
        transactions,
      }))
      .sort((a, b) => b.date.getTime() - a.date.getTime());
  }, [transactions]);

  // useEffect(() => {
  //   if (wallet?.id) {
  //     setTransactions([]);
  //     setPage(1);
  //   }
  // }, [wallet?.id]);

  const handlePullToRefresh = () => {
    try {
      fetchWallets();
      setTransactions([]);
      if (currentPage === 1) {
        getTransactionsRequest.reset();
        return;
      }
      setPage(1);
    } catch (error) {
      console.log(error);
    }
  };

  const handleRetry = () => {
    setIsRetrying(true);
    handlePullToRefresh();
    setTimeout(() => setIsRetrying(false), 2000);
  };

  const listHeaderComponent = useCallback(() => {
    return <View>{listHeader}</View>;
  }, [wallet]);

  return (
    <View className="bg-white" style={{ flex: 1 }}>
      <View style={{ flex: 1 }}>
        <Animated.FlatList
          data={transactionsByDay}
          keyExtractor={(item, index) => item.date.toString() + index}
          onScroll={scrollHandler}
          refreshControl={<RefreshControl refreshing={false} onRefresh={handlePullToRefresh} />}
          onEndReachedThreshold={0.3}
          ListHeaderComponent={listHeaderComponent}
          ListEmptyComponent={() =>
            getTransactionsRequest?.isLoading ? (
              <TransactionSkeletonLoader />
            ) : (
              <QueryErrorBoundary
                error={getTransactionsRequest.error}
                isLoading={getTransactionsRequest.isLoading}
                refetch={handleRetry}
                isRetrying={isRetrying}
                variant="section"
                errorTitle="Failed to load payments data"
                customErrorMessage="We couldn't load your transaction history. Please check your connection and try again.">
                <EmptyState
                  classes="mt-20"
                  icon={<DocumentText variant="Bold" color={colors.grey.muted} />}
                  text="No Transactions Here"
                  showBtn={false}
                />
              </QueryErrorBoundary>
            )
          }
          className="flex-1 pb-40"
          contentContainerStyle={{ flexGrow: 1 }}
          onEndReached={() => {
            if (!getTransactionsRequest?.isLoading && !getTransactionsRequest.error && transactions.length !== 0) {
              goNext(getTransactionsRequest?.response?.total_pages);
            }
          }}
          renderItem={({ item }) => (
            <View className="py-10 w-full px-20">
              <BaseText fontSize={12} classes="text-black-placeholder">
                {' '}
                {item.date.toDateString()}
              </BaseText>
              <View className="mt-10">
                {item.transactions.map((transaction: Transaction, idx) => (
                  <TransactionCard
                    isLast={idx === item.transactions.length - 1}
                    key={transaction.id}
                    transaction={transaction}
                    onPress={(id: string) => navigation.navigate('PaymentInfo', { id })}
                  />
                ))}
              </View>
            </View>
          )}
          ListFooterComponent={
            <View style={{ marginBottom: 80 }}>
              {getTransactionsRequest?.isLoading && (
                <View className="mt-10">
                  <TransactionSkeletonLoader />
                </View>
              )}
            </View>
          }
        />
      </View>
    </View>
  );
};

export default TransactionHistory;

interface CardProps {
  transaction: Transaction;
  isLast: boolean;
  onPress?: (id: string) => void;
}

export const TransactionCard: React.FC<CardProps> = ({ transaction, isLast, onPress }) => {
  const isDebit = transaction.type === 'debit';
  return (
    <Pressable onPress={() => onPress?.(transaction.id)}>
      <Row className={cx('w-full space-x-2.5 py-10 border-b border-grey-border', { 'border-b-0': isLast })}>
        <CircledIcon className={cx({ 'bg-[#F4E2E6]': isDebit, 'bg-[#E7F3EF]': !isDebit })} iconBg="bg-grey-bgOne">
          <ArrowCircleRight2
            color={isDebit ? colors.accentRed.main : colors.accentGreen.main}
            style={{ transform: [{ rotate: isDebit ? '-45deg' : '135deg' }] }}
            variant="Bold"
          />
        </CircledIcon>
        <Column className="flex-1">
          <Row className="w-full space-x-20 mb-5" spread>
            <View className="flex-1 ">
              <BaseText numberOfLines={1} type="body" weight="regular" fontSize={13} classes=" text-black-muted">
                {transaction.narration}
              </BaseText>
            </View>
            <BaseText fontSize={12} classes="ml-15 text-black-placeholder ">
              {dayjs(transaction.created_at).format('hh:mm A')}
            </BaseText>
          </Row>
          <View className="">
            <BaseText type="heading" fontSize={12} classes="text-black" style={{ letterSpacing: 0.5 }}>
              {toCurrency(toNaira(transaction.amount), transaction?.currency)}
            </BaseText>
          </View>
        </Column>
      </Row>
    </Pressable>
  );
};
