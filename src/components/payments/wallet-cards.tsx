import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import {
  Animated,
  Dimensions,
  Image,
  ImageSourcePropType,
  LayoutAnimation,
  ScrollView,
  View,
  ViewProps,
} from 'react-native';
import { Add, AddCircle, Bank, Import, Repeat } from 'iconsax-react-native/src';
import useModals from 'src/hooks/use-modals';
import { BaseText, Row, WhiteCardBtn } from '../ui';
import Pressable from '../ui/base/pressable';
import {
  ArrowRight,
  ChevronDown,
  GhanaFlag,
  NigeriaFlag,
  USAFlag,
  BritainFlag,
  CanadaFlag,
  SouthAfricaFlag,
  KenyaFlag,
  EuropeFlag,
} from '../ui/icons';
import RequestAccessModal from './modals/request-access-modal';
import useWalletContext from 'src/contexts/wallet/wallet-context';
import useAuthContext from 'src/contexts/auth/auth-context';
import { CURRENCIES, CURRENCY_ACTIONS, CURRENCY_ACTIONS_MAP, paymentsEnabledCurrencies } from 'catlog-shared';
import MoreOptions, { OptionWithIcon } from '../ui/more-options';
import classNames from 'node_modules/classnames';
import SelectDropdown, { DropDownMethods } from '../ui/inputs/select-dropdown';
import ListItemCard from '../ui/cards/list-item-card';
import CircledIcon from '../ui/circled-icon';
import Shimmer from '../ui/shimmer';
import Column from '../ui/column';
import { Dots } from './balance-cards';
import { amountFormat, hasAllItems, hp, toCurrency, toNaira, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import EnabledCurrenciesModal from '../store-settings/store-configuration/enable-currencies-modal';

const screenWidth = Dimensions.get('window').width;
const width = wp(305);

interface PaymentsInfoCardsProps {
  toggleModal: (
    key: 'todaysRate' | 'generateStatement' | 'convertCurrency' | 'withdrawCash' | 'accounts' | 'requestPayment',
    value?: boolean,
  ) => void;
  activeCurrency: CURRENCIES;
  setActiveCurrency: (currency: CURRENCIES) => void;
  isLoading?: boolean;
  userAccountDeactivated?: boolean;
}

const PaymentsInfoCards = ({
  toggleModal,
  activeCurrency,
  userAccountDeactivated,
  setActiveCurrency,
  // isLoading = true,
}: PaymentsInfoCardsProps) => {
  const { wallets, canManageWallet, getWalletsRequest } = useWalletContext();
  const dropdownRef = React.useRef<DropDownMethods>(null);
  const isLoading = getWalletsRequest?.isLoading;

  useEffect(() => {
    console.log('Remount wallets card');
  }, []);

  const activeWallet = useMemo(() => {
    // if (wallets)
    return wallets?.find(wallet => wallet?.currency === activeCurrency);
  }, [activeCurrency, wallets]);
  // Find the balance for the current wallet
  const balance = activeWallet?.balance || 0;

  const enabledCurrencies = wallets.map(wallet => wallet?.currency);
  const hasEnabledAllCurrencies = hasAllItems(enabledCurrencies, paymentsEnabledCurrencies);

  const handleSwitchActiveCurrency = (currency: CURRENCIES) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setActiveCurrency(currency);
  };

  const currencyOptions = useMemo(() => {
    const enabledCurrenciesOptions = enabledCurrencies.map(currency => {
      return {
        leftElement: <View className="w-24 h-24">{currencyDetails[currency].icon({})}</View>,
        label: currencyDetails[currency].fullName,
        value: currency,
      };
    });

    return enabledCurrenciesOptions;
  }, [enabledCurrencies]);

  const currencyActionOptions = useMemo(() => {
    const currency = activeWallet?.currency || CURRENCIES.NGN;
    const actions = [
      ...(!userAccountDeactivated
        ? [
            {
              icon: <Add size={wp(20)} color={colors.white} />,
              title: 'Request',
              onPress: () => toggleModal('requestPayment', true),
              key: CURRENCY_ACTIONS.REQUEST,
              enabled: true,
            },
          ]
        : []),
      ...(!userAccountDeactivated
        ? [
            {
              icon: <Repeat size={wp(20)} color={colors.white} />,
              title: 'Convert',
              onPress: () => toggleModal('convertCurrency', true),
              key: CURRENCY_ACTIONS.CONVERT,
              enabled: canManageWallet && balance > 0,
            },
          ]
        : []),
      {
        icon: <Import size={wp(20)} color={colors.white} />,
        title: 'Withdraw',
        onPress: () => toggleModal('withdrawCash', true),
        key: CURRENCY_ACTIONS.WITHDRAW,
        enabled: canManageWallet && balance > 0,
      },
      {
        icon: <Bank size={wp(20)} color={colors.white} />,
        title: 'Accounts',
        onPress: () => toggleModal('accounts', true),
        key: CURRENCY_ACTIONS.BANK_ACCOUNTS,
        enabled: activeWallet?.has_completed_kyc ? activeWallet?.accounts?.length > 0 : true,
      },
    ];

    const currencyActions = CURRENCY_ACTIONS_MAP[currency];

    return actions.filter(action => currencyActions?.includes(action.key));
  }, [activeWallet, balance, canManageWallet]);

  const BalanceCard = useCallback(() => {
    useEffect(() => {
      console.log('Remount BalanceCard card');
    }, []);
    return (
      <View>
        <CustomImageBg
          className={`rounded-[15px] overflow-hidden items-start justify-center p-15`}
          source={require('@/assets/images/account-balance-bg.png')}
          style={{ width: wallets.length > 1 ? screenWidth - 40 : wp(330) }}>
          <SelectDropdown
            ref={dropdownRef}
            showAnchor={false}
            selectedItem={activeWallet?.currency}
            onPressItem={(value: CURRENCIES) => handleSwitchActiveCurrency(value)}
            label={''}
            showLabel={false}
            items={currencyOptions}
            isLoading={getWalletsRequest.isLoading}
            containerClasses="mt-15"
            listAddOns={
              hasEnabledAllCurrencies ? undefined : (
                <ListItemCard
                  showBorder={false}
                  title={'Add New Currency'}
                  className="border-t border-t-grey-border"
                  titleProps={{ weight: 'medium' }}
                  onPress={() => {}}
                  leftElement={
                    <CircledIcon className="p-5 bg-grey-bgOne">
                      <AddCircle variant={'Bold'} size={wp(24)} color={colors.primary.main} />
                    </CircledIcon>
                  }
                  rightElement={
                    <CircledIcon className="p-10 bg-grey-bgOne">
                      <ArrowRight size={wp(16)} strokeWidth={2} currentColor={colors.primary.main} />
                    </CircledIcon>
                  }
                />
              )
            }
          />
          <Pressable onPress={() => dropdownRef?.current?.open()}>
            <Row className="py-4 px-7 rounded-15 justify-start bg-white">
              <View className="w-18 h-18 mr-5">{currencyDetails[activeWallet?.currency].icon({})}</View>
              <BaseText weight={'medium'} fontSize={11} classes="text-black-main">
                {currencyDetails[activeWallet?.currency]?.fullName?.toUpperCase() ?? ''} (
                {activeWallet?.currency ?? 'NGN'})
              </BaseText>
              <ChevronDown size={wp(16)} strokeWidth={2} stroke={colors.black.muted} />
            </Row>
          </Pressable>
          <View className="mt-10">
            <BaseText type="heading" weight="semiBold" fontSize={26} classes="text-black-main">
              {getWalletsRequest?.isLoading || getWalletsRequest?.isReLoading
                ? 'Loading...'
                : canManageWallet
                  ? `${toCurrency(toNaira(balance ?? 0), activeWallet?.currency)}`
                  : `*****`}
            </BaseText>
          </View>
          <Row classes="mt-24 items-start justify-start" spread={false}>
            {currencyActionOptions.map(item => (
              <Pressable
                key={item.title}
                className={classNames('items-center justify-start mr-25', { 'opacity-50': !item.enabled })}
                onPress={item.onPress}
                disabled={getWalletsRequest?.isLoading || !item.enabled}>
                <Row classes={`p-10 rounded-full items-center justify-center bg-accentGreen-main`}>{item?.icon}</Row>
                <BaseText fontSize={12} weight="medium" lineHeight={16} classes="text-black-secondary mt-4">
                  {item.title}
                </BaseText>
              </Pressable>
            ))}
          </Row>
        </CustomImageBg>
      </View>
    );
  }, []);

  const RequestInternationalPaymentCard = useCallback(() => {
    const { modals, toggleModal } = useModals(['requestAccess']);
    useEffect(() => {
      console.log('Remount RequestInternationalPaymentCard card');
    }, []);

    return (
      <View>
        <CustomImageBg
          className={`rounded-[15px] overflow-hidden items-start justify-center px-15 py-20`}
          source={require('@/assets/images/request-bg.png')}
          style={{ width: wallets.length > 1 ? screenWidth - 40 : wp(330) }}>
          <BaseText type={'heading'} fontSize={18} classes="text-white">
            Get Paid Internationally 💸
          </BaseText>
          <BaseText fontSize={12} classes="text-white opacity-80 mt-5">
            Catlog now let's you collect payments in USD, GBP and other currencies.
          </BaseText>
          <WhiteCardBtn
            className="mt-[37] py-8 px-16 rounded-8"
            onPress={() => toggleModal('requestAccess')}
            icon={<ArrowRight size={18} strokeWidth={2} currentColor={colors.primary.light} />}>
            <BaseText fontSize={14} weight="medium" classes="text-primary-light">
              Request Access
            </BaseText>
          </WhiteCardBtn>
        </CustomImageBg>
        <EnabledCurrenciesModal show={modals.requestAccess} toggle={toggleModal} />
      </View>
    );
  }, []);

  const [scrollX] = useState(new Animated.Value(0));

  return (
    <View className="pt-25">
      <View>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ paddingHorizontal: wp(20) }}
          scrollEventThrottle={16}
          onScroll={Animated.event(
            [
              {
                nativeEvent: { contentOffset: { x: scrollX } },
              },
            ],
            { useNativeDriver: false },
          )}
          pagingEnabled>
          {!isLoading ? (
            <Row className="items-start">
              <BalanceCard />
              {wallets.length < 2 && (
                <>
                  <View className="w-15" />
                  <RequestInternationalPaymentCard />
                </>
              )}
            </Row>
          ) : (
            <View
              style={{ width: screenWidth - 40, height: screenWidth * 0.45, borderRadius: wp(15) }}
              className="bg-grey-bgTwo p-15">
              <Shimmer width={screenWidth * 0.3} height={hp(20)} borderRadius={wp(15)} />
              <Shimmer width={screenWidth * 0.55} height={hp(30)} borderRadius={wp(15)} marginTop={hp(15)} />
              <Row classes="mt-20 items-start justify-start" spread={false}>
                {[1, 2, 3].map(item => (
                  <Column className="mr-20" key={item}>
                    <Shimmer borderRadius={wp(999)} height={hp(40)} width={wp(40)} />
                    <Shimmer width={screenWidth * 0.175} height={hp(12)} borderRadius={wp(15)} marginTop={hp(5)} />
                  </Column>
                ))}
              </Row>
            </View>
          )}
        </ScrollView>
      </View>

      {wallets.length < 2 && (
        <View className="items-center pt-10">
          <Dots
            scrollX={scrollX}
            colors={{ active: colors.accentGreen.main, inactive: colors.grey.border }}
            length={wallets.length < 2 ? 2 : 1}
          />
        </View>
      )}
    </View>
  );
};

export default memo(PaymentsInfoCards);

export const currencyDetails = {
  NGN: {
    fullName: 'Nigerian Naira',
    icon: NigeriaFlag,
  },
  GHS: {
    fullName: 'Ghanaian Cedi',
    icon: GhanaFlag,
  },
  USD: {
    fullName: 'US Dollar',
    icon: USAFlag,
  },
  EUR: {
    fullName: 'Euros',
    icon: EuropeFlag,
  },
  GBP: {
    fullName: 'Great Britain Pound',
    icon: BritainFlag,
  },
  CAD: {
    fullName: 'Canadian Dollar',
    icon: CanadaFlag,
  },
  ZAR: {
    fullName: 'South African Rand',
    icon: SouthAfricaFlag,
  },
  KES: {
    fullName: 'Kenyan Shilling',
    icon: KenyaFlag,
  },
};

interface CustomImageBgProps extends ViewProps {
  source: ImageSourcePropType;
  imageClasses?: string;
  children: React.ReactNode;
}

export const CustomImageBg: React.FC<CustomImageBgProps> = ({ source, imageClasses, children, ...props }) => {
  return (
    <View {...props}>
      <Image className={classNames(`absolute z-0 right-0`, imageClasses)} source={source} />
      {children}
    </View>
  );
};
