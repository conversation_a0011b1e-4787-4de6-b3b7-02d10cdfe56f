import React, { useEffect, useMemo, useRef } from 'react';
import { ActivityIndicator, ScrollView, View } from 'react-native';
import { useState } from 'react';
import BottomModal, { BottomModalMethods, BottomModalProps } from '@/components/ui/modals/bottom-modal';
import { BaseText, CircledIcon } from '@/components/ui';
import { ArrowUpRight, CheckActive } from '@/components/ui/icons';
import {
  calculateWithdrawalFee,
  delay,
  hp,
  millify,
  testYupValidation,
  toCurrency,
  toKobo,
  toNaira,
  wp,
} from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import useSteps from 'src/hooks/use-steps';
import AmountAccountStep from './enter-amount';
import ConfirmWithdrawal from './confirm-withdrawal';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useApi } from 'src/hooks/use-api';
import useWalletContext from 'src/contexts/wallet/wallet-context';
import {
  WithdrawalAccount,
  COMPLETE_WITHDRAWAL,
  CompleteWithdrawalParams,
  GET_WITHDRAWAL_FEES,
  REQUEST_WITHDRAWAL,
  RequestWithdrawalParams,
  Fees,
  WITHDRAWAL_FEE_TYPES,
  CURRENCIES,
} from 'catlog-shared';
import InfoBadge from 'src/components/store-settings/info-badge';
import Pressable from 'src/components/ui/base/pressable';
import WithdrawalFeesModal from './withdrawal-fees';
import useModals from 'src/hooks/use-modals';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import WithdrawalSummary from './withdrawal-summary';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import useKeyboard from 'src/hooks/use-keyboard';

enum MAKE_WITHDRAWAL_STEPS {
  AMOUNT_ACCOUNT = 'AMOUNT_ACCOUNT',
  SUMMARY = 'SUMMARY',
  ENTER_OTP = 'ENTER_OTP',
  SUCCESS = 'SUCCESS',
}

interface WithdrawCashModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressProceed?: () => void;
  currency: CURRENCIES;
}

export interface WithdrawalForm {
  amount: number;
  code: string;
  account: string;
  account_info: WithdrawalAccount;
  request_id?: string;
  email: '';
  fee: number;
  security_pin: string;
  rawInput?: string;
}

const WithdrawCashModal = ({ children, closeModal, onPressProceed, currency, ...props }: WithdrawCashModalProps) => {
  const { wallets, store, updateBalance, getWalletBalance } = useWalletContext();

  const { modals, toggleModal } = useModals(['addBankModal', 'feesModal']);
  const formSteps = useSteps<MAKE_WITHDRAWAL_STEPS>(Object.values(MAKE_WITHDRAWAL_STEPS), 0);
  const { step, next, previous, changeStep } = formSteps;
  const [fees, setFees] = useState<Fees>();
  const [error, setError] = useState<string>();

  const bottomModalRef = useRef<BottomModalMethods>(null);

  const isKeyboardActive = useKeyboard();

  const wallet = useMemo(() => {
    return wallets.find(wallet => wallet.currency === currency);
  }, [wallets, currency]);
  const currentBalance = wallet?.balance ?? 0;

  const getFeesReq = useApi(
    {
      apiFunction: GET_WITHDRAWAL_FEES,
      key: GET_WITHDRAWAL_FEES.name,
      onSuccess: res => {
        setFees(res.data);
      },
      method: 'GET',
      autoRequest: true,
    },
    { currency },
  );

  const requestWithdrawalReq = useApi<RequestWithdrawalParams>({
    apiFunction: REQUEST_WITHDRAWAL,
    key: REQUEST_WITHDRAWAL.name,
    method: 'POST',
  });

  const completeWithdrawalReq = useApi<CompleteWithdrawalParams>({
    apiFunction: COMPLETE_WITHDRAWAL,
    key: COMPLETE_WITHDRAWAL.name,
    method: 'POST',
  });

  const formValidation = useMemo(() => {
    return validationSchema(
      currentBalance,
      step,
      toNaira(fees?.min_withdrawal_amount ?? 0),
      toNaira(fees?.max_withdrawal_amount ?? 0),
      currency,
    );
  }, [step, fees, currency, currentBalance]);

  const form = useFormik<WithdrawalForm>({
    initialValues: {
      rawInput: '',
      amount: 0,
      code: '',
      account: '',
      account_info: null,
      request_id: '',
      email: '',
      fee: 0,
      security_pin: '',
    },
    validationSchema: formValidation,
    onSubmit: async values => {
      switch (step) {
        case MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT:
          next();
          break;
        case MAKE_WITHDRAWAL_STEPS.SUMMARY:
          form.setFieldValue('fee', toNaira(calculateWithdrawalFee(form.values?.amount, fees.fees)));

          const [res, error] = await requestWithdrawalReq.makeRequest({
            withdrawal_account: values.account,
            amount: Number(values.amount),
            wallet: wallet?.id,
          });

          if (error) {
            setError(error?.message ?? 'Something went wrong! Reload page & retry');
            break;
          }

          if (res) {
            form.setFieldValue('request_id', res?.data?.request);
            form.setFieldValue('email', res?.data?.email);
            next();
          }
          break;
        case MAKE_WITHDRAWAL_STEPS.ENTER_OTP: {
          const [res, err] = await completeWithdrawalReq.makeRequest({
            code: values.code,
            request_id: values.request_id,
            security_pin: values.security_pin,
          });

          if (err && err.statusCode !== 412) {
            changeStep(MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT);
            form.resetForm();
            return;
          }

          if (!err) {
            debitBalance(Number(values.amount) + Number(values.fee));
            next();
          }

          break;
        }

        case MAKE_WITHDRAWAL_STEPS.SUCCESS: {
          form.resetForm();
          changeStep(MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT);
          closeModal();
          break;
        }
      }
    },
  });

  useEffect(() => {
    (async() => {
      await delay(5000);
      form.validateForm();
    })()
  }, [form]);

  useEffect(() => {
    if (isKeyboardActive) {
      bottomModalRef.current?.expand();
    } else {
      bottomModalRef.current?.snapToIndex(0);
    }
  }, [isKeyboardActive]);

  useEffect(() => {
    if (!props.isVisible) {
      form.resetForm();
      changeStep(MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT);
    }
  }, [step, props.isVisible]);

  const CTAIsDisabled = useMemo(() => {
    switch (step) {
      case MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT:
        return !!form.errors.amount || !!form.errors.account;
      case MAKE_WITHDRAWAL_STEPS.ENTER_OTP:
        return !!form.errors.security_pin || !!form.errors.code;
      case MAKE_WITHDRAWAL_STEPS.SUCCESS:
        return true;
    }
  }, [form.errors, step]);

  const debitBalance = (amount: number) => {
    updateBalance({ walletId: wallet.id, type: 'debit', amount: toKobo(amount) });
  };

  const isLoading = getFeesReq.isLoading || form.isSubmitting;
  const showBackBtn = step !== MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT && step !== MAKE_WITHDRAWAL_STEPS.SUCCESS;
  const feeString = toCurrency(toNaira(calculateWithdrawalFee(form.values?.amount, fees?.fees ?? [])), currency);

  return (
    <>
      <BottomModal
        ref={bottomModalRef}
        closeModal={closeModal}
        title={modalTitles[step]}
        buttons={[
          showBackBtn
            ? {
                text: 'Back',
                onPress: () => previous(),
                disabled: false,
                variant: ButtonVariant.LIGHT,
              }
            : null,
          {
            text: 'Continue',
            onPress: () => form.submitForm(),
            isLoading,
            disabled: CTAIsDisabled,
          },
        ].filter(Boolean)}
        footerAddon={
          form.values.amount > 0 &&
          step === MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT && (
            <Pressable onPress={() => toggleModal('feesModal')}>
              <InfoBadge
                text={`This withdrawal will cost ${feeString}`}
                className="mt-15 rounded-0 px-[20]"
                rounded={false}
                addon={
                  <Pressable className="flex items-center flex-row">
                    <BaseText fontSize={11} weight="medium" classes="text-primary-main">
                      Learn More
                    </BaseText>
                    <ArrowUpRight size={wp(15)} strokeWidth={2} currentColor={colors.primary.main} />
                  </Pressable>
                }
              />
            </Pressable>
          )
        }
        {...props}>
        <ScrollView contentContainerStyle={{ marginBottom: 20 }}>
          {getFeesReq.isLoading ? (
            <View className="py-40">
              <ActivityIndicator size="small" color={colors.primary.main} />
            </View>
          ) : (
            <>
              {step === MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT && (
                <AmountAccountStep error={error} currency={currency} form={form} fees={fees} />
              )}
              {step === MAKE_WITHDRAWAL_STEPS.SUMMARY && (
                <WithdrawalSummary form={form} currency={currency} fee={feeString} />
              )}
              {step === MAKE_WITHDRAWAL_STEPS.ENTER_OTP && <ConfirmWithdrawal form={form} />}
              {step === MAKE_WITHDRAWAL_STEPS.SUCCESS && (
                <View className="mx-20 mb-[60px] mt-[40]">
                  <View className="self-center">
                    <SuccessCheckmark />
                  </View>
                  <BaseText fontSize={20} weight="light" type={'heading'} classes="text-center mt-10">
                    Your withdrawal of
                  </BaseText>
                  <BaseText fontSize={20} weight="semiBold" type={'heading'} classes="text-center ">
                    {currency} {millify(Number(form.values.amount))} is processing
                  </BaseText>
                </View>
              )}
            </>
          )}
        </ScrollView>
        <WithdrawalFeesModal
          isVisible={modals.feesModal}
          closeModal={() => toggleModal('feesModal', false)}
          fees={fees}
          currency={currency}
        />
      </BottomModal>
    </>
  );
};
export default WithdrawCashModal;

const modalTitles = {
  [MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT]: 'Withdraw Funds',
  [MAKE_WITHDRAWAL_STEPS.SUMMARY]: 'Withdrawal Summary',
  [MAKE_WITHDRAWAL_STEPS.ENTER_OTP]: 'Enter OTP',
  [MAKE_WITHDRAWAL_STEPS.SUCCESS]: 'Withdrawal Complete',
};

const validationSchema = (balance: number, step: string, min: number, max: number, currency) =>
  Yup.object().shape({
    account: Yup.string().typeError('Must be a string').required('Withdrawal account is required'),
    amount: Yup.number()
      .typeError('Amount must be a number')
      .required('Withdrawal amount is required')
      .min(min, `Amount should be at least ${currency} ${millify(min)}`)
      .max(max, `You can only withdraw ${currency} ${millify(max)} at a time`)
      .test('lower_than_balance', 'Insufficient funds', value => value <= toNaira(balance)),
    code:
      step === 'ENTER_TOKEN'
        ? Yup.string().required('OTP token is required').length(6, 'OTP token should be 6 digits')
        : undefined,
    security_pin: Yup.string().min(6, 'Security Pin must be at least 6 digists'),
  });
