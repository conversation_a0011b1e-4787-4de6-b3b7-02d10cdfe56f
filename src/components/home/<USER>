import { useNavigation } from '@react-navigation/native';
import { Activity, BagHappy, Chart, ExportCircle, Moneys } from 'iconsax-react-native/src';
import React, { ReactNode, useMemo } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, withDelay, Easing } from 'react-native-reanimated';
import colors from '@/theme/colors';
import Row from '../ui/row';
import { BaseText } from '../ui/base';
import { ArrowDownRight, ArrowUpRight } from '../ui/icons';
import { isEven, millify, toCurrency, toKobo, toNaira } from '@/assets/utils/js/functions';
import cx from 'classnames';
import { wp } from '@/assets/utils/js';
import { ApiData, ResponseWithoutPagination, useApi } from 'src/hooks/use-api';
import HomeAnalyticsSkeletonLoader from './home-analytics-skeleton-loader';
import useAuthContext from 'src/contexts/auth/auth-context';
import useWalletContext from 'src/contexts/wallet/wallet-context';

export enum AnalyticsVariants {
  'STORE_VISITS' = 'store_visits',
  'TOTAL_ORDERS' = 'total_orders',
  'PAYMENTS' = 'payments',
  'CREDIT_BALANCE' = 'credit_balance',
}

interface HomeAnalyicsCardsProps {
  statistics: {
    total_orders: number;
    total_payments: number;
    total_visits: number;
  };
  trends: {
    total_orders: number;
    total_payments: number;
    total_visits: number;
  };
  isLoading: boolean;
  catlogCreditRequest: ApiData<undefined, ResponseWithoutPagination<{ balance: number; currency: string }>>;
}

interface AnalyticsCardInfo {
  title: string;
  cardBg: string;
  icon: ReactNode;
  iconBg: string;
  key: AnalyticsVariants;
  addon?: ReactNode;
  value?: number | string;
  change: number;
}

const AnimatedCard = ({ 
  info, 
  onPress, 
  disabled, 
  index 
}: { 
  info: AnalyticsCardInfo; 
  onPress?: VoidFunction; 
  disabled: boolean;
  index: number;
}) => {
  const opacity = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: withDelay(
        index * 50,
        withTiming(opacity.value, {
          duration: 200,
          easing: Easing.ease,
        })
      ),
    };
  });

  React.useEffect(() => {
    opacity.value = 1;
  }, []);

  return (
    <Animated.View style={[{ flex: 1 }, animatedStyle]}>
      <TouchableOpacity
        className={`px-15 rounded-[15px] items-start py-15 flex-1 ${info.cardBg}`}
        disabled={disabled}
        onPress={onPress}
        activeOpacity={0.8}>
        <Row className="w-full items-start">
          <Row className={`p-10 rounded-full items-center justify-center ${info.iconBg}`}>{info.icon}</Row>
          {info?.addon ? (
            info.addon
          ) : (
            <Row className="flex flex-row items-center justify-center ml-4 rounded-3xl bg-white py-3 px-5 self-center">
              <Text
                className={cx('leading-none  font-interSemiBold text-[11px]', {
                  'text-accentGreen-main': info.change >= 0,
                  'text-accentRed-main': info.change < 0,
                })}>
                {Math.abs(info.change)}%
              </Text>
              {info.change >= 0 ? (
                <ArrowUpRight currentColor={colors.accentGreen.main} size={14} strokeWidth={2} />
              ) : (
                <ArrowDownRight currentColor={colors.accentRed.main} size={14} strokeWidth={2} />
              )}
            </Row>
          )}
        </Row>
        <View className="mt-15">
          <BaseText fontSize={12} classes="leading-4 text-black-muted">
            {info.title}
          </BaseText>
          <Row className="justify-start mt-4">
            <BaseText
              fontSize={18}
              weight={'black'}
              classes={cx({
                'text-black-muted': info.value === 0,
                'text-black-main': info.value !== 0,
              })}
              type="heading">
              {info.value}
            </BaseText>
          </Row>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const HomeAnalyicsCards = ({ isLoading = false, statistics, catlogCreditRequest, trends }: HomeAnalyicsCardsProps) => {
  const navigation = useNavigation();
  const { store } = useAuthContext();

  const { catlogCredits: wallet } = useWalletContext();

  const analyticsCardsInfo: AnalyticsCardInfo[] = useMemo(
    () => [
      {
        title: 'Store Visits',
        cardBg: 'bg-accentRed-pastel',
        icon: <Chart variant="Bold" size={wp(18)} color={colors?.white} />,
        iconBg: 'bg-accentRed-main',
        key: AnalyticsVariants.STORE_VISITS,
        value: statistics?.total_visits ?? 0,
        change: trends?.total_visits ?? 0,
      },
      {
        title: 'Payments Received',
        cardBg: 'bg-accentGreen-pastel',
        icon: <Moneys variant="Bold" size={wp(18)} color={colors?.white} />,
        iconBg: 'bg-accentGreen-main',
        key: AnalyticsVariants.PAYMENTS,
        // value: 'NGN 100M',
        value: millify(toNaira(statistics?.total_payments), 2, wallet?.currency),
        change: trends?.total_payments,
      },
      {
        title: 'Total Orders',
        cardBg: 'bg-accentYellow-pastel',
        icon: <BagHappy variant="Bold" size={wp(18)} color={colors?.white} />,
        iconBg: 'bg-accentYellow-main',
        key: AnalyticsVariants.TOTAL_ORDERS,
        value: statistics?.total_orders,
        change: trends?.total_orders,
      },
      {
        title: 'Credit Balance',
        cardBg: 'bg-accentOrange-pastel',
        icon: <Activity variant="Bold" size={wp(18)} color={colors?.white} />,
        iconBg: 'bg-accentOrange-main',
        key: AnalyticsVariants.CREDIT_BALANCE,
        addon: <ExportCircle variant="Bold" size={wp(18)} color={colors.accentOrange.light} />,
        // value: toCurrency(catlogCredit?.balance, catlogCredit?.currency, true, 0),
        value: millify(toNaira(wallet?.balance), 2, wallet?.currency),
        change: 0,
      },
    ],
    [trends, statistics, wallet],
  );

  const onPressActions: { [key: string]: VoidFunction } = {
    [AnalyticsVariants.CREDIT_BALANCE]: () => navigation.navigate('CatlogCredits'),
    [AnalyticsVariants.TOTAL_ORDERS]: () => navigation.navigate('Orders'),
    [AnalyticsVariants.PAYMENTS]: () => navigation.navigate('Payments' as any),
  };

  const splitCards = useMemo(() => {
    let columnOne: AnalyticsCardInfo[] = [],
      columnTwo: AnalyticsCardInfo[] = [];

    analyticsCardsInfo.forEach((i, index) => (isEven(index) ? columnOne.push(i) : columnTwo.push(i)));

    return [columnOne, columnTwo];
  }, [analyticsCardsInfo]);

  // const loading = isLoading || catlogCreditRequest?.isLoading;
  const loading = isLoading;

  return (
    <View className="flex-1">
      {loading && <HomeAnalyticsSkeletonLoader />}
      {!loading && (
        <>
          {splitCards.map((group, groupIndex) => (
            <View className="flex-row mb-15" key={groupIndex} style={{gap: wp(18)}}>
              {group.map((info, cardIndex) => {
                // Calculate global index for staggered animation
                const globalIndex = groupIndex * 2 + cardIndex;
                
                return (
                  <AnimatedCard
                    key={info.key}
                    info={info}
                    onPress={onPressActions[info.key]}
                    disabled={!onPressActions[info.key]}
                    index={globalIndex}
                  />
                );
              })}
            </View>
          ))}
        </>
      )}
    </View>
  );
};

export default HomeAnalyicsCards;