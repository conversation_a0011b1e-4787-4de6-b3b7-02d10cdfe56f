import { Gift, InfoCircle, Profile } from 'iconsax-react-native/src';
import { Dimensions, Text, TouchableOpacity, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ReactNode, useMemo } from 'react';
import colors from '@/theme/colors';
import { BaseText, CircledIcon } from '../ui';
import { wp } from '@/assets/utils/js';
import { ArrowRight } from '../ui/icons';
import CircularProgress from 'react-native-circular-progress-indicator';
import { StoreInterface, User } from 'catlog-shared';

export interface HomeNotificationCardProps {
  variant?: HomeNotificationCardVariant;
  user?: User;
  store?: StoreInterface;
}

enum HomeNotificationCardVariant {
  'SUBSCRIPTION' = 'Subscription',
  'MORE' = 'More',
  'GET_STARTED' = 'Get Started',
}

const HomeNotificationCard = ({
  variant = HomeNotificationCardVariant.GET_STARTED,
  store,
  user,
}: HomeNotificationCardProps) => {
  const navigation = useNavigation();

  const setupCompletion = useMemo(() => {
    let pendingTasks = [
      user?.email_verified && user?.phone_verified,
      store?.onboarding_steps?.products_added,
      store?.onboarding_steps?.link_added,
      user?.onboarding_steps?.community_joined,
      user?.onboarding_steps?.pwa_added,
      store?.payments_enabled && store?.kyc_approved,
      store?.slug !== store?.primary_slug,
      !!store?.business_category,
    ];
    return (pendingTasks.filter(v => v).length / pendingTasks.length) * 100;
  }, [store]);

  const variantProperty: {
    [key: string]: { leftElement: ReactNode; title: string; description: string; onClick?: () => void };
  } = {
    [HomeNotificationCardVariant.GET_STARTED]: {
      leftElement: (
        <View>
          <CircularProgress
            value={Math.ceil(setupCompletion)}
            radius={wp(17.5)}
            duration={500}
            delay={600}
            activeStrokeWidth={wp(6)}
            inActiveStrokeWidth={wp(6)}
            strokeLinecap={'round'}
            activeStrokeColor={colors.accentGreen.main}
            inActiveStrokeColor={colors.white}
            maxValue={100}
            valueSuffix={'%'}
            progressValueStyle={{
              fontSize: wp(8),
              fontFamily: 'Inter-Bold',
              color: colors.black.muted,
            }}
          />
        </View>
      ),
      title: 'Get started',
      description: 'Finish setting up & learn more about catlog ',
      onClick: () => navigation.navigate('GetStarted'),
    },
    [HomeNotificationCardVariant.MORE]: {
      leftElement: (
        <CircledIcon iconBg="bg-accentOrange-main">
          <Gift size={wp(16)} color={colors.white} variant="Bold" />
        </CircledIcon>
      ),
      title: 'Get more from Catlog',
      description: 'Subscribe to a paid plan to unlock more',
    },
    [HomeNotificationCardVariant.SUBSCRIPTION]: {
      leftElement: (
        <CircledIcon iconBg="bg-accentRed-main">
          <InfoCircle size={wp(16)} color={colors.white} variant="Bold" />
        </CircledIcon>
      ),
      title: 'Renew Subscription',
      description: 'Your subscription to basic plan has expired',
    },
  };

  const isNotificationAvailable = () => {
    if (setupCompletion >= 100) {
      return false;
    }

    return true;
  };

  return (
    <TouchableOpacity
      className="flex-row items-center p-20 border-b border-b-grey-border bg-primary-pastel"
      style={{ display: isNotificationAvailable() ? 'flex' : 'none' }}
      activeOpacity={0.8}
      onPress={variantProperty[variant]?.onClick}>
      {variantProperty[variant].leftElement}
      <View className={'flex-1 mx-10'}>
        <BaseText fontSize={15} type="heading" classes={'flex-1 text-black-primary'} numberOfLines={1}>
          {variantProperty[variant].title}
        </BaseText>
        <BaseText fontSize={11} classes={'text-black-secondary'} numberOfLines={1}>
          {variantProperty[variant].description}
        </BaseText>
      </View>
      <CircledIcon iconBg="bg-white">
        <ArrowRight currentColor={colors.primary.main} width={wp(16)} height={wp(16)} strokeWidth={2} />
      </CircledIcon>
    </TouchableOpacity>
  );
};

export default HomeNotificationCard;
