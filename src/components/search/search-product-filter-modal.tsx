import { View } from 'react-native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { BaseText, CircledIcon, Row, SelectionPill } from '../ui';
import { Fragment, useMemo, useRef, useState } from 'react';
import Pressable from '../ui/base/pressable';
import AccordionGroup from '../ui/others/accordion/accordion-group';
import AccordionAnchor from '../ui/others/accordion/accordion-anchor';
import colors from 'src/theme/colors';
import { hp, wp } from 'src/assets/utils/js';
import Separator from '../ui/others/separator';
import { Add } from 'iconsax-react-native/src';
import SelectProductsModal from '../products/storefront-products/select-products-modal';
import useAuthContext from 'src/contexts/auth/auth-context';
import SelectDropdown, { DropDownMethods } from '../ui/inputs/select-dropdown';
import Input from '../ui/inputs/input';
import useLayoutHeight from 'src/hooks/use-layout-height';
import { Close, Search } from '../ui/icons';
import { FormikProps } from 'formik';
import { SearchProductsParams } from 'src/screens/products/storefront/search-products';
import Accordion from '../ui/others/accordion';
import {
  Category,
  PRODUCT_AVAILABILITY_TAG,
  PRODUCT_DISCOUNT_TAG,
  PRODUCT_FEATURED_TAG,
  PRODUCT_VARIANT_TAG,
} from 'catlog-shared';
import { BottomSheetScrollView, BottomSheetView } from '@gorhom/bottom-sheet';
import { ScrollView } from 'react-native-gesture-handler';

interface SearchProductFilterModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressContinue?: () => void;
  form?: FormikProps<SearchProductsParams>;
}

const SearchProductFilterModal = ({ closeModal, onPressContinue, form, ...props }: SearchProductFilterModalProps) => {
  const dropDownRef = useRef<DropDownMethods>(null);
  const { store, categories } = useAuthContext();
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

  const filterParams = form?.values;

  const categoriesMapped = categories!.map((item: Category) => ({
    value: item.id!,
    label: item.name + ' ' + item.emoji,
  }));

  const getCategoryName = (id: string) => {
    const findCategory = categories?.find(i => i.id === id);
    if (findCategory) {
      return findCategory.name;
    }
    return '';
  };

  const optionsMap = (object: {}) => Object.entries(object).map(item => ({ name: item[1], value: item[1] }));

  const removeCategory = (index: number) => {
    const categoriesCopy = filterParams?.categories;
    categoriesCopy?.splice(index, 1);
    form?.setFieldValue('categories', categoriesCopy);
  };

  const filterOptions = [
    {
      title: 'Availability',
      options: optionsMap(PRODUCT_AVAILABILITY_TAG),
      activeFilter: filterParams?.availability,
      fieldName: 'availability',
    },
    {
      title: 'Product Variants',
      options: optionsMap(PRODUCT_VARIANT_TAG),
      activeFilter: filterParams?.variants,
      fieldName: 'variants',
    },
    {
      title: 'Featured Product',
      options: optionsMap(PRODUCT_FEATURED_TAG),
      activeFilter: filterParams?.featured_product,
      fieldName: 'featured_product',
    },
    {
      title: 'Discount',
      options: optionsMap(PRODUCT_DISCOUNT_TAG),
      activeFilter: filterParams?.discount,
      fieldName: 'discount',
    },
  ];

  const ClearFilterBtn = () => {
    return (
      <Pressable onPress={() => form?.handleReset()}>
        <BaseText weight="medium" fontSize={11} classes="text-accentRed-main">
          Clear Filters
        </BaseText>
      </Pressable>
    );
  };

  const CategoriesAnchor = () => {
    return (
      <Row className="flex flex-row py-0">
        <Row className="items-center" style={{ gap: 5 }}>
          <BaseText type="heading" fontSize={15}>
            Categories
          </BaseText>
        </Row>
        <Pressable onPress={() => dropDownRef.current?.open()}>
          <CircledIcon className="bg-grey-bgOne p-7">
            <Add size={wp(18)} color={colors.black.placeholder} />
          </CircledIcon>
        </Pressable>
      </Row>
    );
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      size="lg"
      buttons={[
        {
          text: 'Update Filters',
          onPress: onPressContinue,
        },
      ]}
      title={'Filters'}
      headerAddon={<ClearFilterBtn />}>
      <BottomSheetScrollView style={{ paddingHorizontal: wp(20) }}>
        {filterOptions.map((item, index) => (
          <Fragment key={item.title}>
            <Accordion
              initiallyOpened
              anchorElement={status => <AccordionAnchor title={item.title} isOpened={status} />}>
              <Row className="flex-wrap justify-start mt-12" style={{ rowGap: hp(12), columnGap: wp(8) }}>
                {item.options.map((option, index) => (
                  <SelectionPill
                    title={option?.name as string}
                    key={index}
                    className="mr-0"
                    selected={item.activeFilter === option.value}
                    onPress={() => form?.setFieldValue(item.fieldName, option.value)}
                    borderColor={{ active: colors.primary.main, inActive: 'transparent' }}
                    showBorder={true}
                    capitalize
                  />
                ))}
              </Row>
            </Accordion>
            <Separator className="m-0 mt-15" />
          </Fragment>
        ))}
        <Accordion initiallyOpened anchorElement={status => <CategoriesAnchor />}>
          <Row className="flex-wrap justify-start mt-12" style={{ rowGap: hp(12), columnGap: wp(8) }}>
            {filterParams?.categories?.map((item, index) => (
              <SelectionPill
                title={getCategoryName(item)}
                key={index}
                className="mr-0"
                selected={false}
                onPress={() => removeCategory(index)}
                borderColor={{ active: colors.primary.main, inActive: 'transparent' }}
                showBorder={true}>
                <View className="ml-4">
                  <Close size={wp(12)} currentColor={colors.black.placeholder} />
                </View>
              </SelectionPill>
            ))}
          </Row>
        </Accordion>
      </BottomSheetScrollView>
      <SelectDropdown
        ref={dropDownRef}
        showAnchor={false}
        selectedItems={filterParams?.categories}
        isMultiSelect
        onPressItem={v =>
          form?.setFieldValue('categories', [
            ...(Boolean(filterParams?.categories) ? [...filterParams?.categories] : [])!,
            v,
          ])
        }
        showLabel
        label={'Select Categories'}
        items={categoriesMapped}
        closeAfterSelection={true}
        containerClasses="mt-15"
        showButton
        buttons={[{ text: 'Select Categories', onPress: () => dropDownRef.current?.close() }]}
        headerComponent={
          <View className="mx-20">
            <Input
              placeholder={'Search for categories'}
              rightAccessory={<Search size={wp(20)} primaryColor={colors?.black.muted} />}
            />
          </View>
        }
      />
    </BottomModal>
  );
};

export default SearchProductFilterModal;
