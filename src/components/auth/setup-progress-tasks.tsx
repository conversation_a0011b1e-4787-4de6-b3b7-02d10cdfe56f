import React, { useState } from 'react';
import { Linking, TouchableOpacity, View } from 'react-native';
import Row from '../ui/row';
import { CheckActive } from '../ui/icons';
import { getCommunityLink, showError, showLoader, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { BaseText } from '../ui';
import CircledIcon from '../ui/circled-icon';
import { SETUP_TYPE, SetupTask } from '@/screens/get-started/get-started';
import Pressable from '../ui/base/pressable';
import { useApi } from 'src/hooks/use-api';
import { useNavigation } from '@react-navigation/native';
import useAuthContext from 'src/contexts/auth/auth-context';
import { VERIFICATION_TYPE } from 'src/@types/utils';
import { UPDATE_PROFILE, UPDATE_STORE_DETAILS, CountryInterface } from 'catlog-shared';

interface TasksProps {
  tasks: SetupTask[];
  storeId: string;
  storeCountry: CountryInterface;
  onboardingSteps: {};
  disabled?: boolean;
}

const SetupProgressTasks: React.FC<TasksProps> = props => {
  const { tasks, storeId, onboardingSteps, storeCountry } = props;
  const { user } = useAuthContext();
  const navigation = useNavigation();

  const updateStoreRequest = useApi(
    {
      apiFunction: UPDATE_STORE_DETAILS,
      key: 'update-store-request',
      method: 'PATCH',
    },
    { id: storeId },
  );

  const updateProfileRequest = useApi({
    apiFunction: UPDATE_PROFILE,
    key: 'update-profile-request',
    method: 'PATCH',
  });

  const handleOnPressStep = async (type: SETUP_TYPE, isPrimaryAction: boolean) => {
    switch (type) {
      case SETUP_TYPE.UPLOAD_PRODUCT:
        if (isPrimaryAction) {
          navigation.navigate('SelectAddingMethod');
        } else {
          updateStoreRequest.makeRequest({
            id: storeId,
            onboarding_steps: { ...onboardingSteps, products_added: true },
          });
        }
        break;
      case SETUP_TYPE.ADD_LINK_TO_SOCIAL:
        showLoader('Updating store...', false, true);
        const [res, error] = await updateStoreRequest.makeRequest({
          id: storeId,
          onboarding_steps: { ...onboardingSteps, link_added: true },
        });
        if (error) {
          showError(error?.message);
        }

        break;
      case SETUP_TYPE.STORE_CATEGORIZATION:
        navigation.navigate('SelectCategorization');
        break;
      case SETUP_TYPE.GET_VERIFIED:
        navigation.navigate('Verify', {
          type: !user?.email_verified
            ? VERIFICATION_TYPE.EMAIL
            : !user?.phone_verified
              ? VERIFICATION_TYPE.PHONE
              : null,
        });
        break;
      case SETUP_TYPE.JOIN_COMMUNITY:
        if (isPrimaryAction) {
          Linking.openURL(getCommunityLink(storeCountry ?? 'NG'));
        } else {
          updateProfileRequest.makeRequest({
            onboarding_steps: { ...user.onboarding_steps, community_joined: true },
          });
        }
        break;
      default:
      //do nothing
      // console.log("Invalid action passed:: ", action);
    }
  };

  return (
    <View className="px-20">
      {tasks.map((item, index) => (
        <Task
          key={index}
          rightIcon={item.rightIcon}
          showBorder={tasks.length - 1 !== index}
          title={item.title}
          isCompleted={item.isCompleted ?? false}
          onPressIsCompleted={() => props.disabled || handleOnPressStep(item.type, false)}
          onPressActionBtn={() => props.disabled || handleOnPressStep(item.type, true)}
        />
      ))}
    </View>
  );
};

interface TaskProps {
  title: string;
  rightIcon: React.ReactNode;
  showBorder: boolean;
  isCompleted: boolean;
  onPressIsCompleted?: VoidFunction;
  onPressActionBtn?: VoidFunction;
}

const Task = ({ title, rightIcon, showBorder, isCompleted, onPressIsCompleted, onPressActionBtn }: TaskProps) => {
  // const [isCompleted, setIsCompleted] = useState(isCompleted);
  return (
    <View key={title}>
      <Row className={`py-20 ${showBorder && 'border-b border-b-grey-border'}`}>
        <Pressable activeOpacity={0.8} disabled={isCompleted} onPress={onPressIsCompleted}>
          {isCompleted ? (
            <CheckActive size={wp(18)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
          ) : (
            <View className="rounded-full border-[3px] border-grey-border w-20 h-20" />
          )}
        </Pressable>
        <BaseText fontSize={13} classes="flex-1 mx-10 text-black-secondary">
          {title}
        </BaseText>
        {isCompleted ? (
          <CircledIcon iconBg={'bg-accentGreen-pastel'}>
            <CheckActive size={wp(16)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
          </CircledIcon>
        ) : (
          <Pressable disabled={onPressActionBtn === undefined} onPress={onPressActionBtn}>
            {rightIcon}
          </Pressable>
        )}
      </Row>
    </View>
  );
};

export default SetupProgressTasks;
