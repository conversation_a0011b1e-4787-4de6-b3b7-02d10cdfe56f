import { ButtonVariant } from 'src/components/ui/buttons/button';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import Container from 'src/components/ui/container';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import { Dimensions, Linking, View } from 'react-native';
import { delay, hp, showError, showSuccess, wp } from 'src/assets/utils/js';
import { BagCross, Gift, KeySquare, TickCircle, Trash, UserAdd } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import * as Animatable from 'react-native-animatable';
import useSteps from 'src/hooks/use-steps';
import PasswordInput from '../ui/inputs/password-input';
import { useState } from 'react';
import { useApi } from 'src/hooks/use-api';
import { DELETE_ACCOUNT } from 'catlog-shared';
import AsyncStorage from '@react-native-async-storage/async-storage';
import useAuthStore from 'src/contexts/auth/store';
import { CONTACT_SUPPORT_WA_LINK } from 'src/assets/utils/js/constants';
import { useNavigation } from '@react-navigation/native';
import Pressable from '../ui/base/pressable';

interface Props extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
}

const width = Dimensions.get('window').width;

const DeleteAccountModal = ({ closeModal, ...props }: Props) => {
  const { clearUserData } = useAuthStore();
  const [password, setPassword] = useState('');
  const { isActive, step, changeStep } = useSteps(['deleteInfo', 'enterPassword']);

  const navigation = useNavigation();

  const deleteAccountReq = useApi({ apiFunction: DELETE_ACCOUNT, method: 'DELETE', key: 'delete-account' });
  const preDeleteAction = [
    {
      icon: (
        <CircledIcon className="p-8">
          <KeySquare size={wp(15)} color={colors.accentGreen.main} variant="Bold" />
        </CircledIcon>
      ),
      title: "If you can't update your email or password",
      ctaText: 'Contact Support',
      onPress: () => Linking.openURL(CONTACT_SUPPORT_WA_LINK),
    },
    {
      icon: (
        <CircledIcon className="p-8">
          <Gift size={wp(15)} color={colors.accentYellow.main} variant="Bold" />
        </CircledIcon>
      ),
      title: "If you'd like to cancel your subscription",
      ctaText: 'Cancel Subscription',
      onPress: () => {
        closeModal();
        navigation.navigate('ManageSubscription');
      },
    },
    {
      icon: (
        <CircledIcon className="p-8">
          <BagCross size={wp(15)} color={colors.accentRed.main} variant="Bold" />
        </CircledIcon>
      ),
      title: "If you'd like to delete all products or orders",
      ctaText: 'Contact Support',
      onPress: () => Linking.openURL(CONTACT_SUPPORT_WA_LINK),
    },
    {
      icon: (
        <CircledIcon className="p-8">
          <UserAdd size={wp(15)} color={colors.accentOrange.main} variant="Bold" />
        </CircledIcon>
      ),
      title: 'If you want to create a new account',
      ctaText: 'Contact Support',
      onPress: () => Linking.openURL(CONTACT_SUPPORT_WA_LINK),
    },
  ];

  const handleDeleteAccount = async () => {
    const [res, err] = await deleteAccountReq.makeRequest({ password });

    if (res) {
      closeModal();
      await delay(700);
      showSuccess('Account deleted successfully');
      await AsyncStorage.removeItem('auth-storage');
      clearUserData();
    }

    if (err) {
      showError(err);
    }
  };

  const isLoading = deleteAccountReq.isLoading;

  return (
    <BottomModal
      {...props}
      enableDynamicSizing
      closeModal={closeModal}
      onModalHide={() => changeStep('deleteInfo')}
      buttons={
        step === 'deleteInfo'
          ? [
              {
                text: 'Continue',
                onPress: () => changeStep('enterPassword'),
              },
            ]
          : [
              {
                text: 'Go Back',
                variant: ButtonVariant.LIGHT,
                onPress: () => changeStep('deleteInfo'),
                isLoading: isLoading,
              },
              { text: 'Delete', variant: ButtonVariant.DANGER, onPress: handleDeleteAccount, isLoading },
            ]
      }>
      <Container className="pb-50">
        <View style={{ display: step === 'deleteInfo' ? 'flex' : 'none' }}>
          <View className="items-center px-20">
            <View className={`bg-accentRed-pastel p-10 rounded-full`}>
              <CircledIcon className={`bg-accentRed-main p-20`}>
                <Trash variant="Bold" color={colors.white} size={wp(40)} />
              </CircledIcon>
            </View>
            <BaseText fontSize={20} type="heading" classes="mt-10">
              Delete Account
            </BaseText>
            <BaseText fontSize={14} classes="text-black-placeholder mt-8 text-center" lineHeight={22}>
              You should only delete your account if you no longer need Catlog. Consider the following options instead:
            </BaseText>
          </View>
          <View className="mt-18" style={{ gap: hp(10) }}>
            {preDeleteAction.map((i, idx) => (
              <Pressable key={i?.title} className="bg-grey-bgOne rounded-8 p-12" onPress={i.onPress}>
                <Row>
                  {i.icon}
                  <View className="flex-1 ml-10">
                    <BaseText weight="medium">{i.title}</BaseText>
                    <BaseText fontSize={12} weight="medium" className="text-primary-main mt-4">
                      {i.ctaText}
                    </BaseText>
                  </View>
                </Row>
              </Pressable>
            ))}
          </View>
        </View>
        <View style={{ display: step === 'enterPassword' ? 'flex' : 'none' }}>
          <BaseText type="heading" fontSize={14} classes="mt-10">
            Enter password to continue
          </BaseText>
          <PasswordInput containerClasses="mt-25" useBottomSheetInput value={password} onChangeText={setPassword} />
        </View>
      </Container>
    </BottomModal>
  );
};

export default DeleteAccountModal;
