import React, { ReactNode } from 'react';
import FeatureFlagsWidget from './feature-flags-widget';
import { DevToolsBubble } from 'react-native-react-query-devtools';

interface DevToolsProviderProps {
  children: ReactNode;
}

const DevToolsProvider = ({ children }: DevToolsProviderProps) => {
  return (
    <>
      {children}
      {__DEV__ && <><FeatureFlagsWidget /><DevToolsBubble /></>}
    </>
  );
};

export default DevToolsProvider;