import React, { useState } from 'react';
import { View, ScrollView, Pressable, Platform, Dimensions } from 'react-native';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import { ArrowRight, Setting2 } from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import { wp } from '@/assets/utils/js';
import useFeatureFlagsStore from '@/contexts/feature-flags/store';
import CustomSwitch from '../ui/inputs/custom-switch';
import { useFeatureFlags } from '@/contexts/feature-flags/use-feature-flags';
import ListItemCard from '../ui/cards/list-item-card';
import SectionContainer from '../ui/section-container';
import Modal from 'react-native-modal';
import Separator from '../ui/others/separator';

import { GestureDetector, Gesture } from 'react-native-gesture-handler';
import Animated, { useAnimatedStyle, useSharedValue, withSpring, withTiming } from 'react-native-reanimated';
import { navigate, resetRoutes } from 'src/navigation';

const FeatureFlagsWidget = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { flags } = useFeatureFlags();
  const setFlags = useFeatureFlagsStore(state => state.setFlags);

  const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

  const translateX = useSharedValue(-25);
  const translateY = useSharedValue(screenHeight - 150);

  const panGesture = Gesture.Pan()
    .onUpdate(event => {
      // translateX.value = event.absoluteX - 25;
      // translateY.value = event.absoluteY - 25;
      translateX.value = withSpring(event.absoluteX - 25);
      translateY.value = withSpring(event.absoluteY - 25);
    })
    .onEnd(() => {
      const iconSize = 50;

      // Ensure widget stays on screen
      if (translateX.value < -25) translateX.value = -25;
      if (translateX.value > 10) translateX.value = 10;
      if (translateX.value > screenWidth - iconSize) translateX.value = screenWidth - iconSize;

      if (translateY.value < 50) translateY.value = 50;
      if (translateY.value > screenHeight - iconSize - 100) {
        translateY.value = screenHeight - iconSize - 100;
      }
    });

  // Create animated style for the widget
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }, { translateY: translateY.value }],
    };
  });

  const toggleFlag = (flag: keyof typeof flags) => {
    setFlags({ [flag]: !flags[flag] });
  };

  const featuresList = Object.entries(flags).map(([key, value]) => ({
    key,
    value,
  }));

  const navigateToUpdates = () => {
    resetRoutes('ForceUpdate');
  };

  return (
    <>
      <GestureDetector gesture={panGesture}>
        <Animated.View
          style={[
            {
              position: 'absolute',
              zIndex: 9999,
            },
            animatedStyle,
          ]}>
          <Pressable onPress={() => setIsVisible(true)}>
            <CircledIcon className="bg-accentDarkRed-main p-14">
              <Setting2 size={wp(22)} color={colors.white} />
            </CircledIcon>
          </Pressable>
        </Animated.View>
      </GestureDetector>

      <Modal
        isVisible={isVisible}
        onBackdropPress={() => setIsVisible(false)}
        style={{ margin: 0, justifyContent: 'flex-end' }}>
        <View className="bg-white rounded-t-15 pt-15 max-h-1/2">
          <BaseText fontSize={20} type="heading" classes="mb-15 px-20 ">
            Developer Tools
          </BaseText>
          <Separator className="mx-0 mt-0 mb-0" />
          <ScrollView>
            <View className="px-20 pb-40 pt-15">
              <BaseText fontSize={14} type="heading" classes="mb-10">
                Feature Flags
              </BaseText>
              <SectionContainer className="mt-5">
                {featuresList.map((feature, index) => (
                  <ListItemCard
                    key={feature.key}
                    title={feature.key}
                    showBorder={index !== featuresList.length - 1}
                    rightElement={
                      <CustomSwitch
                        value={feature.value}
                        onValueChange={() => toggleFlag(feature.key as keyof typeof flags)}
                      />
                    }
                  />
                ))}
              </SectionContainer>
              <View className="mt-20">
                <BaseText fontSize={14} type="heading" classes="mb-10">
                  Role Simulation
                </BaseText>
                <SectionContainer className="mt-10 py-15">
                  <BaseText>Role Simulation goes here</BaseText>
                  {/* Add role simulation controls here */}
                </SectionContainer>
              </View>
              <View className="mt-20">
                <BaseText fontSize={14} type="heading" classes="mb-10">
                  Others
                </BaseText>
                <SectionContainer className="mt-10 py-15">
                  <Pressable onPress={navigateToUpdates}>
                    <Row>
                      <BaseText type="heading">Navigate to force updates screens</BaseText>
                      <ArrowRight size={wp(18)} color={colors.primary.main} />
                    </Row>
                  </Pressable>
                  {/* Add role simulation controls here */}
                </SectionContainer>
              </View>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </>
  );
};

export default FeatureFlagsWidget;
