import { ReactNode, useMemo } from 'react';
import { Text, View, ViewProps } from 'react-native';
import BaseText from '@/components/ui/base/base-text';
import colors from '@/theme/colors';
import Pressable, { PressableProps } from '../base/pressable';
import { styled } from 'nativewind';
import cx from 'classnames';

interface StatusPillProps extends Partial<ViewProps> {
  title: string;
  statusType?: StatusType;
  whiteBg?: boolean;
  greyBg?: boolean;
}

export enum StatusType {
  'SUCCESS' = 'Success',
  'SUCCESS_INVERTED' = 'Success_inverted',
  'DANGER' = 'Danger',
  'WARN' = 'Warn',
  'PROCESSING' = 'Processing',
  'DEFAULT' = 'Default',
}

const StatusPill = ({
  title,
  statusType = StatusType.SUCCESS,
  whiteBg = false,
  greyBg = false,
  className,
  ...props
}: StatusPillProps) => {
  const textColor = {
    [StatusType.DANGER]: 'text-accentRed-main',
    [StatusType.WARN]: 'text-accentYellow-main',
    [StatusType.PROCESSING]: 'text-accentOrange-main',
    [StatusType.SUCCESS]: 'text-accentGreen-main',
    [StatusType.SUCCESS_INVERTED]: 'text-white',
    [StatusType.DEFAULT]: 'text-black-muted',
  };

  const bgColor = {
    [StatusType.DANGER]: 'bg-accentRed-pastel',
    [StatusType.WARN]: 'bg-accentYellow-pastel',
    [StatusType.PROCESSING]: 'bg-accentOrange-pastel',
    [StatusType.SUCCESS]: 'bg-accentGreen-pastel',
    [StatusType.SUCCESS_INVERTED]: 'bg-accentGreen-main',
    [StatusType.DEFAULT]: 'bg-white',
  };

  return (
    <View
      className={cx(
        'flex flex-row py-5 px-10 items-center justify-center rounded-40',
        whiteBg ? 'bg-white' : greyBg ? 'bg-grey-bgOne' : bgColor[statusType],
        {
          hidden: !title || title === '',
        },
        className,
      )}
      {...props}>
      <BaseText fontSize={10} weight={'medium'} classes={`${textColor[statusType]}`}>
        {title}
      </BaseText>
    </View>
  );
};

export default StatusPill;
