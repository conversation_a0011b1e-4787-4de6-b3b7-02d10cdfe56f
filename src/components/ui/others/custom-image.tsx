import { styled } from 'nativewind';
import { View, ViewProps } from 'react-native';
import { Image, ImageProps } from 'expo-image';
import classNames from 'classnames';

interface CustomImageProps extends Partial<ViewProps> {
  imageProps: ImageProps;
  transparentBg?: boolean;
  cache?: boolean;
}

const CustomImage = ({ imageProps, className, cache = false ,transparentBg = true, ...props }: CustomImageProps) => {
  return (
    <View
      className={classNames('overflow-hidden', className, { 'bg-grey-extraLight': transparentBg === false })}
      {...props}>
      <Image className="w-full h-full" cachePolicy={cache ? 'memory-disk' : undefined} {...imageProps} />
    </View>
  );
};

export default CustomImage;
