import { ScrollView, View } from 'react-native';
import { BaseText } from '../base';
import * as Animatable from 'react-native-animatable';
import { CloseCircle, TickCircle } from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import { wp } from '@/assets/utils/js';
import CircledIcon from '@/components/ui/circled-icon';
import FixedBtnFooter from '../buttons/fixed-btn-footer';
import * as Updates from 'expo-updates';
import { SafeAreaProvider } from 'react-native-safe-area-context';

const ErrorBoundary = () => {
  const reloadApp = async () => {
    await Updates.reloadAsync();
  };

  
  return (
    <SafeAreaProvider className='flex-1 bg-white'>
      <View className='flex-1 bg-white'>
        <View className="flex-1 mx-20 items-center justify-center bg-white">
          <Animatable.View className="bg-accentRed-pastel p-10 rounded-full" animation={'zoomIn'} duration={300}>
            <Animatable.View animation={'zoomIn'} delay={75} duration={200}>
              <CircledIcon className="p-24 bg-accentRed-main">
                <Animatable.View animation={'zoomIn'} duration={300} delay={150}>
                  <CloseCircle variant="Bold" color={colors.white} size={wp(30)} />
                </Animatable.View>
              </CircledIcon>
            </Animatable.View>
          </Animatable.View>
          <BaseText fontSize={20} weight={'bold'} classes={`text-black-primary mt-10 text-center`} type="heading">
            We are sorry an error occurred, please try again later.
          </BaseText>
        </View>
        <FixedBtnFooter buttons={[{ text: 'Reload App', onPress: () => reloadApp() }]} />
      </View>
    </SafeAreaProvider>
  );
};

export default ErrorBoundary;
