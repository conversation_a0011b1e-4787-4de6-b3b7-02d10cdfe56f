import { ReactNode } from "react";
import { View } from "react-native";
import { actionIsAllowed, Permission } from "src/assets/utils/js/permissions";
import useAuthContext from "src/contexts/auth/auth-context";

type FunctionChild = (cond: boolean) => ReactNode;

interface Props {
  children?: ReactNode | FunctionChild;
  data: {
    permission?: Permission;
    planPermission?: Permission;
    countryPermission?: Permission;
  };
  controlRender?: boolean;
}

const Can: React.FC<Props> = ({ children, data, controlRender = true }) => {
  const { userRole, store, user } = useAuthContext();
  const permission = data?.permission;
  const planPermission = data?.planPermission;
  const countryPermission = data?.countryPermission;
  const storePlan = store?.subscription?.plan?.type;
  const isAllowed = actionIsAllowed({
    userRole,
    permission,
    planPermission,
    plan: storePlan,
    countryPermission,
    country: store?.country?.code,
  }); //returning

  if (typeof children === "function") return children(isAllowed);
  if (isAllowed) return children;

  return (
    <>
      {!controlRender && (
        <View
          className=""
        >
          {children}
        </View>
      )}
    </>
  );
};

export default Can;
