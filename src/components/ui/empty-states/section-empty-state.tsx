import React, { ReactNode } from 'react';
import { View, ViewProps } from 'react-native';
import { BaseText } from '@/components/ui';
import Row from '@/components/ui/row';
import Button from '../buttons/button';
import { styled } from 'nativewind';

export interface SectionEmptyStateProps extends Partial<ViewProps> {
  text?: string;
  icon?: ReactNode;
  btnText?: string;
  onPressBtn?: () => void;
}

const SectionEmptyState = ({ text, icon, btnText, onPressBtn, ...rest }: SectionEmptyStateProps) => {
  return (
    <View className="flex-1 items-center justify-center py-[75px]" {...rest}>
      {icon}
      <BaseText fontSize={12} weight={'medium'} classes={'text-black-placeholder'}>
        {text}
      </BaseText>
      {/* <Button className='mt-20' text={btnText!} onPress={onPressBtn}/> */}
    </View>
  );
};

export default styled(SectionEmptyState);
