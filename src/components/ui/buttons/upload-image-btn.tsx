import React, { Fragment, ReactNode, Ref, useEffect, useId, useImperativeHandle, useState } from 'react';
import { Image as ImageView, TouchableOpacity, View } from 'react-native';
import { BaseText, CircledIcon } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import { Close, CloudUpload } from '@/components/ui/icons';
import Row from '@/components/ui/row';
import { Image as ImageIcon, TickCircle } from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import { showSuccess, wp } from '@/assets/utils/js';
import useStatusbar from '@/hooks/use-statusbar';
import * as ImagePicker from 'expo-image-picker';
import Animated, { useAnimatedStyle, useSharedValue, withDelay, withSpring } from 'react-native-reanimated';
import { Image } from '@/@types/utils';
import useImageUploads from '@/hooks/use-file-uploads';

interface UploadImageBtnProps {
  setImageUrl: (url: string) => void;
  setTempImage: (image: string) => void;
  customBtn?: ( pickImage: VoidFunction, isUploading: boolean) => ReactNode;
}

const UploadImageBtn = ({ setImageUrl, setTempImage, customBtn }: UploadImageBtnProps) => {
  const [images, setImages] = useState<Image[]>([]);
  const image = images[0];
  const progress = image?.uploadProgress ?? 0;
  const isUploading = image && image.isUploading;

  useImageUploads(images, setImages);

  const progressWidth = useSharedValue(0);

  useEffect(() => {
    if (images?.[0]?.src) {
      setTempImage(images?.[0]?.src);
    }
  }, [images]);

  useEffect(() => {
    progressWidth.value = withDelay(500, withSpring(progress));
  }, [progress]);

  useEffect(() => {
    if (image?.url && image.url !== '') {
      showSuccess('Image uploaded successfully');
      setImageUrl(image.url);
    }
  }, [image?.url]);

  const progressStyle = useAnimatedStyle(() => {
    return {
      width: `${progressWidth.value}%`,
      height: '200%',
      top: 0,
      left: 0,
      position: 'absolute',
      backgroundColor: colors.accentGreen.pastel,
    };
  });

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.1,
      // base64: true,
    });

    if (!result.canceled) {
      const image = result.assets[0];
      const uriParts = image.uri.split('/');
      const name = uriParts[uriParts.length - 1];
      const currentTime = new Date().getTime();

      setImages([
        {
          file: null,
          url: '',
          name: image.fileName ?? name,
          src: image.uri,
          key: image.assetId ?? currentTime.toString(),
          isUploading: false,
          uploadProgress: 0,
          error: false,
        },
      ]);
    }
  };

  return (
    <Fragment>
      {customBtn ? (
        customBtn( pickImage, isUploading ?? false)
      ) : (
        <Pressable
          className="py-14 px-10 bg-grey-bgTwo rounded-[14px] border border-grey-border border-dashed mt-15 overflow-hidden"
          onPress={pickImage}>
          {isUploading && <Animated.View style={progressStyle} />}
          <Row>
            {!image?.url && (
              <CircledIcon iconBg="bg-primary-pastel">
                <View className="items-center justify-center bg-primary-main rounded-full p-5">
                  <ImageIcon variant={'Bold'} color={colors.white} size={wp(9)} />
                </View>
              </CircledIcon>
            )}
            {image?.url && (
              <ImageView source={{ uri: image?.src }} style={{ height: wp(36), width: wp(36), borderRadius: 8 }} />
            )}
            <View className="flex-1 mx-10">
              <BaseText
                classes="text-black-secondary"
                weight="medium"
                fontSize={12}
                numberOfLines={1}
                ellipsizeMode="tail">
                {!image?.src ? 'Click to upload business logo' : ''}
                {image?.src && !image?.isUploading ? image.name : ''}
                {image?.src && image?.isUploading ? 'Uploading...' : ''}
              </BaseText>
              <BaseText classes="text-black-placeholder mt-1" fontSize={11}>
                {!image?.src ? 'PNG, JPG | 10MB max.' : ''}
                {image?.src && !image?.isUploading ? 'Tap to change' : ''}
                {image?.src && image?.isUploading ? `${image.uploadProgress}% Completed` : ''}
              </BaseText>
            </View>
            <CircledIcon>
              {!image?.src && <CloudUpload size={20} currentColor={colors.black.placeholder} />}
              {image?.src && image?.isUploading && (
                <TouchableOpacity onPress={() => setImages([])}>
                  <Close size={20} currentColor={colors.black.placeholder} />
                </TouchableOpacity>
              )}
              {image?.src && !image?.isUploading && image.url && (
                <View className="h-32 w-32 rounded-full bg-accentGreen-transparent flex items-center justify-center">
                  <TickCircle size={20} variant="Bold" color={colors.accentGreen.main} />
                </View>
              )}
            </CircledIcon>
          </Row>
        </Pressable>
      )}
    </Fragment>
  );
};

export default UploadImageBtn;
