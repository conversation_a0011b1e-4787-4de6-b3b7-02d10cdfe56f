import { Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { BaseText } from '../ui';
import { Image } from 'react-native';
import { ReactNode, useEffect } from 'react';
import CircledIcon from './circled-icon';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import useKeyboard from 'src/hooks/use-keyboard';

export interface ScreenInfoHeaderProps {
  pageTitleTop?: string | ReactNode;
  pageTitleBottom?: string;
  colorPalette?: ColorPaletteType;
  iconElement?: ReactNode;
  customElements?: ReactNode;
  isTextFollowPalette?: boolean;
  hideIconOnKeyboardActive?: boolean;
}

export enum ColorPaletteType {
  'YELLOW' = 'Yellow',
  'RED' = 'Red',
  'ORANGE' = 'Orange',
  'GREEN' = 'Green',
  'PRIMARY' = 'Primary',
}

const ScreenInfoHeader = ({
  iconElement,
  colorPalette = ColorPaletteType.PRIMARY,
  pageTitleTop,
  pageTitleBottom,
  customElements,
  isTextFollowPalette = true,
  hideIconOnKeyboardActive = false,
}: ScreenInfoHeaderProps) => {
  const isKeyboardVisible = useKeyboard();

  const keyboardActive = useSharedValue(hideIconOnKeyboardActive ?isKeyboardVisible : false);

  useEffect(() => {
    if (hideIconOnKeyboardActive) {
      keyboardActive.value = isKeyboardVisible;
    }
  }, [isKeyboardVisible]);

  const iconContainerStyle = useAnimatedStyle(() => {
    return {
      opacity: keyboardActive.value ? withTiming(0) : withTiming(1),
      transform: [{ scale: keyboardActive.value ? withTiming(0) : withTiming(1) }],
      height: keyboardActive.value ? withTiming(0) : withTiming(80),
    };
  });

  const mainContainerStyle = useAnimatedStyle(() => {
    return {
      // opacity: keyboardActive.value ? withTiming(0) : withTiming(1),
      // transform: [{ scale: keyboardActive.value ? withTiming(0) : withTiming(1) }],
      // height: keyboardActive.value ? withTiming(0) : withTiming(80),
      padding: keyboardActive.value ? withTiming(15) : withTiming(30),
    };
  });

  // const keyboardActiveHeaderStyle = useAnimatedStyle(() => {
  //   return {
  //     opacity: keyboardActive.value ? withTiming(1) : withTiming(0),
  //   };
  // });

  const bottomTextColor = isTextFollowPalette ? palletVariants[colorPalette].textColor : 'text-black-main';
  return (
    <Animated.View style={mainContainerStyle} className={`items-center justify-center ${palletVariants[colorPalette].bgColor}`}>
      <Animated.View style={iconContainerStyle}>
        {iconElement ? iconElement : <CircledIcon className="bg-white p-30" />}
      </Animated.View>
      {pageTitleTop && (
        <BaseText fontSize={22} classes={`mt-10 font-fhOscarLight text-black}`} type="heading">
          {pageTitleTop}
        </BaseText>
      )}
      {pageTitleBottom && (
        <BaseText fontSize={22} weight={'bold'} classes={`${bottomTextColor} mt-3`} type="heading">
          {pageTitleBottom}
        </BaseText>
      )}
      {customElements}
    </Animated.View>
  );
};

export const palletVariants = {
  [ColorPaletteType.ORANGE]: {
    textColor: 'text-accentOrange-main',
    bgColor: 'bg-accentOrange-pastel',
  },
  [ColorPaletteType.PRIMARY]: {
    textColor: 'text-primary-main',
    bgColor: 'bg-primary-pastel',
  },
  [ColorPaletteType.RED]: {
    textColor: 'text-accentRed-main',
    bgColor: 'bg-accentRed-pastel',
  },
  [ColorPaletteType.YELLOW]: {
    textColor: 'text-accentYellow-main',
    bgColor: 'bg-accentYellow-pastel',
  },
  [ColorPaletteType.GREEN]: {
    textColor: 'text-accentGreen-main',
    bgColor: 'bg-accentGreen-pastel2',
  },
};

export default ScreenInfoHeader;
