import { View } from 'react-native';
import Row from '@/components/ui/row';
import colors from '@/theme/colors';
import { LineChart, LineChartPropsType, lineDataItem } from 'react-native-gifted-charts';
import { hp, wp } from '@/assets/utils/js';
import { BaseText } from '@/components/ui';
import Separator from '@/components/ui/others/separator';
import { ChevronDown } from '../icons';
import SelectDropdown from '../inputs/select-dropdown';
import MoreOptions from '../more-options';
import { TextProps } from 'react-native';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { TimeRange } from 'src/@types/utils';
import Pressable from '../base/pressable';

type Interval = 'days' | 'months' | 'hours';

interface GraphData {
  data?: { time: string | Date; value: string | number }[][];
  range: TimeRange;
}

interface AreaGraphProps extends LineChartPropsType {
  title: string;
  rawData: GraphData['data'];
  className?: TextProps['className'];
  range: TimeRange;
  setRange?: (r: TimeRange) => void;
  isLoadingData?: boolean;
  labels?: {
    topPrefix?: string;
    bottomPrefix?: string;
  };
  theme?: 'green' | 'primary';
  error?: any;
  onPressRetry?: VoidFunction;
}

const AreaGraph = ({
  title,
  className,
  labels,
  rawData,
  isLoadingData,
  range = TimeRange.THIS_YEAR,
  setRange,
  theme = 'green',
  error,
  onPressRetry,
  ...props
}: AreaGraphProps) => {
  const hasError = Boolean(error);

  const optionElement = [
    {
      title: 'All Time',
      onPress: () => setRange?.(TimeRange.ALL_TIME),
    },
    {
      title: 'This Year',
      onPress: () => setRange?.(TimeRange.THIS_YEAR),
    },
    {
      title: 'Last 30 Days',
      onPress: () => setRange?.(TimeRange.LAST_30_DAYS),
    },
    {
      title: 'Last week',
      onPress: () => setRange?.(TimeRange.LAST_WEEK),
    },
    {
      title: 'This week',
      onPress: () => setRange?.(TimeRange.THIS_WEEK),
    },
    {
      title: 'Today',
      onPress: () => setRange?.(TimeRange.TODAY),
    },
  ];

  const dataIsEmpty = rawData?.every(data => data.length === 0);
  const series = useMemo(() => getSeries({ data: rawData, range }), [rawData, range]);

  return (
    <View className={classNames('flex-1', className)}>
      <Row>
        <Row>
          <BaseText fontSize={15} weight={'bold'} type={'heading'}>
            {title}
          </BaseText>
          <ChevronDown size={15} primaryColor={colors.grey.muted} />
        </Row>
        <MoreOptions
          options={optionElement}
          customMenuElement={
            <Row className="items-center bg-grey-bgOne py-7 px-10 rounded-full">
              <BaseText fontSize={12} classes="mr-4" style={{ color: colors.black.muted }}>
                {range}
              </BaseText>
              <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.grey.muted} />
            </Row>
          }
        />
      </Row>
      <View className="mt-20">
        {hasError && (
          <View style={{ height: hp(160) }} className="flex justify-between items-center flex-col relative">
            {[0, 1, 2, 3, 4].map((_, index) => (
              <View className="h-1 w-full bg-grey-bgTwo" key={index} />
            ))}
            <Pressable
              className="h-full w-full absolute flex flex-col items-center justify-center"
              onPress={onPressRetry}>
              <View className="justify-center items-center bg-white p-5">
                <BaseText fontSize={12} classes="text-center text-grey-muted">
                  We couldn't load your store analytics right{'\n'}now.{' '}
                  <BaseText fontSize={12} classes="text-center text-primary-main">
                    Refresh now{' '}
                  </BaseText>
                  or try again later.
                </BaseText>
              </View>
            </Pressable>
          </View>
        )}
        {!hasError && dataIsEmpty && (
          <View style={{ height: hp(160) }} className="flex justify-between items-center flex-col relative">
            {[0, 1, 2, 3, 4].map((_, index) => (
              <View className="h-1 w-full bg-grey-bgTwo" key={index} />
            ))}
            <View className="h-full w-full absolute flex flex-col items-center justify-center">
              <View className="justify-center items-center bg-white p-5">
                <BaseText fontSize={11} type="body" style={{ color: colors.grey.muted }}>
                  {isLoadingData ? 'Loading...' : 'No data Available'}
                </BaseText>
              </View>
            </View>
          </View>
        )}
        {!dataIsEmpty && !hasError && (
          <LineChart
            curvature={0.3}
            spacing={wp(45)}
            initialSpacing={wp(16)}
            endSpacing={wp(15)}
            interpolateMissingValues={true}
            thickness={3}
            startOpacity={0.8}
            endOpacity={0.3}
            key={series?.length}
            areaChart
            curved
            isAnimated
            yAxisThickness={0}
            xAxisThickness={1}
            data={series ?? []}
            noOfSections={4}
            rulesType={'dashed'}
            rulesColor={colors.grey.borderTwo}
            hideDataPoints={true}
            onlyPositive={true}
            dataPointsHeight={hp(2)}
            dataPointsWidth={hp(2)}
            dataPointsColor={themeColors[theme].main}
            color={themeColors[theme].main}
            startFillColor={themeColors[theme].pastel}
            endFillColor={colors.white}
            curveType={1}
            xAxisLabelsVerticalShift={0}
            adjustToWidth={true}
            xAxisType={''}
            xAxisColor={colors.grey.borderTwo}
            yAxisTextStyle={{ color: colors.black.muted, fontFamily: 'FHOscar-Bold', fontSize: wp(12) }}
            xAxisLabelTextStyle={{
              color: colors.black.muted,
              fontFamily: 'Inter-Medium',
              fontSize: wp(10),
              marginLeft: wp(10),
            }}
            pointerConfig={{
              // pointerStripHeight: 160,
              pointerStripColor: colors.grey.borderTwo,
              pointerStripWidth: 1.5,
              pointerColor: themeColors[theme].main,
              radius: wp(4),
              pointerLabelWidth: 100,
              pointerLabelHeight: 90,
              activatePointersOnLongPress: true,
              autoAdjustPointerLabelPosition: true,
              pointerLabelComponent: (items: { value: number; label: string }[]) => {
                //TODO: Make pointerLabelComponent dynamic
                //TODO: Make pointerLabelComponent dynamic
                //TODO: Make pointerLabelComponent dynamic
                //TODO: Make pointerLabelComponent dynamic
                //TODO: Make pointerLabelComponent dynamic
                return (
                  <View className="flex-1 bg-black-main rounded-[6px] w-[120px]">
                    <Row className="justify-start py-8 px-12">
                      {labels?.topPrefix !== undefined && (
                        <BaseText fontSize={8} weight={'medium'} classes="text-white">
                          {labels?.topPrefix}{' '}
                        </BaseText>
                      )}
                      <BaseText fontSize={10} weight={'semiBold'} classes="text-white">
                        {items[0].value}
                      </BaseText>
                    </Row>
                    <Separator className="mx-0 my-0" />
                    <Row className="items-start py-8 px-12">
                      <BaseText fontSize={8} weight={'medium'} classes="text-white">
                        {items[0].label}
                      </BaseText>
                    </Row>
                  </View>
                );
              },
            }}
            {...props}
          />
        )}
      </View>
    </View>
  );
};

export const getSeries = (graphData: GraphData) => {
  const now = new Date(Date.now());
  const dataMap = graphData?.data?.map(() => new Map<string, number>());
  const chartLabels = Array<string>();

  const aggregateData = (start: dayjs.Dayjs, size: number, interval: Interval) => {
    for (let i = 0; i < size; i++) {
      const dateKey = getDateKey(start, interval);
      chartLabels.push(start.toString());
      start = start.add(1, interval);

      if (graphData.data) {
        prefillValueWithDateKey(dataMap!, dateKey);
      }
    }

    if (graphData.data) {
      computeDataValue(interval);
    }

    const seriesData = graphData.data ? dataMap?.map((map, index) => Array.from(map.entries())) : [];

    const formattedGraphData = seriesData
      ?.flat()
      .map(item => ({ label: formatLabel(interval, item[0]), value: item[1] }));

    return formattedGraphData;
  };

  const formatLabel = (interval: Interval, label: string) => {
    const intervalLabelMaps: { [key: string]: () => string } = {
      months: () => {
        return dayjs(label).format("MMM 'YY");
      },
      hours: () => {
        return dayjs(label).format('HH:mm');
      },
      days: () => {
        return dayjs(label).format('DD MMM');
      },
    };

    return intervalLabelMaps[interval]();
  };

  const prefillValueWithDateKey = (dataMap: Map<string, number>[], key: string) => {
    dataMap.forEach((map, index) => dataMap[index].set(key, 0));
  };

  const getDateKey = (date: dayjs.Dayjs, interval: Interval) => {
    const intervalFunctionMaps: { [key: string]: () => string } = {
      months: () => {
        const splits = date.toISOString().split('T')[0].split('-');
        return splits[0] + '-' + splits[1];
      },
      hours: () => {
        return date.toISOString().split(':')[0].trim();
      },
      days: () => {
        return date.toISOString().split('T')[0].trim();
      },
    };

    return intervalFunctionMaps[interval]();
  };

  const computeDataValue = (interval: Interval) => {
    graphData?.data?.forEach((data, index) => {
      data?.forEach?.(dataValue => {
        const dateKey = getDateKey(dayjs(dataValue.time), interval);
        const value = dataMap?.[index].get(dateKey);
        value !== undefined &&
          dataMap?.[index].set(dateKey, typeof dataValue.value === 'string' ? value + 1 : value + dataValue.value);
      });
    });
  };

  const getGraphData = () => {
    switch (graphData.range) {
      case TimeRange.TODAY: {
        const start = dayjs(now).startOf('day').minute(1);
        return aggregateData(start, 24, 'hours');
      }
      case TimeRange.THIS_WEEK: {
        const start = dayjs(now).day(0);
        return aggregateData(start, 7, 'days');
      }
      case TimeRange.LAST_WEEK: {
        const start = dayjs(now).day(-7);
        return aggregateData(start, 7, 'days');
      }
      case TimeRange.LAST_30_DAYS: {
        let start = dayjs(now).subtract(29, 'days');
        return aggregateData(start, 30, 'days');
      }
      case TimeRange.THIS_YEAR: {
        let start = dayjs(now).subtract(11, 'months');
        return aggregateData(start, 12, 'months');
      }
      case TimeRange.ALL_TIME: {
        const startYear = 2021;
        const monthCount = (now.getFullYear() - startYear + 1) * 12 - 1;
        let startDate = dayjs(now).year(startYear).startOf('year').add(1, 'hour');

        return aggregateData(startDate, monthCount, 'months');
      }
    }
  };

  return getGraphData();
};

export const getFilter = (range: TimeRange) => {
  const now = Date.now();

  const maps = {
    [TimeRange.ALL_TIME]: {
      from: dayjs(now).year(2021).format(),
      to: dayjs(now).format(),
    },
    [TimeRange.THIS_YEAR]: {
      from: dayjs(now).subtract(11, 'months').format(),
      to: dayjs(now).format(),
    },
    [TimeRange.LAST_30_DAYS]: {
      from: dayjs(now).subtract(30, 'days').format(),
      to: dayjs(now).format(),
    },
    [TimeRange.THIS_WEEK]: {
      from: dayjs(now).day(0).format(),
      to: dayjs(now).format(),
    },
    [TimeRange.LAST_WEEK]: {
      from: dayjs(now).day(-7).format(),
      to: dayjs(now).format(),
    },
    [TimeRange.TODAY]: {
      from: dayjs(now).startOf('day').format(),
      to: dayjs(now).format(),
    },
  };
  return maps[range as unknown as keyof typeof maps];
};

const themeColors = {
  green: {
    main: colors.accentGreen.main,
    pastel: colors.accentGreen.pastel2,
  },
  primary: {
    main: colors.primary.main,
    pastel: colors.primary.pastel,
  },
};
export default AreaGraph;
