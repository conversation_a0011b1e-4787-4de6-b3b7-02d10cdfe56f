import { View } from 'react-native';
import Row from '@/components/ui/row';
import colors from '@/theme/colors';
import { PieChart, PieChartPropsType, pieDataItem } from 'react-native-gifted-charts';
import { hp, wp } from '@/assets/utils/js';
import { BaseText } from '@/components/ui';
import Separator from '@/components/ui/others/separator';
import { ChevronDown } from '../icons';
import SelectDropdown from '../inputs/select-dropdown';
import MoreOptions from '../more-options';

interface DoughnutChartProps extends Partial<PieChartPropsType> {
  title: string;
  pieData: pieDataItem[];
}

const DoughnutChart = ({ title, pieData, ...props }: DoughnutChartProps) => {
  const calculateTotalValue = (data: pieDataItem[]): number => {
    return data.reduce((sum, entry) => sum + entry.value, 0);
}
  return (
    <View>
      <Row className="justify-start">
        <BaseText fontSize={15} weight={'bold'} type={'heading'}>
          {title}
        </BaseText>
        <ChevronDown size={15} primaryColor={colors.grey.muted} />
      </Row>
      <View className="mt-20 items-center">
        <PieChart
          data={pieData}
          donut
          textColor="black"
          radius={wp(223 / 2)}
          innerRadius={wp(150 / 2)}
          strokeWidth={4}
          strokeColor={colors.white}
          centerLabelComponent={() => (
            <View className="items-center justify-center">
              <BaseText fontSize={12} classes="text-black-placeholder">
                Total orders
              </BaseText>
              <BaseText fontSize={28} weight="bold" classes="mt-4">
                {calculateTotalValue(pieData) ?? 0}
              </BaseText>
            </View>
          )}
          {...props}
        />
      </View>
    </View>
  );
};

export default DoughnutChart;
