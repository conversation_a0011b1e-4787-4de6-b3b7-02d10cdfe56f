import { TextInput, TouchableOpacity } from 'react-native';
import colors from '@/theme/colors';
import { wp } from '@/assets/utils/js';
import Input, { InputProps } from './input';
import { Eye } from 'iconsax-react-native/src';
import { useState } from 'react';
import Pressable from '../base/pressable';
import { NumericFormat } from 'react-number-format';

interface MoneyInputProps extends Partial<InputProps> {
  inputRef?: React.MutableRefObject<TextInput>;
}

const MoneyInput = ({ inputRef, ...rest }: MoneyInputProps) => {
  const value = rest?.value?.toString().length > 0 ? Number(rest.value) : '0';
  return (
    <NumericFormat
      value={value}
      displayType="text"
      renderText={formattedValue => (
        <Input
          {...rest}
          value={formattedValue}
          onChangeText={t => {
            console.log('t: ', Number(t));
            const cleanedUpText = String(t).replace(/,/g, "")
            console.log('cleanedUpText: ', cleanedUpText);
            rest.onChangeText?.(cleanedUpText);
          }}
          keyboardType={'number-pad'}
        />
      )}
      thousandSeparator
    />
  );
};

export default MoneyInput;
