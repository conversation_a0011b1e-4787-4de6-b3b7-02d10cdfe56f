import { TouchableOpacity, View } from 'react-native';
import colors from '@/theme/colors';
import { sluggify, wp } from '@/assets/utils/js';
import Input, { InputProps } from './input';
import { ArrowDown2, ArrowDown3, Eye } from 'iconsax-react-native/src';
import { useEffect, useRef, useState } from 'react';
import Row from '../row';
import { BaseText } from '..';
import SelectDropdown, { DropDownItem, DropDownMethods } from './select-dropdown';
import { countryCodes } from 'catlog-shared';

interface PhoneNumberInputProps extends Partial<Omit<InputProps, 'onChange'>> {
  onChange: (value: { code: string; digits: string }) => void;
}

const PhoneNumberInput = ({ value, onChange, error, ...rest }: PhoneNumberInputProps) => {
  const [code, setCode] = useState(value?.code ?? '+234');
  const [digits, setDigits] = useState(value?.digits);
  const [accessoryWidth, setAccessoryWidth] = useState(50);

  const dropDownRef = useRef<DropDownMethods>(null);
  const theError = error?.code || error?.digits || '';

  useEffect(() => {
    onChange?.({ code, digits });
  }, [code, digits]);

  useEffect(() => {
    if (value?.code !== code && value?.code !== '') {
      setCode(value?.code);
    }

    if (value?.digits !== digits && value?.digits !== '') {
      setDigits(value?.digits);
    }
  }, [value]);

  const handleChange = (value: string) => {
    if (value.indexOf('0') === 0) {
      value = value.replace('0', '');
    }

    setDigits(value);
  };

  const LeftAccessory = () => (
    <TouchableOpacity
      onPress={() => dropDownRef.current?.open()}
      onLayout={e => setAccessoryWidth(e.nativeEvent.layout.width + 10)}>
      <Row>
        <BaseText fontSize={13} weight={'medium'} classes={'mr-2'}>
          {code}
        </BaseText>
        <ArrowDown2 variant={'Linear'} size={wp(12)} color={colors.black.main} />
      </Row>
    </TouchableOpacity>
  );

  return (
    <View>
      <Input
        leftPadding={accessoryWidth + 10}
        keyboardType={'number-pad'}
        label={'Phone Number'}
        leftAccessory={<LeftAccessory />}
        {...rest}
        value={digits}
        onChangeText={handleChange}
        error={theError}
        hasError={!!theError}
      />
      <SelectDropdown
        ref={dropDownRef}
        showAnchor={false}
        selectedItem={code}
        onPressItem={setCode}
        label={'Categories'}
        hasSearch
        items={countryCodeOptions}
        containerClasses="mt-15"
        genItemKeysFun={value => sluggify(value.label)}
      />
    </View>
  );
};

const countryCodeOptions: DropDownItem[] = countryCodes.map(country => ({
  value: country.dial_code,
  label: `(${country.dial_code}) ${country.name} ${country.emoji}`,
}));

export default PhoneNumberInput;
