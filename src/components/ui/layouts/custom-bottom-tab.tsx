import React, { Fragment, useEffect, useRef, useState } from 'react';
import { Platform, Text, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import colors from '@/theme/colors';
import { CustomersIcon, HomeIcon, PaymentsIcon, ProductsIcon, QuickActionsIcon } from '../icons';
import useAuthStore from 'src/contexts/auth/store';
import * as Animatable from 'react-native-animatable';
import { BaseText } from '../base';

export enum AppTabs {
  HOME = 'Home',
  ORDERS = 'Orders',
  PRODUCTS = 'Products',
  QUICKACTIONS = 'Quick Actions',
  PAYMENTS = 'Payments',
}

const CustomBottomTab = ({ state, descriptors, navigation }: BottomTabBarProps) => {
  const [isActionModalVisible, setisActionModalVisible] = useState(false);
  const [show, setShow] = useState(true);
  const showBottomTab = useAuthStore(state => state.showBottomTab);

  const insets = useSafeAreaInsets();

  // const focusedOptions = descriptors[state.routes[state.index].key].options;
  // if (focusedOptions?.tabBarStyle?.display === 'none') {
  //   return null;
  // }
  const viewRef = useRef<Animatable.View>(null);

  // Register custom animations
  // Animatable.initializeRegistryWithDefinitions({
  //   customEntry: customEntryAnimation,
  //   customExit: customExitAnimation
  // });

  useEffect(() => {
    if (showBottomTab) {
      viewRef.current?.animate('bounceInUp', 200);
    } else {
      viewRef.current?.animate('bounceOutDown', 200);
    }
  }, [showBottomTab]);

  const handleModalClose = () => {
    setisActionModalVisible(false);
  };

  const handleModalOpen = () => {
    setisActionModalVisible(true);
  };

  const onPress = (route: { key: string; name: string }, isFocused: boolean) => {
    const event = navigation.emit({
      type: 'tabPress',
      target: route.key,
      canPreventDefault: true,
    });

    if (!isFocused && !event.defaultPrevented) {
      // The `merge: true` option makes sure that the params inside the tab screen are preserved
      navigation.navigate({ name: route.name, merge: true, params: {} });
    }
  };

  return (
    // <Fragment>
    //   {showBottomTab && (
    //   )}
    // </Fragment>

    <View className="flex-row justify-between pt-[20] h-[85] px-15 border-t border-grey-border bg-white">
      {state.routes.map((route, index) => {
        const label = route.name as AppTabs;
        const tabConfig = tabs[label];
        const isFocused = state.index === index;

        return (
          <Fragment key={label}>
            {label !== AppTabs.QUICKACTIONS && (
              <TouchableOpacity
                className="flex-1 items-center content-center"
                activeOpacity={0.95}
                onPress={() => onPress(route, isFocused)}>
                <>
                  {tabConfig?.icon(tabConfig?.colors[isFocused ? 'on' : 'off'])}
                  <BaseText
                    classes="px-2 mt-5"
                    fontSize={10}
                    weight="medium"
                    style={{ color: !isFocused ? colors.black.muted : tabConfig?.colors.active }}>
                    {label}
                  </BaseText>
                </>
              </TouchableOpacity>
            )}

            {label === AppTabs.QUICKACTIONS && (
              <TouchableOpacity
                onPress={() => onPress(route, isFocused)}
                activeOpacity={0.95}
                className=""
                style={{
                  transform: [{ translateY: -50 }],
                  // backgroundColor: !isFocused ? colors.grey.muted : tabConfig?.colors.active,
                }}>
                <View className="flex flex-col items-center">
                  <View
                    className="h-[70] w-[70] rounded-full flex items-center justify-center"
                    style={{
                      // transform: [{ translateY: -50 }],
                      backgroundColor: !isFocused ? colors.grey.muted : tabConfig?.colors.active,
                    }}>
                    {tabConfig?.icon(tabConfig?.colors[isFocused ? 'on' : 'off'])}
                  </View>
                  <BaseText
                    classes="px-2 mt-7"
                    fontSize={10}
                    weight="medium"
                    adjustsFontSizeToFit
                    style={{
                      color: !isFocused ? colors.black.muted : tabConfig?.colors.active,
                      // transform: [{ translateY: -43 }],
                    }}>
                    Quick Actions
                  </BaseText>
                </View>
              </TouchableOpacity>
            )}
          </Fragment>
        );
      })}
    </View>
  );
};

const tabs = {
  [AppTabs.HOME]: {
    icon: (colors: { [key: string]: string }) => <HomeIcon width={30} {...colors} />,
    colors: {
      active: colors?.primary.main,
      off: {
        primaryColor: colors.grey.mutedLight,
        secondaryColor: colors.grey.muted,
        currentColor: colors.grey.bgOne,
      },
      on: {
        primaryColor: colors.primary.main,
        secondaryColor: colors.primary.light,
        currentColor: colors.primary.extraLight,
      },
    },
  },
  [AppTabs.ORDERS]: {
    icon: (colors: { [key: string]: string }) => <CustomersIcon width={30} {...colors} />,
    colors: {
      active: colors?.accentOrange.main,
      off: {
        currentColor: colors.grey.muted,
      },
      on: {
        currentColor: colors.accentOrange.main,
      },
    },
  },
  [AppTabs.PRODUCTS]: {
    icon: (colors: { [key: string]: string }) => <ProductsIcon width={30} {...colors} />,
    colors: {
      active: colors?.accentYellow.main,
      off: {
        primaryColor: colors.grey.muted,
        secondaryColor: colors.grey.mutedLight,
        currentColor: colors.grey.extraLight,
      },
      on: {
        primaryColor: colors.accentYellow.main,
        secondaryColor: colors.accentYellow.light,
        currentColor: colors.accentYellow.extraLight,
      },
    },
  },
  [AppTabs.PAYMENTS]: {
    icon: (colors: { [key: string]: string }) => <PaymentsIcon width={30} {...colors} />,
    colors: {
      active: colors?.accentGreen.main,
      off: {
        primaryColor: colors.grey.extraLight,
        currentColor: colors.grey.muted,
      },
      on: {
        currentColor: colors.accentGreen.main,
        primaryColor: colors.accentGreen.extraLight,
      },
    },
  },
  [AppTabs.QUICKACTIONS]: {
    icon: (colors: { [key: string]: string }) => <QuickActionsIcon width={50} {...colors} />,
    colors: {
      active: colors?.accentRed.main,
      off: {
        currentColor: colors.white,
      },
      on: {
        currentColor: colors.white,
      },
    },
  },
};

export default CustomBottomTab;
