import { Dimensions, FlatListProps, View } from 'react-native';
import { ReactNode, useState } from 'react';
import BottomModal, { BottomModalProps } from './bottom-modal';
import { BaseText } from '../base';
import Calendar from 'react-native-calendar-range-picker';
import { formatDate, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import moment from 'moment';
import Row from '../row';
import CircledIcon from '../circled-icon';
import { Calendar2 } from 'iconsax-react-native/src';
import CalendarPicker, { DateChangedCallback } from 'react-native-calendar-picker';
import { ChevronUp } from '../icons';
import Toast from 'react-native-toast-message';
import { toastConfig } from '../others/toast-notification';

export interface CalendarModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  singleSelectMode?: boolean;
  onDateChange: DateChangedCallback;
  startDate?: Date;
  endDate?: Date;
  onPressProceed?: VoidFunction;
  maxDate?: Date;
  minDate?: Date;
  initialDate?: Date;
  //use instead of title and description if its different
}

const CalendarModal = ({
  closeModal,
  onDateChange,
  onPressProceed,
  startDate,
  endDate,
  singleSelectMode = false,
  maxDate,
  minDate,
  initialDate,
  ...props
}: CalendarModalProps) => {
  const isToday = moment().format('YYYY-MM-DD') === moment(startDate).format('YYYY-MM-DD');
  const isRange = Boolean(startDate) && Boolean(endDate);


  return (
    <BottomModal
      // size="lg"
      enableDynamicSizing
      enableSnapPoints={false}
      buttons={[{ text: 'Select date', onPress: onPressProceed ?? closeModal }]}
      closeModal={closeModal}
      {...props}>
      <View className="px-0 pb-20">
        <CalendarPicker
          showDayStragglers
          onDateChange={onDateChange}
          selectedDayColor={colors.primary.main}
          selectedDayTextColor={colors.white}
          allowRangeSelection={singleSelectMode ? false : true}
          selectedStartDate={startDate}
          selectedEndDate={endDate}
          allowBackwardRangeSelect={true}
          todayBackgroundColor={colors.black.main}
          todayTextStyle={{ fontFamily: 'Inter-SemiBold', color: colors.primary.main }}
          selectedRangeStyle={{ backgroundColor: colors.primary.pastel }}
          selectedDayTextStyle={{ fontFamily: 'Inter-SemiBold', color: isRange ? colors.primary.main : colors.white }}
          selectedRangeStartTextStyle={{ color: colors.white }}
          selectedRangeEndTextStyle={{ color: colors.white }}
          selectedRangeStartStyle={{ backgroundColor: colors.primary.main }}
          selectedRangeEndStyle={{ backgroundColor: colors.primary.main }}
          previousComponent={
            <CircledIcon className="bg-grey-bgOne">
              <View className="-rotate-90">
                <ChevronUp size={wp(16)} strokeWidth={2} currentColor={colors.primary.main} />
              </View>
            </CircledIcon>
          }
          nextComponent={
            <CircledIcon className="bg-grey-bgOne">
              <View className="rotate-90">
                <ChevronUp size={wp(16)} strokeWidth={2} currentColor={colors.primary.main} />
              </View>
            </CircledIcon>
          }
          monthTitleStyle={{ fontSize: wp(16), fontFamily: 'FHOscar-Bold' }}
          yearTitleStyle={{ fontSize: wp(16), fontFamily: 'FHOscar-Bold' }}
          dayLabelsWrapper={{
            backgroundColor: colors.grey.bgOne,
            borderRadius: 10,
            borderTopWidth: 0,
            borderBottomWidth: 0,
            marginTop: 5,
            marginBottom: 10,
          }}
          maxDate={maxDate ?? undefined}
          minDate={minDate ?? undefined}
          initialDate={initialDate}
        />
      </View>
      {startDate && (
        <Row className="px-20 justify-start pt-2 pb-8">
          <CircledIcon iconBg="bg-grey-bgOne" className="p-6 mr-8">
            <Calendar2 size={wp(15)} color={colors.black.muted} />
          </CircledIcon>
          <BaseText fontSize={12} classes="text-black-placeholder">
            {!singleSelectMode ? (
              <>
                {startDate && formatDate(startDate)} - {endDate ? `${formatDate(endDate)}` : 'Till Deleted'}
              </>
            ) : (
              <> {startDate && formatDate(startDate)}</>
            )}
          </BaseText>
        </Row>
      )}
      <Toast config={toastConfig} topOffset={60} />
    </BottomModal>
  );
};

export default CalendarModal;
