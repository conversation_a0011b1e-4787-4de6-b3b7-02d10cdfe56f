import { Dimensions, Modal, Platform, View } from 'react-native';
import { FormikProps, useFormik } from 'formik';
import { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import Toast from 'react-native-toast-message';
import React, { ReactNode } from 'react';
import { VariantForm } from 'catlog-shared';
import useStatusbar from 'src/hooks/use-statusbar';
import { toastConfig } from 'src/components/ui/others/toast-notification';

interface SheetModalProps extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
  children: ReactNode;
}

const screenHeight = Dimensions.get('screen').height;

const { width } = Dimensions.get('window');
const cardWidth = (width - 40 - 20) / 2;

const SheetModal = ({ closeModal, children, ...props }: SheetModalProps) => {
  const { setStatusBar } = useStatusbar();

  return (
    <Modal
      onModalShow={() => setStatusBar(Platform.OS === 'android' ? 'dark' : 'light')}
      onModalHide={() => setStatusBar('dark')}
      animationType="slide"
      visible={props.isVisible}
      presentationStyle={Platform.OS === 'android' ? 'overFullScreen' : 'pageSheet'}
      onRequestClose={closeModal}
      style={{ margin: 0, flex: 1 }}
      {...props}>
      {/* <View className="self-center w-50 h-5 rounded-full bg-grey-mutedLight mt-10" /> */}
      {children}
      <Toast config={toastConfig} topOffset={20} />
    </Modal>
  );
};

export default SheetModal;
