import { ScrollView, View } from 'react-native';
import Input from '../ui/inputs/input';
import PhoneNumberInput from '../ui/inputs/phone-number-input';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { delay, getFieldvalues } from '@/assets/utils/js';
import { phoneValidation } from '@/assets/utils/js/common-validations';
import { useApi } from '@/hooks/use-api';
import Toast from 'react-native-toast-message';
import { useEffect } from 'react';
import {
  CreateCustomerParams,
  UpadateCustomerParams,
  CustomerInterface,
  CREATE_CUSTOMER,
  UPDATE_CUSTOMER,
} from 'catlog-shared';

interface AddCustomerModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressButton?: () => void;
  callBack?: (updateData?: CustomerInterface) => void;
  btnTitle?: string;
  activeCustomer?: CustomerInterface;
  isEdit?: boolean;
}

export interface CustomerFormParams extends Omit<CreateCustomerParams, 'phone'> {
  phone: { code: string; digits: string };
  id?: string;
}

const AddCustomerModal = ({
  closeModal,
  callBack,
  activeCustomer,
  isEdit,
  btnTitle,
  ...props
}: AddCustomerModalProps) => {
  const createCustomerRequest = useApi<CreateCustomerParams>({
    apiFunction: CREATE_CUSTOMER,
    method: 'POST',
    key: 'create-customer',
  });

  const updateCustomerRequest = useApi<UpadateCustomerParams>({
    apiFunction: UPDATE_CUSTOMER,
    method: 'PUT',
    key: 'update-customer',
  });

  const form = useFormik<CustomerFormParams>({
    initialValues: {
      id: '',
      name: '',
      email: '',
      phone: {
        code: '+234',
        digits: '',
      },
    },
    onSubmit: async values => {
      const phone = `${values.phone.code}-${values.phone.digits}`;
      let response = null;
      let error = null;

      if (isEdit) {
        const editResponse = await updateCustomerRequest.makeRequest({
          id: values.id!,
          name: values.name,
          email: values.email,
          phone,
        });

        response = editResponse[0];
        error = editResponse[1];
      } else {
        const createResponse = await createCustomerRequest.makeRequest({
          name: values.name,
          email: values.email,
          phone,
        });

        response = createResponse[0];
        error = createResponse[1];
      }

      if (response) {
        form.resetForm();
        callBack?.(response?.data);
        closeModal();
        await delay(700);
        Toast.show({ type: 'success', text1: `Customer ${isEdit ? 'updated' : 'created'} successfully` });
        return;
      }

      Toast.show({ type: 'error', text1: error.body.body.message ?? 'Something went wrong' });
    },
    enableReinitialize: true,
  });

  useEffect(() => {
    if (!isEdit) {
      form.resetForm();
    }
    if (isEdit && activeCustomer) {
      form.setFieldValue('id', activeCustomer.id);
      form.setFieldValue('name', activeCustomer.name);
      form.setFieldValue('email', activeCustomer.email);
      const phoneSplit = {
        code: activeCustomer?.phone?.split('-')[0],
        digits: activeCustomer?.phone.includes('-') ? activeCustomer?.phone?.split('-')[1] : activeCustomer?.phone,
      };
      form.setFieldValue('phone', phoneSplit);
    }
  }, [activeCustomer]);

  const preOpeningAction = () => {
    if (!isEdit) {
      form.resetForm();
    }
    if (isEdit && activeCustomer) {
      form.setFieldValue('id', activeCustomer.id);
      form.setFieldValue('name', activeCustomer.name);
      form.setFieldValue('email', activeCustomer.email);
      const phoneSplit = {
        code: activeCustomer?.phone?.split('-')[0],
        digits: activeCustomer?.phone.includes('-') ? activeCustomer?.phone?.split('-')[1] : activeCustomer?.phone,
      };
      form.setFieldValue('phone', phoneSplit);
    }
  };

  return (
    <BottomModal
      size="sm"
      {...props}
      closeModal={closeModal}
      title={isEdit ? 'Edit Customer' : 'Add Customer'}
      onModalShow={preOpeningAction}
      buttons={[
        {
          text: btnTitle ? btnTitle : 'Done',
          onPress: () => form.handleSubmit(),
          isLoading: createCustomerRequest.isLoading || updateCustomerRequest.isLoading,
        },
      ]}>
      <ScrollView keyboardShouldPersistTaps={'handled'}>
        <View className="my-20 px-20">
          <Input useBottomSheetInput label={'Full name'} {...getFieldvalues('name', form)} />
          <PhoneNumberInput
            label={'Phone Number'}
            useBottomSheetInput
            containerClasses="mt-15"
            {...getFieldvalues('phone', form)}
            onChange={value => form.setFieldValue('phone', value)}
          />
          <Input
            useBottomSheetInput
            label={'Email Address'}
            keyboardType={'email-address'}
            autoCapitalize='none'
            containerClasses="mt-15"
            {...getFieldvalues('email', form)}
          />
        </View>
      </ScrollView>
    </BottomModal>
  );
};

export const adCustomerValidationSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  phone: phoneValidation(),
  email: Yup.string().email('Invalid email address'),
});

export default AddCustomerModal;
