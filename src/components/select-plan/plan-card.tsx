import React, { ReactNode } from 'react';
import { View, ViewProps } from 'react-native';
import cx from 'classnames';
import Pressable, { PressableProps } from '../ui/base/pressable';
import { styled } from 'nativewind';
import { BaseText } from '../ui';
import Row from '../ui/row';
import { ArrowUpRight, CheckActive } from '../ui/icons';
import { hp, toCurrency, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import Radio from '../ui/buttons/radio';
import PillTextButton from 'src/components/ui/buttons/pill-text-button';
import { COUNTRY_CURRENCY_MAP, Plan } from 'catlog-shared';
import { GroupedPlan } from 'src/screens/setup/pick-plan';

export interface PlanCardProps extends Partial<PressableProps> {
  children?: ReactNode;
  plan?: GroupedPlan;
  selected: boolean;
  greatChoice: boolean;
  onPressPlansFeatures: () => void;
}

const PlanCard = ({
  greatChoice,
  plan,
  onPressPlansFeatures,
  selected,
  
  ...props
}: PlanCardProps) => {
  return (
    <View className={cx({"bg-accentGreen-pastel2 items-center rounded-15": greatChoice})}>
      {greatChoice && (
            <Row classes={'justify-start px-8 rounded-full mt-15'}>
              <CheckActive size={wp(12)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
              <BaseText fontSize={12} weight={'medium'} classes={'text-accentGreen-main leading-[20px] ml-4'}>
                Great Choice
              </BaseText>
            </Row>
          )}
    <Pressable className={cx('rounded-[15px] w-full bg-white border border-grey-border overflow-hidden')} {...props}>
      <View className="absolute right-15 top-15">
        <Radio active={selected} />
      </View>
      <View className="px-15 pt-15">
        <Row classes="justify-start mb-10">
          <BaseText type={'heading'} fontSize={15} classes={'font-fhOscarBold mr-8 leading-[24px]'}>
            {plan.name}
          </BaseText>
          <PillTextButton label=' 14 days Free' textClassName='text-black-muted'/>
           
        </Row>
        <BaseText type={'heading'} fontSize={25} classes={'font-fhOscarBold leading-[40px]'}>
          {toCurrency(plan.amount, plan.country?.currency,false,0)}
          <BaseText fontSize={12} classes={'font-interRegular leading-[20px] text-black-placeholder'}>
            {' '}
             {plan.interval_text}
          </BaseText>
        </BaseText>
        {/* <BaseText fontSize={12} classes={'font-interRegular mt-5 text-black-placeholder'}>
          {(plan.description[0].features[0])} ...
        </BaseText> */}
      </View>
      <Pressable onPress={onPressPlansFeatures}>
        <Row classes="justify-start bg-grey-bgOne py-10 px-15 mt-10">
          <BaseText fontSize={12} weight={'medium'} classes={'text-primary-main mr-4'}>
            Plan features
          </BaseText>
          <ArrowUpRight size={wp(15)} strokeWidth={1.5} currentColor={colors.primary.main} />
        </Row>
      </Pressable>
    </Pressable>
    </View>
  );
};

export default styled(PlanCard);
