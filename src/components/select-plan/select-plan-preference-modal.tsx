import {
  CURRENCIES,
  PLAN_TYPE,
  Plan,
  PlanOption,
  ChangePlanParams,
} from 'catlog-shared';
import { Gift } from 'iconsax-react-native/src';
import { useMemo } from 'react';
import { View } from 'react-native';
import { toCurrency, toNaira, wp } from 'src/assets/utils/js';
import { BaseText } from 'src/components/ui';
import Radio from 'src/components/ui/buttons/radio';
import ListItemCard from 'src/components/ui/cards/list-item-card';
import CircledIcon from 'src/components/ui/circled-icon';
import Container from 'src/components/ui/container';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import { ApiData, useApi } from 'src/hooks/use-api';
import colors from 'src/theme/colors';

interface Props extends Partial<BottomModalProps> {
  closeModal: () => void;
  isLoading?: boolean;

  subscribeToPlan: (plan: string, plan_option: string) => void;
  selectedPlan: Plan;
  payUpfront: boolean;
  setPayUpfront: (state: boolean) => void;
  currency: CURRENCIES;
  subscriptionRequest: ApiData<ChangePlanParams, any>;
  selectedPlanOption: PlanOption & { plan_option_id: string };
}
const SelectPlanPreferenceModal: React.FC<Props> = ({
  closeModal,
  selectedPlan,
  selectedPlanOption,
  payUpfront,
  isLoading,
  setPayUpfront,
  currency,
  subscribeToPlan,
  subscriptionRequest,

  ...props
}) => {
  const [upfrontAmount, normalAmount] = useMemo(() => {
    const upfrontAmount = UPFRONT_SUBSCRIPTION_AMOUNTS[selectedPlan.type][currency];

    return [
      toCurrency(toNaira(upfrontAmount), currency, false, 0),
      toCurrency(selectedPlanOption.amount, currency, false, 0),
    ];
  }, [selectedPlan, selectedPlanOption]);

  return (
    <BottomModal
      closeModal={closeModal}
      enableDynamicSizing
      {...props}
      title="One time offer"
      buttons={[
        {
          text: payUpfront ? `Use for 30 days` : `Pay ${normalAmount} after 7 days`,
          loadingText: 'Subscribing...',
          onPress: () => subscribeToPlan(selectedPlan.id, selectedPlanOption.plan_option_id),
        },
      ]}>
      <Container className=" mt-10">
        <View className="items-center">
          <CircledIcon className="bg-accentOrange-main p-16">
            <Gift variant={'Bold'} color={colors.white} size={wp(24)} />
          </CircledIcon>
          <BaseText type="heading" fontSize={22} classes="text-center mt-10">
            Here's a special{'\n'}
            offer for you
          </BaseText>
        </View>
        <View className="mt-10">
          {options(upfrontAmount, normalAmount).map((option, i) => (
            <ListItemCard
              key={i}
              title={option.label}
              // showBorder={options.length !== i + 1}
              containerClasses="bg-grey-bgOne rounded-12 pr-12 mt-10"
              description={option.description}
              descriptionProps={{
                fontSize: 12,
                type: 'body',
                weight: 'light',
                classes: 'text-black-muted',
              }}
              titleProps={{
                fontSize: 14,
                type: 'heading',
                classes: 'text-black-main',
              }}
              rightElement={<Radio active={option.key === 'upfront' ? payUpfront : !payUpfront} />}
              onPress={() => setPayUpfront(option.key === 'upfront')}
            />
          ))}
        </View>
      </Container>
    </BottomModal>
  );
};
export default SelectPlanPreferenceModal;

const options = (upfrontAmount: string, normalAmount: string) => [
  {
    key: 'upfront',
    label: `Pay ${upfrontAmount} Today`,
    description: 'To use your selected plan for 30 days',
  },
  {
    key: 'normal',
    label: 'Try free for 7 days',
    description: `You'll need to pay ${normalAmount} after 7 days`,
  },
];

export const UPFRONT_SUBSCRIPTION_AMOUNTS = {
  [PLAN_TYPE.BASIC]: {
    [CURRENCIES.NGN]: 2000_00,
    [CURRENCIES.GHC]: 25_00,
    [CURRENCIES.KES]: 250_00,
    [CURRENCIES.ZAR]: 49_00,
  },
  [PLAN_TYPE.BUSINESS_PLUS]: {
    [CURRENCIES.NGN]: 4250_00,
    [CURRENCIES.GHC]: 70_00,
    [CURRENCIES.KES]: 550_00,
    [CURRENCIES.ZAR]: 99_00,
  },
  // [PLAN_TYPE.KITCHEN]: {
  //   [CURRENCIES.NGN]: 15000_00,
  //   [CURRENCIES.GHC]: 250_00,
  //   [CURRENCIES.KES]: 2500_00,
  //   [CURRENCIES.ZAR]: 499_00, //placholder
  // },
};
