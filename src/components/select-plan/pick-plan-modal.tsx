import { useNavigation } from '@react-navigation/native';
import {
  COUNTRY_CURRENCY_MAP,
  CountryInterface,
  CREATE_FREE_SUBSCRIPTION,
  CURRENCIES,
  PAYMENT_TYPES,
  Plan,
  PlanOption,
} from 'catlog-shared';
import { Tag2 } from 'iconsax-react-native/src';
import { useMemo, useState } from 'react';
import { Text, View } from 'react-native';
import { delay, showLoader, showSuccess, toCurrency, toNaira } from 'src/assets/utils/js';
import PaymentsWidget from 'src/components/payments/payments-widget';
import { BaseText } from 'src/components/ui';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import Radio from 'src/components/ui/buttons/radio';
import ListItemCard from 'src/components/ui/cards/list-item-card';
import CircledIcon from 'src/components/ui/circled-icon';
import Container from 'src/components/ui/container';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import SectionContainer from 'src/components/ui/section-container';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import useModals from 'src/hooks/use-modals';
import useSteps from 'src/hooks/use-steps';
import { GroupedPlan } from 'src/screens/setup/pick-plan';
import colors from 'src/theme/colors';
import SelectPlanPreferenceModal, { UPFRONT_SUBSCRIPTION_AMOUNTS } from './select-plan-preference-modal';

interface Props extends Partial<BottomModalProps> {
  closeModal: () => void;
  plan: GroupedPlan;
  plans: Plan[];
  isSetup?: boolean;
  onComplete: VoidFunction;
}
const PickPlanModal: React.FC<Props> = ({ closeModal, onComplete, plan, plans, isSetup = true, ...props }) => {
  const [payUpfront, setPayUpfront] = useState(true);
  const { step, next, changeStep } = useSteps(['interval', 'confirm', 'error'], 0);
  const { modals, toggleModal } = useModals(['payment', 'planPreference']);
  const [selectedOption, setSelectedOption] = useState<PlanOption>(null);
  const [selectedPlan, setSelectedPlan] = useState<Plan>();
  const navigation = useNavigation();
  const createFreeSubRequest = useApi({
    apiFunction: CREATE_FREE_SUBSCRIPTION,
    key: CREATE_FREE_SUBSCRIPTION.name,
    method: 'POST',
  });

  const selectedPlanOption = useMemo(() => {
    return plans.find((p) => selectedPlan?.id === p.id && selectedOption?.id === p.plan_option_id);
  }, [selectedPlan, selectedOption]);

  // async function subscribe(plan: string, plan_option: string) {
  //   const [res, err] = await createFreeSubRequest.makeRequest({ plan, plan_option });
  //   if (res) {
  //     closeModal();
  //     await delay(1000);
  //     navigation.navigate('SetupComplete');
  //   }
  // }

  async function subscribe(plan: string, plan_option: string) {
    if (isSetup && !payUpfront) {
      const [res, err] = await createFreeSubRequest.makeRequest({ plan, plan_option });

      if (res) {
        closeModal();
        await delay(1000);
        navigation.navigate('SetupComplete');
      }

      return;
    }

    // if (!selectedPlan.is_paid_plan) {
    //   const [res, err] = await changePlanRequest.makeRequest({ plan });

    //   if (res) {
    //     setTimeout(() => {
    //       handleSuccess(plan);
    //     }, 1000);
    //   }

    //   return;
    // }

    //since we're only getting the plan id
    //search the actual list of plans for the plan - so the subscribe modal can use it.
    const actualPlan = plans.find(p => plan === p.id && plan_option === p.plan_option_id);
    
    if (actualPlan) {
      const updatedPlan = {
        ...actualPlan,
        amount:
          payUpfront && selectedPlanOption && isSetup
            ? toNaira(
                UPFRONT_SUBSCRIPTION_AMOUNTS[actualPlan.type][
                  (selectedPlanOption?.country as CountryInterface).currency
                ],
              )
            : actualPlan.amount,
      };
      setSelectedPlan(updatedPlan);
    }

    toggleModal('payment');
    return;
  }

  const handleButton = () => {
    switch (step) {
      case 'interval':
        setSelectedPlan(plans.find(p => p.id === plan.id && p.plan_option_id === selectedOption.id));
        if (isSetup) {
          toggleModal('planPreference');
          // next()
        } else toggleModal('payment');
        break;
      case 'confirm':
        subscribe(selectedPlan.id, selectedOption.id);
        break;
      case 'error':
        subscribe(selectedPlan.id, selectedOption.id);
        break;
      default:
        break;
    }
  };

  const handlePaymentSuccess = async (ref: string) => {
    onComplete();
  };

  const optionsArr = Object.values(plan.options);
  const stepBtnText = {
    confirm: 'Yes Subscribe',
    error: 'Retry',
  };
  return (
    <BottomModal
      closeModal={closeModal}
      enableDynamicSizing
      {...props}
      buttons={[
        ...(step === 'confirm'
          ? [
              {
                text: 'Back',
                onPress: () => {
                  changeStep('interval');
                },
                variant: ButtonVariant.LIGHT,
              },
            ]
          : []),
        {
          onPress: handleButton,
          isLoading: createFreeSubRequest.isLoading,
          text: createFreeSubRequest.isLoading ? 'Subscribing to Plan...' : (stepBtnText[step] ?? 'Continue'),
          disabled: (step === 'interval' ? !Boolean(selectedOption) : false) || createFreeSubRequest.isLoading,
        },
      ]}>
      <Container className="">
        {step === 'interval' && (
          <>
            <BaseText fontSize={16} className="font-fhOscarBold">
              {plan.name} Plan
            </BaseText>
            <SectionContainer className="">
              {optionsArr.map((p, i) => (
                <ListItemCard
                  key={i}
                  title={p.interval_text}
                  showBorder={optionsArr.length !== i + 1}
                  description={toCurrency(p.amount, plan?.country?.currency)}
                  titleAddon={
                    p.discount ? (
                      <StatusPill
                        statusType={StatusType.SUCCESS_INVERTED}
                        className="py-[3px]"
                        title={`${p.discount}% off`}
                      />
                    ) : null
                  }
                  titleProps={{
                    fontSize: 12,
                    type: 'body',
                    weight: 'light',
                    classes: 'text-black-muted',
                  }}
                  descriptionProps={{
                    fontSize: 14,
                    type: 'body',
                    weight: 'medium',
                    classes: 'text-black-main',
                  }}
                  rightElement={<Radio active={selectedOption?.id === p.id} />}
                  onPress={() => setSelectedOption(p)}
                  spreadTitleContainer={false}
                />
              ))}
            </SectionContainer>
          </>
        )}
        {step === 'confirm' && (
          <>
            <View className="items-center flex-col justify-center flex-1 mt-40">
              <CircledIcon className="w-[60px] h-[60px] bg-primary-main">
                <Tag2 size={30} color={colors.white} variant="Bold" />
              </CircledIcon>
              <BaseText fontSize={20} type="heading">
                Subscribe to {plan.name}
              </BaseText>
              <BaseText fontSize={20} type="heading">
                {selectedOption.interval_text} Plan
              </BaseText>
              <BaseText fontSize={14} className="text-black-placeholder mt-15 text-center" type="body">
                {/* {selectedOption.interval_text} */}
                You can try this plan free for <Text className="font-interSemiBold text-black-main">14 days</Text> after
                that you’ll be charged{' '}
                <Text className="font-interSemiBold text-black-main">
                  {toCurrency(selectedOption.amount, plan.country.currency)}
                </Text>{' '}
                every <Text className="font-interSemiBold text-black-main">{selectedOption.interval} days</Text>
              </BaseText>
            </View>
          </>
        )}

        {step === 'error' && (
          <>
            <View className="items-center flex-col justify-center flex-1 mt-40">
              <SuccessCheckmark />
              <BaseText fontSize={20} type="heading">
                Subscription Failed
              </BaseText>
              <BaseText fontSize={14} className="text-black-placeholder mt-15 text-center" type="body">
                Your subscription was not successful, please try again
              </BaseText>
            </View>
          </>
        )}
      </Container>
      {selectedPlanOption && selectedPlan && (
        <SelectPlanPreferenceModal
          selectedPlan={selectedPlan}
          closeModal={() => toggleModal('planPreference', false)}
          selectedPlanOption={selectedPlanOption as any}
          subscribeToPlan={subscribe}
          isVisible={modals.planPreference}
          payUpfront={payUpfront}
          setPayUpfront={setPayUpfront}
          isLoading={createFreeSubRequest?.isLoading}
          subscriptionRequest={createFreeSubRequest}
          currency={(selectedPlan?.country as CountryInterface)?.currency as CURRENCIES}
        />
      )}
      <PaymentsWidget
        data={{
          paymentType: PAYMENT_TYPES.SUBSCRIPTION,
          plan: {
            ...selectedPlan,
            currency: COUNTRY_CURRENCY_MAP[plan?.country?.code],
            country: plan?.country?.code,
          },
          upfrontSubscription: payUpfront && isSetup,
        }}
        onComplete={handlePaymentSuccess}
        show={modals.payment}
        toggle={() => toggleModal('payment', false)}
      />
    </BottomModal>
  );
};
export default PickPlanModal;

{
  /* {step === 'success' && (
          <>
            <View className="items-center flex-col justify-center flex-1 mt-40">
              <SuccessCheckmark />
              <BaseText fontSize={20} type="heading">
                Subscription Successful
              </BaseText>
              <BaseText fontSize={14} className="text-black-placeholder mt-15 text-center" type="body">
                You have successfully subscribed to the
                <Text className="font-interSemiBold text-black-main">
                  {' '}
                  {selectedPlan?.name} ({selectedOption?.interval_text}){' '}
                </Text>{' '}
                plan{' '}
              </BaseText>
            </View>
          </>
  )} */
}
