import { Linking, View } from 'react-native';
import colors from '@/theme/colors';
import { ArrowUpRight, CheckCircle, ChevronDown, ChevronUp } from '@/components/ui/icons';
import { capitalizeWords, copyToClipboard, normalizeEnums, toCurrency, wp } from '@/assets/utils/js';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import {
  Clock,
  Edit2,
  Link21,
  Location,
  MoneyForbidden,
  Moneys,
  MoneyTick,
  ReceiptSearch,
  ShoppingBag,
  Tag2,
  TruckFast,
} from 'iconsax-react-native/src';
import SectionContainer from '@/components/ui/section-container';
import ProductCard from '@/components/products/storefront-products/product-card';
import MoreOptions from '@/components/ui/more-options';
import Separator from '@/components/ui/others/separator';
import InfoRow from '@/components/ui/others/info-row';
import cx from 'classnames';
import Toast from 'react-native-toast-message';
import { useApi } from 'src/hooks/use-api';
import { EXPO_PUBLIC_PUBLIC_URL } from '@env';
import ListItemCard from '@/components/ui/cards/list-item-card';
import CustomImage from '@/components/ui/others/custom-image';
import Accordion from '@/components/ui/others/accordion';
import dayjs from 'dayjs';
import { ReactNode } from 'react';
import { toAppUrl, IDelivery } from 'catlog-shared';

const ProductDetailSection = ({ delivery, callBack }: { delivery: IDelivery; callBack?: VoidFunction }) => {
  const moreOptions = [
    {
      optionElement: (
        <Row>
          <CircledIcon iconBg="bg-grey-bgOne" className="p-7">
            <Edit2 size={wp(15)} color={colors.black.placeholder} />
          </CircledIcon>
          <BaseText fontSize={12} classes="text-black-main ml-10">
            Edit Order
          </BaseText>
        </Row>
      ),
      title: 'Edit Order',
      onPress: () => {},
    },
    {
      optionElement: (
        <Row>
          <CircledIcon iconBg="bg-grey-bgOne" className="p-7">
            <MoneyTick size={wp(15)} color={colors.black.placeholder} />
          </CircledIcon>
          <BaseText fontSize={12} classes="text-black-main ml-10">
            Mark as Paid
          </BaseText>
        </Row>
      ),
      title: 'Mark as Paid',
      onPress: () => {},
    },
    {
      optionElement: (
        <Row>
          <CircledIcon iconBg="bg-grey-bgOne" className="p-7">
            <Link21 size={wp(15)} color={colors.black.placeholder} />
          </CircledIcon>
          <BaseText fontSize={12} classes="text-black-main ml-10">
            Copy Order Link
          </BaseText>
        </Row>
      ),
      title: 'Copy Order Link',
      onPress: () => copyToClipboard(toAppUrl(`deliverys/${delivery.id}`, true, EXPO_PUBLIC_PUBLIC_URL)),
    },
  ];

  return (
    <View className="mx-20">
      <Accordion
        initiallyOpened
        anchorElement={status => (
          <Row className="flex flex-row">
            <BaseText type="heading" fontSize={15}>
              Product Details
            </BaseText>
            <CircledIcon className="bg-grey-bgOne p-7">
              {status ? (
                <ChevronUp size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
              ) : (
                <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
              )}
            </CircledIcon>
          </Row>
        )}>
        <SectionContainer className="pb-15">
          {delivery?.items?.map((item, index) => (
            <View key={index}>
              <ListItemCard
                leftElement={
                  <View className={cx('rounded-[10px] h-40 w-40 overflow-hidden justify-end')}>
                    <CustomImage className="w-full h-full" imageProps={{ source: { uri: item.image } }} />
                  </View>
                }
                title={item.name}
                titleProps={{ weight: 'semiBold', classes: 'text-black-muted' }}
                description={`${item.unit_weight}KG`}
                descriptionProps={{ weight: 'bold', classes: 'text-black-main' }}
                rightElement={
                  <View className="rounded-full py-8 px-10 bg-white">
                    <BaseText fontSize={10} weight="medium" classes="text-black-muted">
                      Quantity: {item.quantity}
                    </BaseText>
                  </View>
                }
              />
              {index !== delivery?.items?.length! - 1 ? (
                <View className=" h-[1px] bg-grey-border" />
              ) : (
                <View className="mb-[-15px]" />
              )}
            </View>
          ))}
          <Separator className="mx-0" />
          <InfoRow
            iconBg="white"
            title={'Est. Pick Up Time'}
            icon={<Clock size={wp(15)} color={colors.black.placeholder} />}
            value={dayjs(delivery?.pickup_date).format('ha  - ddd MM YYYY')}
          />
          <InfoRow
            iconBg="white"
            title={'Est. Drop-off Time'}
            icon={<Clock size={wp(15)} color={colors.black.placeholder} />}
            value={dayjs(delivery?.delivery_date).format('ha  - ddd MM YYYY')}
          />
          {delivery?.delivery_notes && (
            <DeliveryInfo
              icon={<Location size={wp(15)} color={colors.black.placeholder} />}
              title={'Delivery Note'}
              subTitle={delivery?.delivery_notes}
            />
          )}
        </SectionContainer>
      </Accordion>
    </View>
  );
};
export const DeliveryInfo = ({
  icon,
  title,
  subTitle,
  rightElement,
  first = false,
}: {
  icon: ReactNode;
  title: string;
  subTitle?: string;
  rightElement?: ReactNode;
  first?: boolean;
}) => {
  return (
    <Row className={cx(!first ? 'mt-15' : '', { 'items-start': subTitle !== undefined })}>
      <CircledIcon iconBg="bg-white" className={cx('p-6')}>
        {icon}
      </CircledIcon>
      <View className="flex-1 mx-15">
        <BaseText weight={'medium'} classes="text-black-secondary">
          {title}
        </BaseText>
        {subTitle && <BaseText classes="text-black-muted mt-3 leading-[20px]">{subTitle}</BaseText>}
      </View>
      {rightElement && rightElement}
    </Row>
  );
};

export default ProductDetailSection;
