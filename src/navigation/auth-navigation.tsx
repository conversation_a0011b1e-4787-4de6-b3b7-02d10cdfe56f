import React, { useCallback, useMemo, useState } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { Login, ResetPassword, Signup, EnterEmail, Onboarding } from '@/screens';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import SignupV2 from 'src/screens/auth/sign-up-v2';
import { useAsyncStorage } from '@react-native-async-storage/async-storage';

const Stack = createStackNavigator();

const UserAuth = () => {
  const [isOnboarded, setIsOnboarded] = useState(false);
  const isOnboarding = useAsyncStorage('isOnboarded');

  const isOnboardedValue = async () => {
    const value = await isOnboarding.getItem();
    const parsedValue = JSON.parse(value);
    setIsOnboarded(parsedValue);
  };

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}>
      {isOnboarded && <Stack.Screen name="Onboarding" component={Onboarding} />}
      <Stack.Screen name="Login" component={Login} />
      <Stack.Screen name="Signup" component={SignupV2} />
      <Stack.Screen name="EnterEmail" component={EnterEmail} />
      <Stack.Screen name="ResetPassword" component={ResetPassword} />
    </Stack.Navigator>
  );
};

export default UserAuth;
