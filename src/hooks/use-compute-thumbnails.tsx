
import { useMemo } from "react";
import { Product } from "src/components/products/create-products/types";

function useComputeThumbnails(product: Product) {
  const videoThumbnails = useMemo(() => {
    return product?.videos
      ? product?.videos
          ?.filter((v) => v?.meta?.thumbnail)
          .map((v, idx) => ({
            key: v.key,
            name: v?.name,
            url: v?.meta?.thumbnail?.url,
            src: v?.meta?.thumbnail?.src,
            isUploading: v?.meta?.thumbnail?.isUploading,
            uploadProgress: v?.meta?.thumbnail?.uploadProgress,
            error: v?.meta?.thumbnail?.error,
            meta: {
              id: v?.meta?.id,
              type: "video",
              index: idx,
            },
          }))
      : [];
  }, [product.videos]);

  const imageThumbnails = useMemo(() => {
    return product.images.map((i, idx) => ({
      key: i.key,
      name: i.name,
      src: i.src,
      url: i.url,
      isUploading: i.isUploading,
      uploadProgress: i.uploadProgress,
      error: i.error,
      meta: {
        id: i.meta?.id,
        type: "image",
        index: idx,
      },
    }));
  }, [product.images]);

  return { imageThumbnails, videoThumbnails };
}

export default useComputeThumbnails;
