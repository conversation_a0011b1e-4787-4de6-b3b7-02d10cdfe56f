import { ORDER_STATUSES, OrderInterface, GET_ORDERS, GetOrdersParams } from 'catlog-shared';
import { useEffect, useRef, useState } from 'react';
import { delay, ensureUniqueItems } from 'src/assets/utils/js';

import { ResponseWithPagination, useApi } from './use-api';
import usePagination from './use-pagination';

interface OrdersResponseWithPagination extends ResponseWithPagination<OrderInterface[]> {}
export interface OrdersResponse {
  data: OrdersResponseWithPagination;
}
const useOrdersApi = (filters: GetOrdersParams['filter'], perPage = 10) => {
  const [orders, setOrders] = useState<OrderInterface[]>([]);
  const previousFilterRef = useRef(null);

  const { currentPage, goNext, setPage } = usePagination();

  // const changeStatusRequest = useApi({ apiFunction: UPDATE_ORDER, method: 'PUT', key: 'update-order' });
  const getOrdersRequest = useApi<GetOrdersParams, OrdersResponse>(
    {
      apiFunction: GET_ORDERS,
      method: 'GET',
      key: 'get-orders',
      onSuccess: response => {
        setOrders(prev => ensureUniqueItems([...prev, ...response?.data?.data]));

        // do something on success
      },
    },
    {
      filter: filters,
      page: currentPage,
      per_page: perPage,
      sort: 'DESC',
    },
  );

  useEffect(() => {
    const stringifiedFilters = JSON.stringify(filters);

    const fn = async () => {
      if (previousFilterRef.current === stringifiedFilters) {
        return;
      }
      await delay(600);
      setOrders([]);
      setPage(1);
      getOrdersRequest.refetch();
      previousFilterRef.current = stringifiedFilters;
    };
    fn();
  }, [filters, previousFilterRef]);

  const handlePullToRefresh = () => {
    setOrders([]);

    if (currentPage === 1) {
      getOrdersRequest.makeRequest({
        filter: filters,
        page: currentPage,
        per_page: perPage,
        sort: 'DESC',
      });
      return;
    }

    setPage(1);
  };

  const handleOnEndReach = () => {
    if (
      !getOrdersRequest?.isLoading &&
      orders?.length > 0 &&
      currentPage < getOrdersRequest?.response?.data?.total_pages
    ) {
      goNext(getOrdersRequest?.response?.data?.total_pages);
    }
  };

  // const handleUpdateStatus = async () => {};

  return {
    getOrdersRequest,
    orders,
    goNext,
    setPage,
    handlePullToRefresh,
    handleOnEndReach,
    setOrders,
  };
};

export default useOrdersApi;
