import { useEffect, useMemo, useRef, useState } from 'react';
import colors from '@/theme/colors';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { cx, wp, Yup } from '@/assets/utils/js';
import { BaseText, CircledIcon, Container, Row, WhiteCardBtn } from '@/components/ui';
import { Add, ArrowLeft, ArrowRight, Money, MoneyChange, Note1 } from 'iconsax-react-native/src';
import useRouteParams from '@/hooks/use-route-params';
import useModals from 'src/hooks/use-modals';
import ScreenInfoHeader, { ColorPaletteType } from 'src/components/ui/screen-info-header';
import useSteps from 'src/hooks/use-steps';
import Pressable from 'src/components/ui/base/pressable';
import Input from 'src/components/ui/inputs/input';
import { ScrollView, View } from 'react-native';
import { useApi } from 'src/hooks/use-api';
import useAuthContext from 'src/contexts/auth/auth-context';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import { useNavigation } from '@react-navigation/native';
import { BULK_UPDATE_ITEMS, BulkUpdateFormItem, GET_ITEMS, GetItemsParams, getItemThumbnail, ProductItemInterface } from 'catlog-shared';
import UpdateQtyStep from 'src/components/orders/bulk-update/update-qty-step';
import { ProductsResponse } from 'src/screens/products/storefront';
import Toast from 'react-native-toast-message';

const BATCH_SIZE = 10;

const useBulkUpdate = (scrollRef: React.MutableRefObject<ScrollView>) => {
  const { store } = useAuthContext();
  const [form, setForm] = useState<{ [id: string]: BulkUpdateFormItem }>();
  const [batchData, setBatchData] = useState({ batchStart: 0, batchEnd: BATCH_SIZE });

  const getProductsRequest = useApi<GetItemsParams, ProductsResponse>(
    {
      apiFunction: GET_ITEMS,
      method: 'GET',
      key: 'fetch-custom-items',
    },
    {
      per_page: 9007199254740991,
      filter: { store: store.id },
    },
  );

  const bulkUpdateRequest = useApi({
    apiFunction: BULK_UPDATE_ITEMS,
    key: 'bulk-update',
    method: 'PUT',
  });

  const changeBatch = (dir: '+' | '-') => {
    const { batchEnd, batchStart } = batchData;
    if (
      dir === '+'
        ? batchEnd + BATCH_SIZE <= Math.ceil(selected?.length / BATCH_SIZE) * BATCH_SIZE
        : batchStart - BATCH_SIZE >= 0
    ) {
      setBatchData({
        batchEnd: dir === '+' ? batchEnd + BATCH_SIZE : batchEnd - BATCH_SIZE,
        batchStart: dir === '+' ? batchStart + BATCH_SIZE : batchStart - BATCH_SIZE,
      });
    }
    scrollRef.current?.scrollTo({ y: 0, x: 0, animated: true });
  };

  const handleItemsSelect = (itemsSelected: string[]) => {
    const form = getProductsRequest.response?.data.items
      ?.filter(i => itemsSelected.includes(i.id))
      .reduce((current, i) => {
        current[i.id] = {
          name: i.name,
          id: i.id,
          quantity: i.quantity,
          image: getItemThumbnail(i),
          is_always_available: i.is_always_available,
          variants: i.variants,
          price: i.price,
          old_price: i.price,
        };
        return current;
      }, {});
    setForm(form);
    // if (isQuantityUpdate) next();
    // toggleModal("select");
  };

  const handleFormUpdate = (id: string, data: BulkUpdateFormItem) => {
    const formCopy = { ...form };
    formCopy[id] = data;
    setForm(formCopy);
  };

  // useEffect(() => {

  // }, [form])

  const selected = useMemo(() => {
    if (form === undefined) {
      return [];
    }
    const formValues = Object.values(form);
    if (formValues.length > 0) {
      return formValues.sort(i => (i.variants?.options?.length > 0 ? 1 : -1));
    }

    return [];
  }, [form]);

  const totalBatches = Math.ceil(selected?.length / BATCH_SIZE);
  const currentBatch = Math.ceil(batchData.batchEnd / BATCH_SIZE);

  const navigation = useNavigation();

  const completeUpdate = async (externalForm?: BulkUpdateFormItem) => {
    const [res, error] = await bulkUpdateRequest.makeRequest({
      items: [
        ...Object.values(externalForm ?? form).map(({ is_always_available, id, variants, price, quantity }) => ({
          is_always_available,
          id,
          variants,
          price,
          old_price: price,
          quantity,
        })),
      ],
    });

    if (res) {
      Toast.show({ type: 'success', text1: 'Selected items updated successfully' });
      navigation.goBack();
      // next();
    }
  };

  return {
    getProductsRequest,
    bulkUpdateRequest,
    selected,
    completeUpdate,
    changeBatch,
    handleItemsSelect,
    handleFormUpdate,
    form,
    setForm,
    batchData,
    setBatchData,
    store,
    totalBatches,
    currentBatch,
  };
};

export default useBulkUpdate;
