import { useCallback, useEffect } from 'react';
import { Image } from '@/@types/utils';
import { uploadImage } from '@/assets/utils/js/upload-files';
import * as FileSystem from 'expo-file-system';

const useImageUploads = (
  images: Image[],
  setImages: (images: Image[] | ((currentImages: Image[]) => Image[])) => void,
) => {
  const startUpload = useCallback(
    async (image: Image) => {
      if (image.isUploading || image.url || image.error) return;

      setImages(prevImages =>
        prevImages.map(img =>
          img.key === image.key ? { ...img, isUploading: true, uploadProgress: 0, error: false } : img,
        ),
      );

      try {
        const url = await uploadImage<string>(image.src, 'file', progress => {
          setImages(prevImages =>
            prevImages.map(img => (img.key === image.key ? { ...img, uploadProgress: progress } : img)),
          );
        });
        setImages(prevImages =>
          prevImages.map(img =>
            img.key === image.key ? { ...img, isUploading: false, url, uploadProgress: 100 } : img,
          ),
        );
      } catch (e) {
        console.log(e)
        setImages(prevImages =>
          prevImages.map(img => (img.key === image.key ? { ...img, isUploading: false, error: true } : img)),
        );
      }
    },
    [setImages],
  );

  const retryUpload = useCallback(
    (image: Image) => {
      setImages(prevImages =>
        prevImages.map(img => (img.key === image.key ? { ...img, error: false, uploadProgress: 0 } : img)),
      );
      startUpload(image);
    },
    [setImages, startUpload],
  );

  useEffect(() => {
    images.forEach(image => {
      if (!image.isUploading && !image.url && !image.error) {
        startUpload(image);
      }
    });
  }, [images, startUpload]);

  return { retryUpload };
};

export default useImageUploads;
