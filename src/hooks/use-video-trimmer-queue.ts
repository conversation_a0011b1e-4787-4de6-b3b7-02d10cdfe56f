// types.ts
export enum ProcessingStep {
  TRIM_VIDEO = 'trim_video',
  UPLOAD_VIDEO = 'upload_video',
  UPDATE_VIDEO_URL = 'update_video_url',
  UPLOAD_THUMBNAIL = 'upload_thumbnail',
  UPDATE_THUMBNAIL_URL = 'update_thumbnail_url'
}

export interface QueueItem {
  type: MediaType;
  src: string;
  name: string;
  lastModified: number;
  file: File | Blob;
  isUploading?: boolean;
  uploadProgress?: number;
  url?: string;
  thumbnail?: string;
  error?: boolean;
  meta?: {
    id: string;
    thumbnail: {
      src: string;
      name: string;
      lastModified: number;
      file: string | null;
      isUploading: boolean;
      uploadProgress: number;
      url: string;
      error: boolean;
      key: string;
    };
  };
  key?: string;
  isPlaceholder?: boolean;
  
  // Queue-specific properties
  queueId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  currentStep: ProcessingStep;
  stepProgress: Record<ProcessingStep, number>;
  overallProgress: number;
  errorMessage?: string;
  retryCount: number;
  processedVideoUrl?: string;
  processedThumbnailUrl?: string;
  
  // Trimming parameters
  startTimeMs: number;
  endTimeMs: number;
  outputPath?: string;
}

// useVideoProcessingQueue.ts
import { useState, useEffect, useCallback, useRef } from 'react';
import { MediaType } from 'catlog-shared';
import { uploadImage } from 'src/assets/utils/js/upload-files';
import { useVideoTrimmer } from './use-video-trimmer';

// Progress weights for each step (should sum to 100)
const STEP_WEIGHTS = {
  [ProcessingStep.TRIM_VIDEO]: 30,
  [ProcessingStep.UPLOAD_VIDEO]: 40,
  [ProcessingStep.UPDATE_VIDEO_URL]: 5,
  [ProcessingStep.UPLOAD_THUMBNAIL]: 20,
  [ProcessingStep.UPDATE_THUMBNAIL_URL]: 5,
};

const PROCESSING_STEPS = [
  ProcessingStep.TRIM_VIDEO,
  ProcessingStep.UPLOAD_VIDEO,
  ProcessingStep.UPDATE_VIDEO_URL,
  ProcessingStep.UPLOAD_THUMBNAIL,
  ProcessingStep.UPDATE_THUMBNAIL_URL,
];

function calculateOverallProgress(stepProgress: Record<ProcessingStep, number>): number {
  let totalProgress = 0;
  
  for (const [step, progress] of Object.entries(stepProgress)) {
    const weight = STEP_WEIGHTS[step as ProcessingStep];
    totalProgress += (progress / 100) * weight;
  }
  
  return Math.round(totalProgress);
}

function createQueueItem(item: Omit<QueueItem, 'queueId' | 'status' | 'currentStep' | 'stepProgress' | 'overallProgress' | 'retryCount'>): QueueItem {
  return {
    ...item,
    queueId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    status: 'pending',
    currentStep: ProcessingStep.TRIM_VIDEO,
    stepProgress: PROCESSING_STEPS.reduce((acc, step) => ({
      ...acc,
      [step]: 0,
    }), {} as Record<ProcessingStep, number>),
    overallProgress: 0,
    retryCount: 0,
    outputPath: item.outputPath || null,
  };
}

export const useVideoProcessingQueue = () => {
  const [queue, setQueue] = useState<QueueItem[]>([]);
  const [completedItems, setCompletedItems] = useState<QueueItem[]>([]);
  const [failedItems, setFailedItems] = useState<QueueItem[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentItem, setCurrentItem] = useState<QueueItem | null>(null);
  const [isPaused, setIsPaused] = useState(false);
  
  const { trimVideo } = useVideoTrimmer();
  const processingRef = useRef(false);
  
  // Update video function - you'll need to implement this based on your state management
  const updateVideo = useCallback((videoId: string, updates: Partial<QueueItem>) => {
    // This should update your main video state/store
    // Example: setVideos(prev => prev.map(v => v.id === videoId ? {...v, ...updates} : v))
    console.log(`Updating video ${videoId}:`, updates);
  }, []);
  
  const updateProgress = useCallback((queueId: string, step: ProcessingStep, progress: number) => {
    setQueue(prevQueue => 
      prevQueue.map(item => {
        if (item.queueId === queueId) {
          const newStepProgress = {
            ...item.stepProgress,
            [step]: progress,
          };
          const updatedItem = {
            ...item,
            stepProgress: newStepProgress,
            overallProgress: calculateOverallProgress(newStepProgress),
          };
          
          // Update current item if it's the one being processed
          if (currentItem?.queueId === queueId) {
            setCurrentItem(updatedItem);
          }
          
          return updatedItem;
        }
        return item;
      })
    );
  }, [currentItem]);
  
  const processQueueItem = useCallback(async (item: QueueItem): Promise<void> => {
    const { queueId } = item;
    let trimmedVideo: any = null;
    let videoUrl: string | null = null;
    let thumbnailUrl: string | null = null;
    
    try {
      // Step 1: Trim Video (30%)
      updateProgress(queueId, ProcessingStep.TRIM_VIDEO, 0);
      
      trimmedVideo = await trimVideo(item.src, item.startTimeMs, item.endTimeMs, item.outputPath);
      
      updateProgress(queueId, ProcessingStep.TRIM_VIDEO, 100);
      
      // Step 2: Upload Video (40%)
      updateProgress(queueId, ProcessingStep.UPLOAD_VIDEO, 0);
      
      videoUrl = await uploadImage<string>(trimmedVideo, 'file', (progress: number) => {
        updateProgress(queueId, ProcessingStep.UPLOAD_VIDEO, progress);
      });
      
      updateProgress(queueId, ProcessingStep.UPLOAD_VIDEO, 100);
      
      // Step 3: Update Video URL locally (5%)
      updateProgress(queueId, ProcessingStep.UPDATE_VIDEO_URL, 0);
      
      // Update local state with new video URL
      updateVideo(item.meta?.id || item.key || queueId, {
        url: videoUrl,
        processedVideoUrl: videoUrl,
      });
      
      // Simulate brief processing time
      await new Promise(resolve => setTimeout(resolve, 200));
      updateProgress(queueId, ProcessingStep.UPDATE_VIDEO_URL, 100);
      
      // Step 4: Upload Thumbnail (20%)
      updateProgress(queueId, ProcessingStep.UPLOAD_THUMBNAIL, 0);
      
      const thumbnailFile = item.meta?.thumbnail?.file;
      if (thumbnailFile) {
        thumbnailUrl = await uploadImage<string>(thumbnailFile, 'file', (progress: number) => {
          updateProgress(queueId, ProcessingStep.UPLOAD_THUMBNAIL, progress);
        });
      } else {
        // If no thumbnail file, skip with 100% progress
        thumbnailUrl = item.thumbnail || '';
      }
      
      updateProgress(queueId, ProcessingStep.UPLOAD_THUMBNAIL, 100);
      
      // Step 5: Update Thumbnail URL locally (5%)
      updateProgress(queueId, ProcessingStep.UPDATE_THUMBNAIL_URL, 0);
      
      // Update local state with new thumbnail URL
      updateVideo(item.meta?.id || item.key || queueId, {
        thumbnail: thumbnailUrl,
        processedThumbnailUrl: thumbnailUrl,
      });
      
      // Simulate brief processing time
      await new Promise(resolve => setTimeout(resolve, 200));
      updateProgress(queueId, ProcessingStep.UPDATE_THUMBNAIL_URL, 100);
      
      // Mark as completed
      setQueue(prevQueue => prevQueue.filter(q => q.queueId !== queueId));
      setCompletedItems(prev => [...prev, {
        ...item,
        status: 'completed',
        overallProgress: 100,
        processedVideoUrl: videoUrl,
        processedThumbnailUrl: thumbnailUrl,
      }]);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      // Move to failed items
      setQueue(prevQueue => prevQueue.filter(q => q.queueId !== queueId));
      setFailedItems(prev => [...prev, {
        ...item,
        status: 'failed',
        errorMessage,
      }]);
      
      throw error;
    }
  }, [trimVideo, updateProgress, updateVideo]);
  
  const processNextItem = useCallback(async () => {
    if (processingRef.current || isPaused) return;
    
    const nextItem = queue.find(item => item.status === 'pending');
    if (!nextItem) {
      setIsProcessing(false);
      setCurrentItem(null);
      return;
    }
    
    processingRef.current = true;
    setIsProcessing(true);
    setCurrentItem(nextItem);
    
    // Update item status to processing
    setQueue(prevQueue =>
      prevQueue.map(item =>
        item.queueId === nextItem.queueId
          ? { ...item, status: 'processing' }
          : item
      )
    );
    
    try {
      await processQueueItem(nextItem);
    } catch (error) {
      console.error('Failed to process queue item:', error);
    } finally {
      processingRef.current = false;
      setIsProcessing(false);
      setCurrentItem(null);
      
      // Process next item after a brief delay
      setTimeout(() => {
        if (!isPaused) {
          processNextItem();
        }
      }, 500);
    }
  }, [queue, isPaused, processQueueItem]);
  
  // Auto-process queue when items are added
  useEffect(() => {
    if (!isProcessing && !isPaused && queue.some(item => item.status === 'pending')) {
      processNextItem();
    }
  }, [queue, isProcessing, isPaused, processNextItem]);
  
  const addToQueue = useCallback((item: Omit<QueueItem, 'queueId' | 'status' | 'currentStep' | 'stepProgress' | 'overallProgress' | 'retryCount'>) => {
    const queueItem = createQueueItem(item);
    setQueue(prevQueue => [...prevQueue, queueItem]);
  }, []);
  
  const removeFromQueue = useCallback((queueId: string) => {
    setQueue(prevQueue => prevQueue.filter(item => item.queueId !== queueId));
  }, []);
  
  const retryItem = useCallback((queueId: string) => {
    const failedItem = failedItems.find(item => item.queueId === queueId);
    if (failedItem) {
      const retryItem = createQueueItem({
        ...failedItem,
        retryCount: failedItem.retryCount + 1,
      });
      
      setFailedItems(prev => prev.filter(item => item.queueId !== queueId));
      setQueue(prevQueue => [...prevQueue, retryItem]);
    }
  }, [failedItems]);
  
  const clearCompleted = useCallback(() => {
    setCompletedItems([]);
  }, []);
  
  const clearFailed = useCallback(() => {
    setFailedItems([]);
  }, []);
  
  const pauseQueue = useCallback(() => {
    setIsPaused(true);
  }, []);
  
  const resumeQueue = useCallback(() => {
    setIsPaused(false);
  }, []);
  
  const clearAll = useCallback(() => {
    setQueue([]);
    setCompletedItems([]);
    setFailedItems([]);
    setCurrentItem(null);
    setIsProcessing(false);
  }, []);
  
  // Get current step name for display
  const getCurrentStepName = useCallback((step: ProcessingStep): string => {
    const stepNames = {
      [ProcessingStep.TRIM_VIDEO]: 'Trimming Video',
      [ProcessingStep.UPLOAD_VIDEO]: 'Uploading Video', 
      [ProcessingStep.UPDATE_VIDEO_URL]: 'Updating Video',
      [ProcessingStep.UPLOAD_THUMBNAIL]: 'Uploading Thumbnail',
      [ProcessingStep.UPDATE_THUMBNAIL_URL]: 'Updating Thumbnail',
    };
    return stepNames[step] || step;
  }, []);
  
  return {
    // State
    queue,
    completedItems,
    failedItems,
    isProcessing,
    currentItem,
    isPaused,
    
    // Actions
    addToQueue,
    removeFromQueue,
    retryItem,
    clearCompleted,
    clearFailed,
    pauseQueue,
    resumeQueue,
    clearAll,
    
    // Utilities
    getCurrentStepName,
    
    // Computed values
    totalItems: queue.length + completedItems.length + failedItems.length,
    pendingCount: queue.filter(item => item.status === 'pending').length,
    processingCount: queue.filter(item => item.status === 'processing').length,
    completedCount: completedItems.length,
    failedCount: failedItems.length,
  };
};