import { useQuery, useMutation, useQueryClient, DefinedUseQueryResult, UseMutationResult } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { ApiError, client } from '@/assets/utils/js/api';
import useAuthStore from 'src/contexts/auth/store';
import { showError } from '@/assets/utils/js/functions';
import { useNetwork } from 'src/contexts/network-context';

type Methods = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
type ApiFunction<T> = (params: T) => { endpoint: string; data?: any; method: string };
type Configs<T> = {
  config?: any;
  baseUrl?: string;
  autoRequest?: boolean;
  params?: T;
  onSuccess?: (response: any) => void;
};

export interface ApiData<T, R> {
  response: R;
  error?: ApiError; // Update error type
  isLoading: boolean;
  makeRequest: (params: T) => Promise<[R, ApiError | null]>;
  isReLoading: boolean;
  reset: () => void;
  refetch?: VoidFunction;
}

interface UseApiProps<T, X> {
  key: string;
  apiFunction: ApiFunction<T>;
  method: Methods;
  config?: any;
  baseUrl?: string;
  autoRequest?: boolean;
  params?: T;
  onSuccess?: (response: X) => void;
  onError?: (error: any) => void;
  showErrorToast?: boolean;
  debounceTime?: number;
}

export interface ResponseWithPagination<T> {
  data: T;
  total_pages: number;
  next_page: number | null;
  sort?: string;
  total: number;
  page: number;
  prev_page?: number | null;
  per_page: number;
}

export interface ResponseWithoutPagination<T> {
  data: T;
  message: string;
}

function delayAsyncFunction(asyncFunction: () => any, delay: number) {
  return new Promise((resolve, reject) => {
    setTimeout(async () => {
      try {
        const result = await asyncFunction();
        resolve(result);
      } catch (error) {
        reject(error);
      }
    }, delay);
  });
}

export function useApi<T, X = any>(props: UseApiProps<T, X>, newParams: T = null): ApiData<T, X> {
  const {
    key,
    apiFunction,
    method,
    config = {},
    baseUrl,
    autoRequest = true,
    params = null,
    onSuccess,
    onError,
    showErrorToast = true,
    debounceTime = 0,
  } = props;
  const { clearUserData } = useAuthStore();
  const queryClient = useQueryClient();
  const isGetRequest = method === 'GET';
  const stringifiedParams = JSON.stringify(newParams ?? {});
  const [queryParams, setQueryParams] = useState<T | null>(newParams ?? null);

  useEffect(() => {
    if (debounceTime < 1) {
      setQueryParams(JSON.parse(stringifiedParams));
    } else {
      const handler = setTimeout(() => {
        setQueryParams(JSON.parse(stringifiedParams));
      }, debounceTime);

      return () => {
        clearTimeout(handler);
      };
    }
  }, [stringifiedParams]);

  // Get network context if available
  const network = useNetwork();

  const fetchData = async () => {
    // Check network connectivity using the context
    if (network) {
      const isConnected = await network.checkNetworkStatus();
      if (!isConnected) {
        const networkError = new Error('No internet connection. Please check your network settings and try again.');
        throw networkError;
      }
    }

    const { endpoint, data } = apiFunction(queryParams as T);

    try {
      const response = await client(endpoint, method, { ...config, data }, baseUrl, clearUserData);

      if (onSuccess) onSuccess(response);
      return response;
    } catch (e) {
      throw e;
    }
  };

  const handleError = (error: any) => {
    // Show error toast if enabled
    if (showErrorToast) {
      showError(error?.message ?? error);
    }

    // Call custom error handler if provided
    if (onError) {
      onError(error);
    }
  };

  const query: DefinedUseQueryResult<unknown, Error> | null = isGetRequest
    ? useQuery({
        queryKey: [key, JSON.stringify(queryParams)],
        queryFn: fetchData,
        enabled: autoRequest,
        refetchOnWindowFocus: false,
        ...config,
        onError: handleError,
      })
    : null;

  const makeQuery = async (params: T | null): Promise<[any, any]> => {
    setQueryParams(params);
    // console.log({ params });
    try {
      const result: any = await delayAsyncFunction(() => query?.refetch(), 1000); //hack to force delay so state is updated in useQuery, if this isn't done fetchData would be called with old data which could be null

      if (result?.error) {
        return [null, result.error];
      } else {
        return [result?.data, null];
      }
    } catch (err) {
      return [null, err];
    }
  };

  const mutation: UseMutationResult<unknown, Error, void, unknown> | null = !isGetRequest
    ? useMutation({
        mutationKey: [key],
        mutationFn: async (newParams: T) => {
          // Check network connectivity using the context
          if (network) {
            const isConnected = await network.checkNetworkStatus();
            if (!isConnected) {
              const networkError = new Error('No internet connection. Please check your network settings and try again.');
              throw networkError;
            }
          }

          const { endpoint, data } = apiFunction(newParams);
          // console.log(`CALLED ${method}:`, endpoint, data);
          return client(endpoint, method, { ...config, data }, baseUrl, clearUserData);
        },
        ...config,
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: [key] });
        },
        onError: handleError,
      })
    : null;

  const makeRequest = async (newParams: T): Promise<[any, ApiError | null]> => {
    setQueryParams(newParams);

    try {
      const data = await (mutation as any).mutateAsync(newParams as any);
      return [data, null];
    } catch (err) {
      return [null, err as ApiError];
    }
  };

  return {
    response: (isGetRequest ? query?.data : mutation?.data) as X,
    error: isGetRequest ? query?.error : mutation?.error,
    isLoading: (isGetRequest ? query?.isFetching || query?.isRefetching : mutation?.isPending)!,
    makeRequest: isGetRequest ? makeQuery : makeRequest,
    isReLoading: (isGetRequest ? query?.isRefetching : false)!,
    reset: () => {
      const queryKey = isGetRequest ? [key, JSON.stringify(queryParams)] : [key];

      if (!isGetRequest && mutation) {
        mutation.reset();
      }
      queryClient.clear()
      queryClient.resetQueries({ queryKey });
    },
    refetch: () => query?.refetch(),
  };
}
