import { useState } from 'react';

export interface ProgressStep {
  key: string;
  label: string;
  progress?: number;
  isLoading?: boolean;
  complete?: boolean;
  error?: string;
}
const useProgress = (steps: ProgressStep[]) => {
  const [progressSteps, setSteps] = useState(steps);
  const [currentStep, setStep] = useState(0);

  const setStepComplete = () => {
    const stepCopy = [...progressSteps];
    stepCopy[currentStep].complete = true;
    stepCopy[currentStep].isLoading = false;
    delete stepCopy[currentStep].progress;
    delete stepCopy[currentStep].error;

    setSteps(stepCopy);
  };

  const setStepIsLoading = () => {
    const stepCopy = [...progressSteps];
    stepCopy[currentStep].isLoading = true;
    stepCopy[currentStep].complete = false;
    delete stepCopy[currentStep].progress;
    delete stepCopy[currentStep].error;

    setSteps(stepCopy);
  };

  const setStepProgress = (progress: number) => {
    const stepCopy = [...progressSteps];
    delete stepCopy[currentStep].isLoading;
    delete stepCopy[currentStep].complete;
    delete stepCopy[currentStep].error;
    if (progress <= 100) stepCopy[currentStep].progress = progress;
    setSteps(stepCopy);
  };

  const nextStep = () => {
    const stepCopy = [...progressSteps];
    if (stepCopy[currentStep].progress !== undefined) {
      stepCopy[currentStep].progress = 100;
    } else {
      stepCopy[currentStep].isLoading = false;
      stepCopy[currentStep].complete = true;
    }

    const stepIndex = currentStep + 1;
    if (stepIndex < progressSteps?.length) {
      setSteps(stepCopy);
      setStep(stepIndex);
    }
  };

  const setError = (error: string) => {
    const stepCopy = [...progressSteps];
    stepCopy[currentStep].error = error;
    setSteps(stepCopy);
  };

  const reset = () => {
    setSteps(steps);
    setStep(0);
  };

  return {
    currentStep,
    setStepComplete,
    setStepIsLoading,
    setStepProgress,
    steps: progressSteps,
    nextStep,
    setError,
    currentKey: steps[currentStep].key,
    reset,
  };
};

export default useProgress;
