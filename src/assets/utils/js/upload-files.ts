import { Image } from '@/@types/utils';
import { EXPO_PUBLIC_API_URL } from '@env';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const uploadImage = <T>(
  // data: { file: string; name: string },
  // blob: Blob,
  uri: string,
  endpoint: string,
  onProgress: (progress: number) => void,
  fieldName: string = 'file',
  customFormDataFields?: { [key: string]: string },
  returnAllData?: boolean,
): Promise<T> => {
  const getToken = async () => {
    const authData = JSON.parse((await AsyncStorage.getItem('auth-storage')) ?? '{}');
    const token = authData?.state?.token as string;

    return token;
  };

  return new Promise(async (resolve, reject) => {
    const xhr = new XMLHttpRequest();
    const uploadUrl = `${process.env.EXPO_PUBLIC_API_URL}/${endpoint}`;

    const formData = new FormData();

    if (uri) {
      const file = {
        uri: uri, // CameralRoll Url
        type: 'image/png',
        name: 'photo.png',
      };
      formData.append(fieldName, file as any);
    }

    if (customFormDataFields) {
      for (const [key, value] of Object.entries(customFormDataFields)) {
        formData.append(key, value);
      }
    }

    if (xhr.upload) {
      xhr.upload.onprogress = event => {
        if (event.lengthComputable) {
          const progress = (event.loaded / event.total) * 100;
          onProgress(progress);
        }
      };
    }

    xhr.onload = (e) => {
      if (xhr.status === 200 || xhr.status === 201) {
        const response = JSON.parse(xhr.responseText);
        if (returnAllData) {
          resolve(response);
          return;
        }
        resolve(response.data.link);
      } else {
        reject(new Error('Upload failed'));
      }
    };

    xhr.onerror = e => {
      reject(new Error('Upload failed'));
    };

    const token = await getToken();
    xhr.open('POST', uploadUrl, true);
    xhr.setRequestHeader('Authorization', `Bearer ${token}`);
    xhr.send(formData);
  });
};
