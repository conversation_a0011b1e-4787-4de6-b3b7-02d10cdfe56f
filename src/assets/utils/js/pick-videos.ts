import { Image as ImageType, IMedia } from 'src/@types/utils';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';
import * as VideoThumbnails from 'expo-video-thumbnails';
import { Media, MediaType } from 'catlog-shared';

const generateVideoThumbnail = async (uri: string): Promise<string> => {
  try {
    // Generate a thumbnail from the video
    const { uri: thumbnailUri } = await VideoThumbnails.getThumbnailAsync(
      uri,
      {
        time: 0, // Get thumbnail from the first frame
        quality: 0.7, // Medium quality is sufficient for thumbnails
      }
    );
    
    return thumbnailUri;
  } catch (error) {
    console.error('Error generating video thumbnail:', error);
    // Return the original video URI as fallback if thumbnail generation fails
    return uri;
  }
};

export const pickVideos = async (
  existingVideos: IMedia[] = [],
  isSingle = false,
  extraOptions?: ImagePicker.ImagePickerOptions,
  setInfoMessage?: (message: string) => void,
): Promise<IMedia[]> => {
  const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
  if (!permissionResult.granted) {
    Alert.alert('Permission Required', 'To upload videos, please allow Catlog access to your media library');
    return existingVideos;
  }

  let result = await ImagePicker.launchImageLibraryAsync({
    mediaTypes: ImagePicker.MediaTypeOptions.Videos,
    allowsEditing: false,
    quality: 1,
    allowsMultipleSelection: !isSingle,
    // videoMaxDuration: 60, // 60 seconds max duration
    ...extraOptions,
  });

  if (result.canceled) {
    return existingVideos;
  }
  
  setInfoMessage?.('Processing selected videos...');
  const newVideos = [];
  
  for (const video of result.assets) {
    // Check file size (15MB limit)
    const fileInfo = await FileSystem.getInfoAsync(video.uri, { size: true });
    console.log(fileInfo)
    const fileSizeInMB = fileInfo.size ? fileInfo.size / (1024 * 1024) : 0;
    console.log({fileSizeInMB})
    
    if (fileSizeInMB > 100) {
      Alert.alert('File Too Large', 'Video file exceeds the 15MB limit. Please select a smaller video.');
      continue;
    }
    
    // Generate thumbnail from the video
    setInfoMessage?.('Generating thumbnail...');
    const thumbnail = await generateVideoThumbnail(video.uri);
    
    newVideos.push({ 
      ...video, 
      uri: video.uri,
      thumbnail: thumbnail,
      duration: video.duration || 0,
      type: MediaType.VIDEO
    });
  }
  
  setInfoMessage?.('Finishing up...');

  const parsedVideos = newVideos.map(video => {
    const uriParts = video.uri.split('/');
    const name = uriParts[uriParts.length - 1];
    const currentTime = new Date().getTime();

    return {
      file: null,
      url: '',
      name: video.fileName ?? name,
      src: video.uri,
      key: video.assetId ?? currentTime.toString(),
      isUploading: false,
      uploadProgress: 0,
      error: false,
      newFile: true,
      thumbnail: video.thumbnail,
      duration: video.duration,
      type: MediaType.VIDEO,
      lastModified: null,
    };
  });

  setInfoMessage?.('');

  // Return the combined array of existing and new videos
  return [...(isSingle ? [] : existingVideos), ...parsedVideos];
};
