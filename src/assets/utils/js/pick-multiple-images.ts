import { Image as ImageType } from 'src/@types/utils';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';
import CryptoJS from 'crypto-js';

const getImageHash = async (uri: string) => {
  const fileContentBase64 = await FileSystem.readAsStringAsync(uri, {
    encoding: FileSystem.EncodingType.Base64,
  });

  // Generate a 32-character MD5 hash from the base64 content
  const hash = CryptoJS.MD5(fileContentBase64).toString(CryptoJS.enc.Hex);

  return hash;
};

export const pickMultipleImages = async (
  images: ImageType[],
  setImages: (images: ImageType[]) => void,
  isSingle = false,
  extraOptions?: ImagePicker.ImagePickerOptions,
  setInfoMessage?: (message: string) => void,
) => {
  const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
  setInfoMessage?.('Picking images...');
  if (!permissionResult.granted) {
    alert('To upload products, please allow Catlog access your photos');
    return;
  }

  let result = await ImagePicker.launchImageLibraryAsync({
    mediaTypes: ImagePicker.MediaTypeOptions.Images,
    allowsEditing: false,
    aspect: [1, 1],
    allowsMultipleSelection: !isSingle,
    ...extraOptions,
  });

  if (!result.canceled) {
    setInfoMessage?.('Compressing Selected images...');
    const newImages = [];
    for (const image of result.assets) {
      const hash = await getImageHash(image.uri);
      if (images.find(existingImage => existingImage.imageHash === hash)) {
        Alert.alert('Duplicate Image', 'This image has already been selected.');
      } else {
        const resizedImage = await ImageManipulator.manipulateAsync(image.uri, [{ resize: { width: 1000 } }], {
          compress: 0.5,
          format: ImageManipulator.SaveFormat.JPEG,
        });
        newImages.push({ ...image, imageHash: hash, uri: resizedImage.uri });
      }
    }
    setInfoMessage?.('Finishing up...');

    const parsedImages = newImages.map(image => {
      const uriParts = image.uri.split('/');
      const name = uriParts[uriParts.length - 1];
      const currentTime = new Date().getTime();

      return {
        file: null,
        url: '',
        name: image.fileName ?? name,
        src: image.uri,
        key: image.assetId ?? currentTime.toString(),
        isUploading: false,
        uploadProgress: 0,
        error: false,
        imageHash: image.imageHash,
      };
    });

    // Update the state with the new unique images
    setImages([...(isSingle ? [] : images), ...parsedImages]);
  }
};
