import { AppMediaType, Image as ImageType } from 'src/@types/utils';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';
import CryptoJS from 'crypto-js';
import { Media, MediaType } from 'catlog-shared';

// Enhanced type to support both images and videos
// export interface MediaT extends Omit<Media, 'imageHash'> {
//   imageHash?: string; // Optional for backward compatibility
//   mediaHash?: string; // New hash property for all media types
//   mediaType?: MediaType; // Type indicator
//   duration?: number; // Video duration in seconds
//   fileSize?: number; // File size in bytes
// }

const getMediaHash = async (uri: string) => {
  const fileContentBase64 = await FileSystem.readAsStringAsync(uri, {
    encoding: FileSystem.EncodingType.Base64,
  });

  // Generate a 32-character MD5 hash from the base64 content
  const hash = CryptoJS.MD5(fileContentBase64).toString(CryptoJS.enc.Hex);

  return hash;
};

const getFileSize = async (uri: string): Promise<number> => {
  try {
    const fileInfo = await FileSystem.getInfoAsync(uri);
    return fileInfo.exists ? fileInfo.size || 0 : 0;
  } catch (error) {
    console.warn('Error getting file size:', error);
    return 0;
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const pickMultipleImages = async (
  images: AppMediaType[],
  setImages: (images: AppMediaType[]) => void,
  isSingle = false,
  extraOptions?: ImagePicker.ImagePickerOptions,
  setInfoMessage?: (message: string) => void,
) => {
  const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
  
  if (!permissionResult.granted) {
    alert('To upload content, please allow Catlog access to your photos and videos');
    return;
  }

  let result = await ImagePicker.launchImageLibraryAsync({
    mediaTypes: ImagePicker.MediaTypeOptions.Images,
    allowsEditing: false,
    aspect: [1, 1],
    allowsMultipleSelection: !isSingle,
    ...extraOptions,
  });

  if (!result.canceled) {
    const maxSizeBytes = 100 * 1024 * 1024; // 100MB
    const isVideoMode = extraOptions?.mediaTypes === ImagePicker.MediaTypeOptions.Videos || 
                       extraOptions?.mediaTypes === ImagePicker.MediaTypeOptions.All;
    
    setInfoMessage?.(isVideoMode ? 'Processing selected media...' : 'Compressing Selected images...');
    
    const newMedia = [];
    
    for (const asset of result.assets) {
      const fileSize = await getFileSize(asset.uri);
      
      // Check file size limit
      if (fileSize > maxSizeBytes) {
        Alert.alert(
          'File Too Large', 
          `The selected ${asset.type === 'video' ? 'video' : 'image'} (${formatFileSize(fileSize)}) exceeds the 100MB limit. Please select a smaller file.`
        );
        continue;
      }

      // Generate hash for duplicate detection
      const mediaHash = await getMediaHash(asset.uri);
      const imageHash = mediaHash;
      
      // Check for duplicates using both hash properties for compatibility
      const isDuplicate = images.find(existingMedia => {
        const existing = existingMedia as AppMediaType;
        return existing.mediaHash === mediaHash || 
               existing.imageHash === mediaHash ||
               existing.imageHash === imageHash;
      });
      
      if (isDuplicate) {
        Alert.alert('Duplicate Media', `This ${asset.type === 'video' ? 'video' : 'image'} has already been selected.`);
        continue;
      }

      let processedAsset = { ...asset };
      
      // Only compress images, skip compression for videos
      if (asset.type === 'image') {
        const resizedImage = await ImageManipulator.manipulateAsync(
          asset.uri, 
          [{ resize: { width: 1000 } }], 
          {
            compress: 0.5,
            format: ImageManipulator.SaveFormat.JPEG,
          }
        );
        processedAsset = { ...asset, uri: resizedImage.uri };
      }
      
      // Add processed media with enhanced properties
      newMedia.push({
        ...processedAsset,
        imageHash, // Keep for backward compatibility
        mediaHash,
        type: asset.type === MediaType.VIDEO ? MediaType.VIDEO : MediaType.IMAGE,
        duration: asset.duration,
        fileSize,
      });
    }
    
    setInfoMessage?.('Finishing up...');

    const parsedMedia = newMedia.map(media => {
      const uriParts = media.uri.split('/');
      const name = uriParts[uriParts.length - 1];
      const currentTime = new Date().getTime();

      return {
        file: null,
        url: '',
        name: media.fileName ?? name,
        src: media.uri,
        key: media.assetId ?? currentTime.toString(),
        isUploading: false,
        uploadProgress: 0,
        lastModified: 0,
        error: false,
        imageHash: media.imageHash,
        mediaHash: media.mediaHash,
        type: media.type,
        duration: media.duration,
        fileSize: media.fileSize,
        // newFile: true,
      };
    });

    setInfoMessage?.(null);

    // Update the state with the new unique media
    setImages([...(isSingle ? [] : images), ...parsedMedia]);
  }
};

// Convenience function specifically for picking videos
export const pickMultipleVideos = async (
  videos: AppMediaType[],
  setVideos: (videos: AppMediaType[]) => void,
  isSingle = false,
  extraOptions?: Omit<ImagePicker.ImagePickerOptions, 'mediaTypes'>,
  setInfoMessage?: (message: string) => void,
) => {
  return pickMultipleImages(
    videos,
    setVideos,
    isSingle,
    {
      ...extraOptions,
      mediaTypes: ImagePicker.MediaTypeOptions.Videos,
      allowsEditing: false, // Disable editing for videos
    },
    setInfoMessage
  );
};

// Convenience function for picking both images and videos
export const pickMultipleMedia = async (
  media: AppMediaType[],
  setMedia: (media: AppMediaType[]) => void,
  isSingle = false,
  extraOptions?: Omit<ImagePicker.ImagePickerOptions, 'mediaTypes'>,
  setInfoMessage?: (message: string) => void,
) => {
  return pickMultipleImages(
    media,
    setMedia,
    isSingle,
    {
      ...extraOptions,
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      allowsEditing: false, // Disable editing when mixing media types
    },
    setInfoMessage
  );
};