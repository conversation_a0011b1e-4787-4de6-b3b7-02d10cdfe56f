import { Alert, Dimensions } from 'react-native';
import classNames from 'classnames';
import { EXPO_PUBLIC_PUBLIC_URL, EXPO_PUBLIC_CHOWBOT_URL } from '@env';
import { format } from 'date-fns';
import { FormikProps } from 'formik';
import colors from '@/theme/colors';
import * as Clipboard from 'expo-clipboard';
import Toast from 'react-native-toast-message';
import { BaseTransaction, GroupedTransactions, WithKey } from 'src/@types/utils';
import countries from './countries.json';
import * as yup from 'yup';
import { Product } from '@/components/products/create-products/types';
import * as WebBrowser from 'expo-web-browser';
import {
  hexToRgb,
  toAppUrl,
  ProductItemInterface,
  CountryInterface,
  Fees,
  WITHDRAWAL_FEE_TYPES,
  StoreCurrencySettings,
  CURRENCIES,
} from 'catlog-shared';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import isToday from 'dayjs/plugin/isToday';
import isYesterday from 'dayjs/plugin/isYesterday';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import authStore from 'src/contexts/auth/store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { RootStackParamList } from 'src/@types/navigation';
import * as Device from 'expo-device';
import { ApiError } from './api';
import * as Network from 'expo-network';
import useAuthStore from 'src/contexts/auth/store';

export const getStates = (country: string) => {
  const states = countries.find(c => c.code === country)?.states;

  return states?.map(s => s.name);
};
export const convertImageUrlsToImages = (imageUrls: string[]) => {
  return imageUrls?.map?.(image => ({
    src: image,
    name: '',
    lastModified: null,
    file: null,
    isUploading: false,
    uploadProgress: 100,
    url: image,
    error: false,
    key: null,
  }));
};

export default getStates;
export const isEven = (number: number) => number % 2 === 0;

export function hexToRgba(hex: string, opacity: number): string {
  // Remove the hash at the start if it's there
  hex = hex.replace('#', '');

  // Parse the r, g, b values
  let r, g, b;
  if (hex.length === 3) {
    r = parseInt(hex[0] + hex[0], 16);
    g = parseInt(hex[1] + hex[1], 16);
    b = parseInt(hex[2] + hex[2], 16);
  } else if (hex.length === 6) {
    r = parseInt(hex.substring(0, 2), 16);
    g = parseInt(hex.substring(2, 4), 16);
    b = parseInt(hex.substring(4, 6), 16);
  } else {
    r = 0;
    g = 0;
    b = 0;
  }

  // Return the RGBA color string
  return `rgba(${r},${g},${b},${opacity})`;
}

export const amountFormat = (amount: number | string, toFixed: number = 2): string => {
  const numericAmount = parseFloat(amount.toString());

  if (isNaN(numericAmount)) {
    // throw new Error("Invalid amount provided");
    return '';
  }

  const fixedAmount = numericAmount.toFixed(toFixed);
  const [integerPart, decimalPart] = fixedAmount.split('.');
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
};

export const toNaira = (amount: number) => {
  return amount / 100;
};

export const millify = (num: number, fractionDigits: number = 0, currency?: string) => {
  if (!num) return 0;

  // if (num % 1 === 0) {
  //   fractionDigits = 0;
  // }

  function getStr() {
    if (num > 999 && num < 1_000_000) {
      return (num / 1000).toFixed(num % 1000 > 0 ? fractionDigits : 0) + 'K'; // convert to K for number from > 1000 < 1 million
    } else if (num >= 1_000_000 && num < 1_000_000_000) {
      return (num / 1_000_000).toFixed(fractionDigits) + 'M'; // convert to M for number from > 1 million
    } else if (num >= 1_000_000_000) {
      return (num / 1_000_000_000).toFixed(fractionDigits) + 'B'; // convert to B for number from > 1 billion
    } else if (num < 1000) {
      return num.toFixed(0); // if value < 1000, nothing to do
    }
  }

  switch (Math.sign(num)) {
    case -1:
      num = Math.abs(num);
      return '-' + `${currency ? currency + ' ' : ''}${getStr()}`;
    case 1:
      return `${currency ? currency + ' ' : ''}${getStr()}`;
    default:
      return `${currency ? currency + ' ' : ''}${getStr()}`;
  }
};

export const toCurrency = (amount: number, currency?: string, convert: boolean = true, decimals?: number) => {
  const conversionRate = 1;
  const state = authStore.getState().getStore();
  const defaultCurrency = state?.currencies?.products;
  return `${currency || defaultCurrency} ${amountFormat(amount / conversionRate, decimals)}`;
};

export const formatDate = (date: Date | string, dateFormat?: string) => {
  // const dateString = format(new Date(date), dateFormat ? dateFormat : 'EEE, MMM d yyyy');
  const dateString = dayjs(date).format(dateFormat ?? 'D MMM YYYY, hh:mm A');
  return dateString;
};

export const dateDuration = (startDate: Date | string, endDate?: Date | string) => {
  let formattedStartDate = null;
  let formattedEndDate = 'TILL DELETED';
  if (startDate) {
    formattedStartDate = formatDate(startDate, "D MMM 'YY");
  }

  if (endDate) {
    formattedEndDate = formatDate(endDate, "D MMM 'YY");
  }
  return `${formattedStartDate} ${formattedEndDate !== null ? `- ${formattedEndDate}` : ''}`;
};

export const randomColor = () => {
  const colorVariants = [colors.accentYellow, colors.accentRed, colors.accentGreen, colors.accentOrange];
  let selectNumber = Math.floor(Math.random() * colorVariants.length);
  return colorVariants[selectNumber];
};

export function capitalizeWords(input: string): string {
  return input.toLowerCase().replace(/\b\w/g, char => char.toUpperCase());
}

export function normalizeEnums(input: string): string {
  return input.replace(/_/g, ' ');
}

export function getAvatarBg(name: string = '') {
  const char = name.charAt(0).toLowerCase();

  switch (true) {
    case char >= 'a' && char <= 'c':
      return colors.accentYellow.main;
    case char >= 'd' && char <= 'f':
      return colors.primary.main;
    case char >= 'g' && char <= 'i':
      return colors.accentGreen.main;
    case char >= 'j' && char <= 'l':
      return colors.accentOrange.main;
    case char >= 'm' && char <= 'o':
      return colors.accentRed.main;
    case char >= 'p' && char <= 'r':
      return colors.black.main;
    case char >= 's' && char <= 'u':
      return colors.accentRed.main;
    case char >= 'v' && char <= 'z':
      return colors.black.muted;
    default:
      return colors.accentOrange.light; //todo: revisit this
  }
}

export const subdomainStoreLink = (
  slug: string,
  withProtocol: boolean = false,
  type: 'store' | 'chowbot' = 'store',
  searchParams?: string,
) => {
  if (!EXPO_PUBLIC_CHOWBOT_URL || !EXPO_PUBLIC_PUBLIC_URL) {
    return '';
  }

  const baseUrls = {
    chowbot: EXPO_PUBLIC_CHOWBOT_URL,
    store: EXPO_PUBLIC_PUBLIC_URL,
  };

  // if (process.env.NODE_ENV !== 'production') {
  //   return `${baseUrls[type]?.replace(/^https?:\/\//, '')}/${slug}${searchParams ? `?${searchParams}` : ''}`;
  // }

  return `${withProtocol ? 'https://' : ''}${slug}.${baseUrls[type].replace(/^https?:\/\//, '')}${
    searchParams ? `?${searchParams}` : ''
  }`;
};

export function getFieldvalues(
  name: string,
  form: FormikProps<any>,
  type: 'text' | 'number' | 'select' = 'text',
  remove?: RegExp,
  stringify?: boolean,
) {
  const fromDotNotation = (place: { [key: string]: any }) => {
    if (place[name]) return place[name];
    if (name.includes('.')) {
      const parts = name.split('.');

      return parts.reduce((acc, part) => {
        return acc?.[part];
      }, place);
    }
  };

  return {
    name,
    // value: (form.values[name])
    selectedItem: type === 'select' ? fromDotNotation(form.values) : undefined,
    onChangeText: type !== 'select' ? form.handleChange(name) : undefined,
    onPressItem: type === 'select' ? value => form.setFieldValue(name, value) : undefined,
    value: stringify ? fromDotNotation(form.values)?.toString?.() : fromDotNotation(form.values),
    onBlur: form.handleBlur(name),
    error: fromDotNotation(form.touched) && fromDotNotation(form.errors) ? fromDotNotation(form.errors) : null,
    hasError: fromDotNotation(form.touched) && fromDotNotation(form.errors) !== undefined,
    errors: fromDotNotation(form.errors),
    touched: fromDotNotation(form.touched),
    type,
  };
}

export function sluggify(text: string, separator: string = '-'): string {
  let slug = text ? text.toLowerCase() : '';

  // Replace spaces and non-alphanumeric characters with the specified separator
  const regex = new RegExp(`[^a-z0-9]+`, 'g');
  slug = slug.replace(regex, separator);

  // Remove leading and trailing separators
  const trimRegex = new RegExp(`^${separator}+|${separator}+$`, 'g');
  slug = slug.replace(trimRegex, '');

  // Replace multiple separators with a single separator
  const multipleSeparatorRegex = new RegExp(`${separator}{2,}`, 'g');
  slug = slug.replace(multipleSeparatorRegex, separator);

  return slug;
}

export function ensureUniqueItems<T extends { id: K }, K>(items: T[]): T[] {
  const seenIds = new Set<K>();
  return items.filter(item => {
    if (seenIds.has(item.id)) {
      return false;
    } else {
      seenIds.add(item.id);
      return true;
    }
  });
}

export async function copyToClipboard(text: string, successMessage?: string) {
  try {
    await Clipboard.setStringAsync(text);
    Toast.show({ text1: successMessage ?? 'Copied to clipboard' });
    return true;
  } catch (error) {
    return false;
  }
}

export function generateSimpleUUID() {
  let timeStamp = new Date().getTime();
  let base36Timestamp = timeStamp.toString(36);

  let randomValues = Math.random().toString(36).substring(2, 10);
  let uuid = (base36Timestamp + randomValues).slice(0, 16);

  return uuid;
}

export function groupTransactionsByDate<T extends BaseTransaction>(transactions: T[]): GroupedTransactions<T>[] {
  if (transactions === undefined) {
    throw 'Transactions is undefined';
  }
  const grouped = transactions.reduce((acc: { [key: string]: T[] }, transaction) => {
    const dateStr = new Date(transaction.created_at).toISOString().split('T')[0];
    if (!acc[dateStr]) {
      acc[dateStr] = [];
    }
    acc[dateStr].push(transaction);
    return acc;
  }, {});

  return Object.entries(grouped)
    .map(([dateStr, transactions]) => ({
      date: new Date(dateStr),
      transactions,
    }))
    .sort((a, b) => b.date.getTime() - a.date.getTime());
}

// const updateOrDeleteItemFromList = (list: any[], indexKey: string, indexValue: any, newValue: any ) => {
//   const listCopy = [...list]
//   const index = list.find(l => l[indexKey] === indexValue);

//   if(!newValue) {

//   }

//   listCopy[index] = newValue
//   return listCopy

// }

/**
 * Updates or deletes an item from a list based on a given key and value.
 *
 * @template T The type of the items in the list
 * @template K The type of the key used for indexing (must be a string)
 * @param {WithKey<T, K>[]} list - The original list of items
 * @param {K} indexKey - The key to use for finding the item
 * @param {any} indexValue - The value to match against the indexKey
 * @param {T | null} newValue - The new value to update with, or null to delete
 * @returns {WithKey<T, K>[]} A new list with the update applied, or the original list if item not found
 */
export function updateOrDeleteItemFromList<T, K extends string>(
  list: WithKey<T, K>[],
  indexKey: K,
  indexValue: any,
  newValue: Partial<T> | null,
): WithKey<T, K>[] {
  const index = list.findIndex(item => item[indexKey] === indexValue);

  if (index === -1) {
    console.log('Index not found');
    // Item not found, return the original list
    return list;
  }

  const listCopy = [...list];

  if (newValue === null) {
    // Delete the item
    listCopy.splice(index, 1);
  } else {
    // Update the item
    listCopy[index] = { ...listCopy[index], ...newValue };
  }

  return listCopy;
}

export const getCommunityLink = (country: CountryInterface | string) => {
  const COMMUNITY_LINK = {
    NG: 'https://chat.whatsapp.com/HOfRYk0Jdz1HvxYbOiKWNG',
    GH: 'https://chat.whatsapp.com/LweESv4ut397qfjeVuEMeN',
  };
  if (typeof country === 'string') {
    return COMMUNITY_LINK[country];
  }

  return COMMUNITY_LINK[country?.code];
};

export const timeToClock = (time: number) => {
  const minutes = Math.floor(time / 60000);
  const seconds = Math.floor((time % 60000) / 1000);

  return `${minutes}mins ${seconds}secs`;
};

export const getProductLink = (product: Partial<ProductItemInterface>) => {
  const productLink = toAppUrl(`p/${product?.slug}`, true, EXPO_PUBLIC_PUBLIC_URL);
  return productLink;
};

export const cx = classNames;
export const Yup = yup;
export const convertItemToProduct = (item: ProductItemInterface) => {
  return {
    id: item.id,
    name: item?.name,
    thumbnail: item?.thumbnail,
    images: convertImageUrlsToImages(item?.images),
    price: item?.price?.toString(),
    discount_price: item?.discount_price?.toString(),
    price_unit: item?.price_unit,
    category: item?.category?.id,
    description: item?.description,
    variants: item?.variants,
    quantity: item?.quantity,
    is_always_available: item?.is_always_available,
    // minimum_order_quantity: item?.minimum_order_quantity,
    // cost_price: item?.cost_price,
  };
};

export const generateColorSet = <T>(color: string, array: T[], minOpacity: number = 0.1): string[] => {
  // Validate inputs
  const baseColor = { ...hexToRgb(color), a: 1 };
  if (array.length === 0) return [];
  if (minOpacity < 0 || minOpacity > 1) {
    throw new Error('minOpacity must be between 0 and 1');
  }

  if (array.length === 1) {
    return [`rgba(${baseColor.r}, ${baseColor.g}, ${baseColor.b}, 1.00)`];
  }

  const opacityStep = (1 - minOpacity) / (array.length - 1);

  return array.map((_, index) => {
    const opacity = 1 - opacityStep * index;
    return `rgba(${baseColor.r}, ${baseColor.g}, ${baseColor.b}, ${opacity.toFixed(2)})`;
  });
};

export const delay = async (time: number): Promise<void> => {
  return new Promise(resolve => {
    const id = setTimeout(() => {
      resolve();
      clearTimeout(id);
    }, time);
  });
};

export const alertPromise = (
  title: string,
  message: string,
  acceptText = 'Yes',
  rejectText = 'No',
  isDestructive?: boolean,
) => {
  return new Promise<boolean>(resolve => {
    Alert.alert(title, message, [
      { text: rejectText, onPress: () => resolve(false), style: isDestructive ? 'default' : 'destructive' },
      { text: acceptText, onPress: () => resolve(true), style: isDestructive ? 'destructive' : 'default' },
    ]);
  });
};

export const toKobo = (amount: number) => {
  return amount * 100;
};

export const calculateWithdrawalFee = (amount: number, fees: Fees['fees']) => {
  const feeBand = fees.find(band => toKobo(amount) >= band.floor && toKobo(amount) < band.ceil);

  const fee = !feeBand
    ? (0.25 / 100) * amount
    : feeBand?.fee_type === WITHDRAWAL_FEE_TYPES.FIXED
      ? feeBand?.fee + (feeBand?.percentage ?? 0) * toKobo(amount)
      : feeBand?.fee * toKobo(amount);

  return fee;
};
export const transformProductData = (productItem: ProductItemInterface) => {
  const transFormedImage = productItem.images.map(item => ({
    src: item,
    name: '',
    file: null,
    isUploading: false,
    uploadProgress: 0,
    url: item,
    error: false,
    newFile: false,
    key: item,
    imageHash: '',
    isPicker: false,
  }));

  const transformData: Product = {
    thumbnail: productItem?.thumbnail,
    images: productItem.images.map(item => ({
      src: item,
      name: '',
      file: null,
      isUploading: false,
      uploadProgress: 0,
      url: item,
      error: false,
      newFile: false,
      key: item,
      imageHash: '',
      isPicker: false,
    })),
    name: productItem.name,
    price: String(productItem.price),
    discount_price: productItem?.discount_price ? String(productItem?.discount_price) : '',
    category: productItem.category?.id ?? '',
    price_unit: productItem?.price_unit,
    is_always_available: productItem?.is_always_available,
    quantity: productItem?.quantity,
    description: productItem.description,
    variants: productItem.variants,
    hasImages: true,
    id: productItem.id,
    minimum_order_quantity: productItem.minimum_order_quantity,
    cost_price: productItem.cost_price,
    expiry_date: productItem.expiry_date ? new Date(productItem.expiry_date) : undefined,
  };
  return transformData;
};

export const removeUnderscores = (str: string) => str.replace(/_/g, ' ');

export const capitalizeStr = (str: string) => {
  return str
    .split(' ')
    .map(word => word.trim().charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

export const removeEmptyAndUndefined = <T>(obj: T) => {
  for (const key in obj) {
    if (Array.isArray(obj[key])) {
      if (obj[key].length === 0) {
        delete obj[key];
      }
    }

    if (obj[key] === undefined || obj[key] === null || obj[key] === '') {
      delete obj[key];
    }
  }
  return obj as T;
};

export function dateToDDMMYYYY(date: Date) {
  if (!date) return '';

  return (
    (date.getDate() > 9 ? date.getDate() : '0' + date.getDate()) +
    '-' +
    (date.getMonth() > 8 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1)) +
    '-' +
    date.getFullYear()
  );
}

export function DDMMYYYYToDate(dateString) {
  const [day, month, year] = dateString.split('-').map(Number);
  const dateObject = new Date(year, month - 1, day);

  return dateObject;
}

export function getBase64Size(base64String) {
  const yourBase64String = base64String.substring(base64String.indexOf(',') + 1);
  const kb = Math.ceil((yourBase64String.length * 6) / 8 / 1000); // 426 kb
  return kb;
}

export function base64ToUri(base64String) {
  const yourBase64String = base64String.substring(base64String.indexOf(',') + 1);
  return `data:image/jpeg;base64,${yourBase64String}`;
}
export function enumToHumanFriendly(text: string, delimiter: string = '_'): string {
  const splitText = text.split(delimiter);

  console.log({ splitText });

  return splitText.map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(' ');
}
export function humanFriendlyToEnum(text: string, delimiter: string = ' '): string {
  const splitText = text.split(delimiter);

  console.log({ splitText });

  return splitText.map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join('_');
}

export const openLinkInBrowser = async (link: string) => {
  await WebBrowser.openBrowserAsync(link);
};

export const removeCountryCode = (phone: string) => {
  const splitPhone = phone.split('-');

  if (splitPhone.length > 1) {
    return `0${splitPhone[1]}`;
  }
  return phone;
};

dayjs.extend(relativeTime);
dayjs.extend(isToday);
dayjs.extend(isYesterday);
dayjs.extend(advancedFormat);

// Function to get the ordinal suffix (1st, 2nd, 3rd, etc.)
const getOrdinal = (day: number): string => {
  if (day > 3 && day < 21) return `${day}th`; // Covers 11th to 19th
  switch (day % 10) {
    case 1:
      return `${day}st`;
    case 2:
      return `${day}nd`;
    case 3:
      return `${day}rd`;
    default:
      return `${day}th`;
  }
};

export const humanFriendlyRelativeDate = (date: string | Date): string => {
  const inputDate = dayjs(date);
  const today = dayjs();
  const daysDiff = today.diff(inputDate, 'day');

  if (inputDate.isToday()) {
    return 'Today, ' + inputDate.format('h:mm A'); // e.g., "5:00 PM"
  }

  if (inputDate.isYesterday()) {
    return 'Yesterday';
  }

  if (daysDiff < 7) {
    return inputDate.format('dddd'); // e.g., "Monday"
  }

  return `${getOrdinal(inputDate.date())} ${inputDate.format('MMM YYYY')}`; // e.g., "1st Jan 2022"
};

export const getColorAlternates = (index: number) => {
  const colorAlternates = [
    { iconColor: colors.accentOrange.main, bgColor: colors.accentOrange.pastel },
    { iconColor: colors.accentGreen.main, bgColor: colors.accentGreen.pastel },
    { iconColor: colors.accentRed.main, bgColor: colors.accentRed.pastel },
    { iconColor: colors.accentYellow.main, bgColor: colors.accentYellow.pastel },
  ];
  return colorAlternates[index ? index % colorAlternates.length : 0];
};

export const showError = (error: ApiError | string | null, customMessage?: string) => {
  let message: string;

  if (customMessage) {
    message = customMessage;
  } else if (typeof error === 'string') {
    message = error;
  } else if (error?.message) {
    message = error.message;
  } else {
    message = 'An error occurred, please try again later';
  }

  Toast.show({ type: 'error', text1: message });
};

export const showSuccess = (message: string) => {
  Toast.show({ type: 'success', text1: message });
};

export const checkNetworkConnectivity = async (): Promise<boolean> => {
  try {
    // This function is now just a wrapper around the context's checkNetworkStatus
    // The actual implementation is in the NetworkProvider
    // This allows us to maintain backward compatibility

    // If we're in a context where useNetwork is available, use it
    try {
      // We need to dynamically import to avoid circular dependencies
      const { useNetwork } = require('src/contexts/network-context');
      const network = useNetwork();

      if (network && typeof network.checkNetworkStatus === 'function') {
        return await network.checkNetworkStatus();
      }
    } catch (e) {
      // If we can't use the context (e.g., during initialization), fall back to direct check
    }

    // Fallback to direct check if context is not available
    const networkState = await Network.getNetworkStateAsync();
    if (!networkState.isConnected || !networkState.isInternetReachable) {
      showError(null, 'No internet connection. Please check your network settings and try again.');
      return false;
    }
    return true;
  } catch (error) {
    console.error('Failed to check network status:', error);
    return false;
  }
};

export const showLoader = async (message: string, shouldDelay = false, autoHide = false, subMessage?: string) => {
  if (shouldDelay) {
    await delay(700);
  }
  Toast.show({ type: 'activity', autoHide: autoHide, text1: message, text2: subMessage });
};

export const hideLoader = () => {
  Toast.hide();
};

export const optionsMap = (object: {}) => Object.entries(object).map(item => ({ name: item[1], value: item[1] }));

function computeMissingRates(rates: { [key: string]: { [key: string]: number } }, baseCurrency: CURRENCIES) {
  if (!rates?.[baseCurrency]) {
    // throw new Error(`Base currency ${baseCurrency} does not exist in the rates object.`);
    return {};
  }

  const usdRate = rates[baseCurrency]['USD'];
  if (!usdRate) {
    return {};
  }

  for (const targetCurrency in rates) {
    if (targetCurrency === baseCurrency) continue;
    if (rates[baseCurrency][targetCurrency]) continue;

    const targetRateFromUSD = rates['USD'][targetCurrency];
    if (targetRateFromUSD) {
      rates[baseCurrency][targetCurrency] = usdRate * targetRateFromUSD;
    }
  }

  return rates[baseCurrency];
}

export function getExchangeRates(
  allRates: { [key: string]: { [key: string]: number } },
  defaultCurrency: CURRENCIES,
  allCurrencies: CURRENCIES[],
) {
  try {
    if (allCurrencies.length > 1 || (allCurrencies.length === 1 && allCurrencies[0] !== defaultCurrency)) {
      const rates = computeMissingRates(allRates, defaultCurrency);

      return rates;
    } else {
      return { [defaultCurrency]: 1 };
    }
  } catch (error) {
    console.log('Error setting conversion rates', error);

    return null;
  }
}

export const formatAsCurrency = (
  amount: number | string,
  rates?: StoreCurrencySettings['rates'],
  currency?: string,
  markups?: StoreCurrencySettings['rates'],
  convert: boolean = true,
  decimals: number = 2,
): string => {
  const activeCurrency = currency;

  const conversionRate = convert && rates && rates[currency] !== undefined ? rates[currency] : 1;
  const markup = markups?.[currency] || 0;

  // Scale numbers to avoid floating-point issues
  const scale = Math.pow(10, decimals);

  const amountWithMarkup = Math.round(+amount * (1 + markup / 100) * scale) / scale;
  const convertedAmount = Math.round(amountWithMarkup * conversionRate * scale) / scale;

  return `${currency} ${amountFormat(convertedAmount, decimals)}`;
};

export const hasAllItems = (superset: any[], subset: any[]) => subset.every(item => superset.includes(item));

export function getWinDim(padding?: number, isWidth = true) {
  if (isWidth) {
    return Dimensions.get('window').width - (padding ?? 0);
  }
  return Dimensions.get('window').height - (padding ?? 0);
}

/**
 * Result of a Yup validation test
 */
interface ValidationResult<T> {
  isValid: boolean;
  validatedData: T | null;
  errors: yup.ValidationError | null;
}

/**
 * Tests form data against a Yup validation schema and returns the validation result
 * @param schema - The Yup validation schema to test against
 * @param formData - The form data to validate
 * @param options - Additional validation options
 * @returns Object with validation result and any error details
 */
export const testYupValidation = <T>(
  schema: yup.Schema<T>,
  formData: unknown,
  options: yup.ValidateOptions = { abortEarly: false },
): ValidationResult<T> => {
  try {
    // Attempt to validate the form data against the schema
    const validatedData = schema.validateSync(formData, options) as T;

    return {
      isValid: true,
      validatedData,
      errors: null,
    };
  } catch (error) {
    // If validation fails, return the validation errors
    return {
      isValid: false,
      validatedData: null,
      errors: error instanceof yup.ValidationError ? error : null,
    };
  }
};

export const setDisappearingError = (error: string, setError: (error: string) => void, timeout: number = 3000) => {
  setError(error);

  setTimeout(() => {
    setError('');
  }, timeout);
};

export const logAllAsyncStorage = async () => {
  try {
    // Get all keys
    const keys = await AsyncStorage.getAllKeys();
    console.log('All AsyncStorage keys:', keys);

    // Get all values for each key
    const items = await AsyncStorage.multiGet(keys);

    // Log each key-value pair
    console.log('All AsyncStorage contents:');
    items.forEach(([key, value]) => {
      console.log(`${key}: ${value}`);
    });

    return items;
  } catch (error) {
    console.error('Error logging AsyncStorage:', error);
  }
};

export function getActiveRouteName(state: any): keyof RootStackParamList | undefined {
  if (!state) return;

  const route = state.routes[state.index];

  // Dive into nested navigators if they exist
  if (route.state) {
    return getActiveRouteName(route.state);
  }

  return route.name;
}
export const deviceType = async () => {
  const deviceType = await Device.getDeviceTypeAsync();
  return deviceType;
};

const isTablet = () => {
    return Dimensions.get('window').width > 768;
  };

  // export const getDynamicFunctionKey = (functionName: string) => {
  //   const store = useAuthStore.getState().storeId;
  //   return `${functionName}${store}`;
  // };
