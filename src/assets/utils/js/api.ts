import AsyncStorage from '@react-native-async-storage/async-storage';
import useAuthStore from 'src/contexts/auth/store';

const BASE_URL = process.env.EXPO_PUBLIC_API_URL;
// export type ApiFunction = (params: any) => { endpoint: string; method: string; data?: any };

export interface RequestInterface<T> {
  data: unknown;
  error: Error | null;
  isLoading: boolean;
  makeRequest: (newParams: T) => Promise<[any, any]>;
}

export interface ApiError {
  message: string;
  body?: any;
  status?: number;
  fields?: Record<string, string>;
}

interface CustomizationProps {
  data?: any;
  headers?: any;
  [key: string]: any;
}

export async function client(
  endpoint: string,
  method: string,
  customization: CustomizationProps = {},
  baseUrl: string = BASE_URL,
  clearUserData: () => void,
) {
  const { data, headers: customHeaders, ...customConfig } = customization;

  console.info(`Making ${method} request to ${endpoint}`);

  const authData = JSON.parse((await AsyncStorage.getItem('auth-storage')) ?? '{}');
  const token = authData?.state?.token;

  if (!baseUrl) {
    throw new Error('Base URL missing');
    return;
  }

  const headers = {
    Authorization: token ? `Bearer ${token}` : undefined,
    'Content-Type': data ? 'application/json' : undefined,
    ...customHeaders,
  };

  const config = {
    method,
    body: data ? JSON.stringify(data) : undefined,
    headers,
    ...customConfig,
  };

  try {
    const response = await fetch(`${baseUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`, config);
    const responseData = response.status === 204 ? {} : await response.json();

    if (response.status === 401 && token) {
      await AsyncStorage.removeItem('auth-storage');
      clearUserData();
      // Handle reload or re-authentication for the mobile app
    }

    if (response.ok) {
      return responseData;
    } else {
      throw getCustomError(responseData, response.status);
    }
  } catch (error) {
    console.error(`Endpoint:${endpoint}`, error);
    throw {
      message: error?.message ?? 'Something went wrong, please check your network connection',
      body: error,
      status: 500,
    };
  }
}

const getCustomError = (error: any, status?: number): ApiError => {
  // If error is already in correct format, return as is
  if (typeof error === 'object' && 'message' in error) {
    return {
      message: error.message,
      body: error,
      status: status ?? 500,
      fields: error.fields
    };
  }

  // Otherwise, format it
  return {
    message: typeof error === 'string' ? error : 'Something went wrong',
    body: error,
    status: status ?? 500
  };
};

// class CustomError extends Error {
//   message: string;
//   body: any;
//   status: number;

//   constructor(error: any, status?: number) {
//     super();
//     this.message = error?.message ?? 'Something went wrong';
//     this.body = { ...error };
//     this.status = status ?? 500;
//   }
// }
