import { useNavigation } from '@react-navigation/native';
import { ORDER_STATUSES, OrderInterface, paramsFromObject } from 'catlog-shared';
import { Bag, Edit2, ExportSquare } from 'iconsax-react-native/src';
import { useState } from 'react';
import { ActivityIndicator, Alert, View } from 'react-native';
import * as Animatable from 'react-native-animatable';
import Toast from 'react-native-toast-message';
import AnalyticsBtn from 'src/components/ui/buttons/analytics-btn';
import { DropDownItem } from 'src/components/ui/inputs/select-dropdown';
import DateRangeModal, { DateRangeString } from 'src/components/ui/modals/date-range-modal';
import SearchContainer, { SEARCH_BG_VARIANT } from 'src/components/ui/others/search-container';
import useAuthStore from 'src/contexts/auth/store';
import { useFileDownload } from 'src/hooks/use-file-download';
import useModals from 'src/hooks/use-modals';
import useScrollHandler from 'src/hooks/use-scroll-handler';

import { hide<PERSON>oader, hp, showLoader, wp } from '@/assets/utils/js';
import OrdersList from '@/components/orders/list';
import { CircledIcon, Row } from '@/components/ui';
import FAB from '@/components/ui/buttons/fab';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import TopTabs from '@/components/ui/others/top-tabs';
import Shimmer from '@/components/ui/shimmer';
import { ResponseWithPagination } from '@/hooks/use-api';
import colors from '@/theme/colors';
import Can from 'src/components/ui/can';
import { actionIsAllowed, SCOPES } from 'src/assets/utils/js/permissions';
import useAuthContext from 'src/contexts/auth/auth-context';
import useInteractionWait from 'src/hooks/use-interaction-wait';

enum OrdersType {
  PENDING_ORDERS = 'Pending Orders',
  PROCESSING_ORDERS = 'Processing Orders',
  FULFILLED_ORDERS = 'Fulfilled Orders',
  CANCELLED_ORDERS = 'Cancelled Orders',
  ABANDONED_ORDERS = 'Abandoned Orders',
}

export interface OrdersResponse extends ResponseWithPagination<{ data: OrderInterface[] }> {}

const Orders = () => {
  const { subscription } = useAuthContext();
  const navigation = useNavigation();
  const [tabIndex, setTabIndex] = useState(0);
  const showBottomTab = useAuthStore(state => state.showBottomTab);
  const { modals, toggleModal } = useModals(['dateRange']);

  const { downloadFile } = useFileDownload();
  const { scrollHandler, headerStyle, inputStyle } = useScrollHandler(100);

  const actionItems: DropDownItem[] = [
    {
      label: 'Customer Carts',
      value: 'cart',
      onPress: () => navigation.navigate('Carts'),
      leftElement: (
        <CircledIcon>
          <Bag size={wp(15)} color={colors.accentGreen.main} strokeWidth={2} />
        </CircledIcon>
      ),
    },
    {
      label: 'Export Orders',
      value: 'export-orders',
      onPress: () => {
        const isAllowed = actionIsAllowed({
          planPermission: SCOPES.ORDERS.EXPORT_ORDERS,
          plan: subscription?.plan?.type,
        });
        if (!isAllowed) {
          Alert.alert('Upgrade Plan!', "You'll need to be on the business plus plan to export orders", [
            {
              text: 'Manage Subscription',
              onPress: () => navigation.navigate('StoreInformation'),
              style: 'destructive',
            },
            { text: 'Cancel', onPress: () => {} },
          ]);
          return;
        }
        toggleModal('dateRange');
      },
      actionDelay: 600,
      leftElement: (
        <CircledIcon>
          <ExportSquare size={wp(14)} color={colors.primary.main} strokeWidth={2} />
        </CircledIcon>
      ),
    },
    {
      label: 'Record Order',
      value: 'record-order',
      onPress: () => navigation.navigate('RecordOrder'),
      leftElement: (
        <CircledIcon>
          <Edit2 size={wp(14)} color={colors.accentOrange.main} strokeWidth={2} />
        </CircledIcon>
      ),
    },
  ];

  const handleExportOrders = async (dateRange: DateRangeString) => {
    try {
      const reqData = {
        filter: {
          from: dateRange.from,
          to: dateRange.to,
        },
      };
      if (dateRange.from === null || dateRange.to === null) return;

      const subUrl = `orders/export?${paramsFromObject(reqData)}`;
      const fileName = `orders-${new Date(dateRange.from).toDateString()}-${new Date(dateRange.to).toDateString()}.xlsx`;
      showLoader('Exporting orders', true);
      const downloadResult = await downloadFile(subUrl, fileName);
      hideLoader();
    } catch (error) {
      Toast.show({ text1: 'Error processing your request', type: 'error' });
      console.log(error);
    }
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: 'Latest Orders',
        variant: HeaderVariants.ROOT_LEVEL,
      }}>
      <SearchContainer
        {...{ headerStyle, inputStyle }}
        color={SEARCH_BG_VARIANT.RED}
        onPressSearch={() => navigation.navigate('SearchOrders')}
        placeholder="Search Orders"
        actions={actionItems}
      />
      <Can data={{ permission: SCOPES.ORDERS.VIEW_ANALYTICS }}>
        <AnalyticsBtn onPress={() => navigation.navigate('OrderAnalytics')} bgColorVariant="RED" />
      </Can>
      <TopTabs
        setIndex={setTabIndex}
        currentIndex={tabIndex}
        swipeEnabled={showBottomTab}
        disableTabSwitcher={!showBottomTab}
        tabItems={[
          {
            key: OrdersType.PENDING_ORDERS,
            title: OrdersType.PENDING_ORDERS,
            component: <OrdersList {...{ scrollHandler }} filters={{ status: ORDER_STATUSES.PENDING }} />,
            func: () => {},
          },
          {
            key: OrdersType.PROCESSING_ORDERS,
            title: OrdersType.PROCESSING_ORDERS,
            component: <OrdersList {...{ scrollHandler }} filters={{ status: ORDER_STATUSES.PROCESSING }} />,
            func: () => {},
          },
          {
            key: OrdersType.FULFILLED_ORDERS,
            title: OrdersType.FULFILLED_ORDERS,
            component: <OrdersList {...{ scrollHandler }} filters={{ status: ORDER_STATUSES.FULFILLED }} />,
            func: () => {},
          },
          {
            key: OrdersType.ABANDONED_ORDERS,
            title: OrdersType.ABANDONED_ORDERS,
            component: <OrdersList {...{ scrollHandler, navigation }} filters={{ status: ORDER_STATUSES.ABANDONED }} />,
            func: () => {},
          },
          {
            key: OrdersType.CANCELLED_ORDERS,
            title: OrdersType.CANCELLED_ORDERS,
            component: <OrdersList {...{ scrollHandler, navigation }} filters={{ status: ORDER_STATUSES.CANCELLED }} />,
            func: () => {},
          },
        ]}
      />
      {showBottomTab && <FAB onPress={() => navigation.navigate('RecordOrder')} />}
      <DateRangeModal
        isVisible={modals.dateRange}
        closeModal={() => toggleModal('dateRange', false)}
        onPressProceed={handleExportOrders}
      />
    </DashboardLayout>
  );
};

export default Orders;

const OrderListSkeleton = () => {
  return (
    <View className="border border-grey-border rounded-12 mb-15">
      <Row className="p-15 border-b border-b-grey-border">
        <View className="flex-row items-center">
          <Shimmer {...{ height: hp(16), width: wp(16), borderRadius: wp(5) }} />
          <Shimmer {...{ height: hp(10), width: wp(90), borderRadius: wp(100) }} classes="ml-5" />
        </View>
        <Shimmer {...{ height: hp(6), width: wp(20), borderRadius: wp(50) }} />
      </Row>
      <View className="pt-12 p-15">
        <Row>
          <Shimmer {...{ height: hp(40), width: wp(40), borderRadius: wp(10) }} />
          <View className="mx-12 flex-1">
            <Shimmer {...{ height: hp(12), width: wp(150), borderRadius: wp(10) }} classes="mb-8" />
            <Shimmer {...{ height: hp(13), width: wp(100), borderRadius: wp(10) }} />
          </View>
          <Shimmer {...{ height: hp(27), width: wp(27), borderRadius: wp(100) }} />
          <Shimmer
            {...{ height: hp(20), width: wp(50), borderRadius: wp(100) }}
            classes="-ml-10 border-2 border-white"
          />
        </Row>
      </View>
    </View>
  );
};

export const OrderListSkeletonLoader = () => {
  return (
    <View>
      {Array.from({ length: 10 }, (_, i) => i).map(i => (
        <OrderListSkeleton key={i} />
      ))}
    </View>
  );
};
