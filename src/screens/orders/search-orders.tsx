import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { useFormik } from 'formik';
import { useState } from 'react';
import SearchHeader from '@/components/search/search-header';
import useModals from 'src/hooks/use-modals';
import { getFieldvalues, removeEmptyAndUndefined } from 'src/assets/utils/js';
import { DELIVERY_METHODS, GetItemsParams, GetOrdersParams, ORDER_CHANNELS, ORDER_PAID_TAG } from 'catlog-shared';
import OrdersList from 'src/components/orders/list';
import SearchOrderFilterModal from 'src/components/search/search-orders-filter-modal';
import { View } from 'react-native';

const SearchOrders = () => {
  const { modals, toggleModal } = useModals(['searchOrderFIlter']);
  const [filters, setFilters] = useState<GetOrdersParams['filter']>({});
  const validFilters = removeEmptyAndUndefined(filters);

  const form = useFormik<SearchOrdersParams>({
    initialValues: {
      payment_status: undefined,
      customer: '',
      search: '',
      fulfillment_method: undefined,
      channel: undefined,
      products: [],
    },
    onSubmit: value => {
      const filter = removeEmptyAndUndefined(value);
      setFilters(filter);
    },
  });

  const submitForm = () => {
    toggleModal('searchOrderFIlter', false);
    form.submitForm();
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: 'Search Orders',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <SearchHeader
        inputProps={{
          ...getFieldvalues('search', form),
          onSubmitEditing: () => form.submitForm(),
          placeholder: 'Search Orders',
        }}
        showFilter
        numberOfActiveFilters={Object.keys(filtersWithoutSearch(validFilters)).length}
        onPressFilterButton={() => toggleModal('searchOrderFIlter')}
      />
      <View className="pt-10 flex-1">
        <OrdersList isSearchPage externalFilters={validFilters} filters={validFilters} />
      </View>
      <SearchOrderFilterModal
        isVisible={modals.searchOrderFIlter}
        onPressContinue={submitForm}
        form={form}
        closeModal={() => toggleModal('searchOrderFIlter', false)}
      />
    </DashboardLayout>
  );
};

export default SearchOrders;

const filtersWithoutSearch = (filters: GetItemsParams['filter']) => {
  const { search, ...rest } = filters;
  return rest;
};

export interface SearchOrdersParams {
  search?: string;
  payment_status?: ORDER_PAID_TAG;
  customer?: '';
  fulfillment_method: DELIVERY_METHODS;
  channel: ORDER_CHANNELS;
  products: string[];
}
