import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import useOrdersApi from 'src/hooks/use-orders-api';
import { useState } from 'react';
import { ResponseWithPagination, useApi } from 'src/hooks/use-api';
import CartItemCard from 'src/components/orders/cart/cart-item-card';
import { RefreshControl, View } from 'react-native';
import { FlatList } from 'react-native';
import { ensureUniqueItems, hp, wp } from 'src/assets/utils/js';
import usePagination from 'src/hooks/use-pagination';
import EmptyState from 'src/components/ui/empty-states/empty-state';
import { CircledIcon, Row } from 'src/components/ui';
import colors from 'src/theme/colors';
import { Bag } from 'iconsax-react-native/src';
import Shimmer from 'src/components/ui/shimmer';
import CartDetailModal from 'src/components/orders/cart/cart-detail-modal';
import useModals from 'src/hooks/use-modals';
import { ORDER_STATUSES, OrderInterface, GET_CARTS, GetCartsParams, CartInterface } from 'catlog-shared';

interface CartResponseWithPagination extends ResponseWithPagination<CartInterface[]> {}
interface CartResponse {
  data: CartResponseWithPagination;
}

const PER_PAGE = 10;

const Carts = () => {
  const [carts, setCarts] = useState<CartInterface[]>([]);
  const [activeCart, setActiveCart] = useState<CartInterface>({} as CartInterface);
  const { currentPage, goNext, setPage } = usePagination();

  const { modals, toggleModal } = useModals(['cartDetailModal']);

  const getCartsRequest = useApi<GetCartsParams, CartResponse>(
    {
      apiFunction: GET_CARTS,
      method: 'GET',
      key: 'get-orders',
      onSuccess: response => {
        setCarts(prev => ensureUniqueItems([...prev, ...response?.data?.data]));
      },
    },
    {
      page: currentPage,
      per_page: PER_PAGE,
      sort: 'DESC',
    },
  );

  const handlePullToRefresh = () => {
    setCarts([]);

    if (currentPage === 1) {
      getCartsRequest.makeRequest({
        page: currentPage,
        per_page: PER_PAGE,
        sort: 'DESC',
      });
      return;
    }

    setPage(1);
  };

  const handleOnEndReach = () => {
    if (
      !getCartsRequest?.isLoading &&
      carts?.length > 0 &&
      currentPage < getCartsRequest?.response?.data?.total_pages
    ) {
      goNext(getCartsRequest?.response?.data?.total_pages);
    }
  };

  const handleOpenCartModal = (item: CartInterface) => {
    setActiveCart(item);
    setTimeout(() => {
      toggleModal('cartDetailModal');
    }, 300);
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: 'Latest Carts',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <FlatList
        data={carts}
        refreshControl={<RefreshControl refreshing={false} onRefresh={() => handlePullToRefresh()} />}
        renderItem={({ item, index }) => (
          <CartItemCard index={index} cartItem={item} onPress={() => handleOpenCartModal(item)} />
        )}
        ItemSeparatorComponent={() => <View className="border-b border-b-grey-border my-15 mx-20" />}
        ListFooterComponent={() => (
          <View style={{ marginBottom: 120 }}>
            {carts?.length! > 0 && getCartsRequest.isLoading && (
              <View>
                <CartListSkeletonLoader />
              </View>
            )}
          </View>
        )}
        ListEmptyComponent={() =>
          getCartsRequest?.isLoading! || getCartsRequest?.isReLoading! ? (
            <CartListSkeletonLoader />
          ) : (
            <EmptyState
              icon={
                <CircledIcon
                  className="mb-20 p-15 bg-white"
                  style={{
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 10,
                    },
                    shadowOpacity: 0.05,
                    shadowRadius: 6.84,

                    elevation: 5,
                  }}>
                  <Bag variant={'Bulk'} size={wp(40)} color={colors.grey.muted} />
                </CircledIcon>
              }
              text={'Customers cart \n will appear here'}
            />
          )
        }
        onEndReached={handleOnEndReach}
        className="flex-1 px-10 pt-20"
        contentContainerStyle={{ flexGrow: 1 }}
      />
      <CartDetailModal
        activeCart={activeCart}
        isVisible={modals.cartDetailModal}
        closeModal={() => toggleModal('cartDetailModal', false)}
      />
    </DashboardLayout>
  );
};

export default Carts;

const CartListSkeleton = () => {
  return (
    <View className={`flex-row items-center border-b border-b-grey-border py-15`}>
      <Shimmer {...{ height: 50, width: 50, borderRadius: 100 }} />
      <View className={'mx-12'}>
        <Row className="mr-12 mb-10">
          <Shimmer {...{ height: 15, width: 90, borderRadius: 50 }} className="mr-2" />
          <Shimmer {...{ height: 15, width: 30, borderRadius: 50 }} />
        </Row>
        <Shimmer {...{ height: 10, width: 150, borderRadius: 50 }} />
      </View>
    </View>
  );
};

export const CartListSkeletonLoader = () => {
  return (
    <View className="mx-20">
      {Array.from({ length: 10 }, (_, i) => i).map(i => (
        <CartListSkeleton key={i} />
      ))}
    </View>
  );
};
