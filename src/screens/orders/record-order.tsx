import React, { useMemo } from 'react';
import { ScrollView, View } from 'react-native';
import colors from '@/theme/colors';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { ArrowRight, ChevronDown, Twitter } from '@/components/ui/icons';
import Container from '@/components/ui/container';
import { copyToClipboard, delay, hideLoader, showLoader, testYupValidation, wp } from '@/assets/utils/js';
import { useRef, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import Pressable from '@/components/ui/base/pressable';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import {
  Category2,
  Copy,
  Instagram,
  Link21,
  ReceiveSquare,
  Send2,
  Shop,
  TickCircle,
  TransmitSquare,
  Whatsapp,
} from 'iconsax-react-native/src';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { AccordionMethod } from '@/components/ui/others/accordion';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import Separator from '@/components/ui/others/separator';
import SelectDropdown, { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import { useFormik } from 'formik';
import useAuthContext from '@/contexts/auth/auth-context';
import * as Yup from 'yup';
import OrderItemsSection from '@/components/orders/create/order-items-section';
import CustomerInformationSection from '@/components/orders/create/customer-information-section';
import DeliveryInformationSection from '@/components/orders/create/delivery-information-section';
import DiscountAndFeeSection from '@/components/orders/create/discount-and-fee-section';
import Toast from 'react-native-toast-message';
import SectionContainer from '@/components/ui/section-container';
import ListItemCard from '@/components/ui/cards/list-item-card';
import {
  ORDER_CHANNELS,
  ORDER_STATUSES,
  CREATE_ORDER,
  DELIVERY_METHODS,
  CURRENCY_OPTIONS,
  CURRENCY_FLAG_MAP,
  OrderInterface,
  toAppUrl,
} from 'catlog-shared';
import { CreateOrderFormParams, RECORD_ORDER_STEPS } from './order.types';
import CustomImage from 'src/components/ui/others/custom-image';
import { useCurrencyConverter } from 'src/hooks/useCurrencyConverter';
import { EXPO_PUBLIC_PUBLIC_URL } from '@env';
import { useFileDownload } from 'src/hooks/use-file-download';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';

const RecordOrder = () => {
  const [orderRecorded, setOrderRecorded] = useState(false);
  const orderItemAccordionRef = useRef<AccordionMethod>(null);
  const customerAccordionRef = useRef<AccordionMethod>(null);
  const deliveryAccordionRef = useRef<AccordionMethod>(null);
  const feesAccordionRef = useRef<AccordionMethod>(null);

  const { store } = useAuthContext();
  const navigation = useNavigation();

  const storeCurrencies = store?.wallets.map(w => w.currency);
  const currencyOptions = CURRENCY_OPTIONS.filter(c => storeCurrencies?.includes(c.value));

  const createOrderRequest = useApi({ apiFunction: CREATE_ORDER, method: 'POST', key: 'create-order' });

  const form = useFormik<CreateOrderFormParams>({
    initialValues: {
      store: store?.id!,
      items: [],
      customer: {
        name: '',
        email: '',
        phone: '',
        store: '',
      },
      delivery_info: {
        delivery_address: '',
        area: '',
        name: '',
        phone: '',
      },
      coupon: '',
      order_origin: '',
      delivery_method: undefined,
      currency: store?.currencies?.products,
      fees: [],
      channel: undefined,
      is_paid: false,

      //custom values
      showOtherSteps: false,
      selectedCustomerId: undefined,
      useSameInfoAsCustomer: false,
      tempSelectedFeeType: '',
      tempFeeAmount: '',
      delivery_phone: {
        code: '+234',
        digits: '',
      },
    },
    validationSchema: createOrderValidationSchema,
    onSubmit: async values => {
      const phone = `${values.delivery_phone.code}-${values.delivery_phone.digits}`;

      const requestData = {
        ...(values.channel && { channel: values.channel }),
        currency: values.currency,
        customer: values.customer,
        ...(values.delivery_method === DELIVERY_METHODS.DELIVERY && {
          delivery_info: { ...values.delivery_info, phone },
        }),
        delivery_method: values.delivery_method,
        fees: values.fees,
        is_paid: values.is_paid,
        items: values.items.map(item => ({
          item_id: item.item?.id,
          quantity: item.quantity,
          ...(item.variant_id && { variant_id: item.variant.id }),
          ...(item.variant && { variant: item.variant }),
          // variant_id: item.variant?.id,
          // variant: item.variant,
        })),
        status: ORDER_STATUSES.PENDING,
        store: values.store,
      };

      const [response, error] = await createOrderRequest.makeRequest(requestData);
      if (response) {
        Toast.show({ type: 'success', text1: response.message });
        setOrderRecorded(true);
      }

      if (error) {
        Toast.show({ type: 'error', text1: error.body.message });
      }
    },
  });

  const currencyRef = useRef<DropDownMethods>(null);
  const orderSourceRef = useRef<DropDownMethods>(null);
  const formatCurrency = useCurrencyConverter(form?.values.currency);

  const handleRecordOrder = () => {
    orderSourceRef?.current?.open();
  };

  const orderResponse = createOrderRequest.response?.data as OrderInterface;

  //TODO: find cleaner way to handle this
  const handleOnPressSave = (step: RECORD_ORDER_STEPS) => {
    switch (step) {
      case RECORD_ORDER_STEPS.ORDER_ITEMS:
        form.setFieldValue('showOtherSteps', true);
        orderItemAccordionRef.current?.toggleAccordion(false);
        setTimeout(() => {
          customerAccordionRef.current?.toggleAccordion(true);
        }, 100);
        break;
      case RECORD_ORDER_STEPS.CUSTOMER_INFORMATION:
        customerAccordionRef.current?.toggleAccordion(false);
        setTimeout(() => {
          deliveryAccordionRef.current?.toggleAccordion(true);
        }, 100);
        break;
      case RECORD_ORDER_STEPS.DELIVERY_INFORMATION:
        deliveryAccordionRef.current?.toggleAccordion(false);
        setTimeout(() => {
          feesAccordionRef.current?.toggleAccordion(true);
        }, 100);
        break;
      case RECORD_ORDER_STEPS.DISCOUNT_AND_FEES:
        feesAccordionRef.current?.toggleAccordion(false);
        setTimeout(() => {
          handleRecordOrder();
        }, 100);
        break;
      default:
        break;
    }
  };

  const handlePressContinue = () => {
    orderSourceRef?.current?.close();
    form.handleSubmit();
  };

  const calculateOrderAmount = () => {
    const cartItems = form.values.items;
    let totalAmount = 0;

    for (let index = 0; index < cartItems.length; index++) {
      const cartItem = cartItems[index];
      const quantity = cartItem.quantity;
      const itemAmount = cartItem.item?.price;
      const totalUnitAmount = Number(itemAmount) * Number(quantity);
      totalAmount += totalUnitAmount;
    }

    return totalAmount;
  };

  const RightElement = () => (
    <CircledIcon className="bg-white p-8">
      <ArrowRight size={wp(15)} currentColor={colors.primary.main} strokeWidth={wp(2)} />
    </CircledIcon>
  );

  const { downloadFile, isLoading } = useFileDownload();

  const handleDownloadReceipt = async (invoiceId: string) => {
    try {
      if (invoiceId === null) return;

      // const subUrl = `orders/export?${paramsFromObject(reqData)}`;
      await delay(600);
      showLoader('Downloading Invoice', true);

      const subUrl = `invoices/pdf/${invoiceId}`;
      const fileName = `invoice-${invoiceId}.pdf`;
      const downloadResult = await downloadFile(subUrl, fileName);

      hideLoader();
    } catch (error) {
      console.log('handleDownloadReceipt: ', error);
      hideLoader();
      Toast.show({ text1: 'Error processing your request', type: 'error' });
    }
  };

  const extraActions = [
    {
      title: 'Share Order',
      leftElement: (
        <CircledIcon className="bg-primary-pastel p-8">
          <TransmitSquare size={wp(15)} color={colors.primary.main} />
        </CircledIcon>
      ),
      rightElement: <RightElement />,
      onPress: () => copyToClipboard(toAppUrl(`orders/${orderResponse.id}`, true, EXPO_PUBLIC_PUBLIC_URL)),
    },
    {
      title: 'Copy Invoice Link',
      leftElement: (
        <CircledIcon className="bg-accentYellow-pastel p-8">
          <Link21 size={wp(15)} color={colors.accentYellow.main} />
        </CircledIcon>
      ),
      rightElement: (
        <CircledIcon className="bg-white p-8">
          <Copy size={wp(15)} color={colors.primary.main} />
        </CircledIcon>
      ),
      onPress: () => copyToClipboard(toAppUrl(`invoices/${orderResponse?.invoice}`, true, EXPO_PUBLIC_PUBLIC_URL)),
    },
    // {
    //   title: 'Send Invoice',
    //   leftElement: (
    //     <CircledIcon className="bg-accentRed-pastel p-8">
    //       <Send2 size={wp(15)} color={colors.accentRed.main} />
    //     </CircledIcon>
    //   ),
    //   rightElement: <RightElement />,
    // },
    {
      title: 'Download Invoice as PDF',
      leftElement: (
        <CircledIcon className="bg-accentOrange-pastel p-8">
          <ReceiveSquare size={wp(15)} color={colors.accentOrange.main} />
        </CircledIcon>
      ),
      rightElement: <RightElement />,
      onPress: () => handleDownloadReceipt(orderResponse?.invoice as string),
    },
  ];

  const isFormValid = (form: { values: unknown }, schema: typeof createOrderValidationSchema): boolean => {
    const { isValid } = testYupValidation<any>(schema, form.values);
    return isValid;
  };

  const disableSubmission = useMemo(() => {
    return !isFormValid(form, createOrderValidationSchema);
  }, [form.values]);

  // WIP: Record Orders
  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: orderRecorded ? 'Order Recorded' : 'Record Order',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      {!orderRecorded && (
        <AvoidKeyboard>
          <ScrollView keyboardDismissMode={'interactive'} keyboardShouldPersistTaps={'handled'}>
            <View className="px-20 py-30 items-center bg-accentRed-pastel">
              <CustomImage
                className="w-[75px] h-[75px] bg-accentRed-pastel"
                imageProps={{ source: require('@/assets/images/cart.png'), contentFit: 'cover' }}
              />
              <BaseText fontSize={22} classes="mt-10 leading-[30px]" type="heading">
                Record an Order
              </BaseText>
              <Row className="mt-5 gap-x-4">
                <View className="py-6 px-10 rounded-full bg-white">
                  <BaseText fontSize={12} weight={'medium'} classes="text-black-muted">
                    Total:{' '}
                    <BaseText fontSize={12} weight="semiBold" classes="text-black-secondary">
                      {/* {toCurrency(calculateOrderAmount(), form.values.currency, true, 0)} */}
                      {formatCurrency(calculateOrderAmount())}
                    </BaseText>
                  </BaseText>
                </View>
                <Pressable
                  className="flex-row items-center py-3 px-10 rounded-full bg-white"
                  onPress={() => currencyRef.current?.open()}>
                  <BaseText fontSize={12} weight="semiBold" classes="text-black-secondary">
                    {CURRENCY_FLAG_MAP[form.values.currency]} {form.values.currency}
                  </BaseText>
                  <ChevronDown size={wp(16)} currentColor={colors.black.muted} />
                </Pressable>
              </Row>
            </View>
            <Container className={'mt-20'}>
              <OrderItemsSection
                form={form}
                accordionRef={orderItemAccordionRef}
                onPressSave={() => handleOnPressSave(RECORD_ORDER_STEPS.ORDER_ITEMS)}
              />
              {form.values?.showOtherSteps && (
                <>
                  <Separator className="mx-0 mb-0" />
                  <CustomerInformationSection
                    form={form}
                    accordionRef={customerAccordionRef}
                    onPressSave={() => handleOnPressSave(RECORD_ORDER_STEPS.CUSTOMER_INFORMATION)}
                  />
                  <Separator className="mx-0 mb-0" />
                  <DeliveryInformationSection
                    form={form}
                    accordionRef={deliveryAccordionRef}
                    onPressSave={() => handleOnPressSave(RECORD_ORDER_STEPS.DELIVERY_INFORMATION)}
                  />
                  <Separator className="mx-0 mb-0" />
                  <DiscountAndFeeSection
                    form={form}
                    accordionRef={feesAccordionRef}
                    onPressSave={() => handleOnPressSave(RECORD_ORDER_STEPS.DISCOUNT_AND_FEES)}
                  />
                </>
              )}
            </Container>
          </ScrollView>
        </AvoidKeyboard>
      )}
      {!orderRecorded && (
        <FixedBtnFooter
          buttons={[
            {
              text: 'Save Order',
              onPress: handleRecordOrder,
              isLoading: createOrderRequest.isLoading,
              disabled: disableSubmission,
            },
          ]}
        />
      )}
      {orderRecorded && (
        <View className="flex-1 justify-center">
          <ScrollView className="flex-1">
            <View className={'items-center justify-center mt-30'}>
              <SuccessCheckmark variant='lg' />
              <BaseText fontSize={22} type={'heading'} classes="text-center mt-10 max-w-[325px]">
                Your order has{'\n'}been Recorded
              </BaseText>
            </View>
            <SectionContainer className="mx-20 mt-30">
              {extraActions?.map((value, index) => (
                <ListItemCard
                  key={value.title}
                  showBorder={index !== extraActions.length - 1}
                  title={value.title}
                  description={undefined}
                  titleProps={{ weight: 'medium', fontSize: 14 }}
                  titleClasses="text-black-secondary"
                  onPress={() => value?.onPress?.()}
                  // disabled={isMultiSelect ? isActive(value.value)! : false}
                  leftElement={value?.leftElement}
                  rightElement={value?.rightElement}
                />
              ))}
            </SectionContainer>
          </ScrollView>
          <FixedBtnFooter
            buttons={[
              {
                text: 'See all Orders',
                onPress: () => navigation.navigate('HomeTab'),
              },
            ]}
          />
        </View>
      )}
      <SelectDropdown
        items={sourceType}
        ref={orderSourceRef}
        showAnchor={false}
        showButton
        closeAfterSelection={false}
        buttons={[{ text: 'Continue', onPress: handlePressContinue, disabled: !Boolean(form.values.channel) }]}
        onPressItem={value => form.setFieldValue('channel', value)}
        selectedItem={form.values.channel}
        label={'Where did this order come from?'}
        containerClasses="my-15"
        showLabel
      />
      <SelectDropdown
        items={currencyOptions}
        ref={currencyRef}
        showAnchor={false}
        onPressItem={value => form.setFieldValue('currency', value)}
        selectedItem={form.values.currency}
        label={'Where did this order come from'}
        containerClasses="my-15"
      />
    </DashboardLayout>
  );
};

const createOrderValidationSchema = Yup.object().shape({
  items: Yup.array().required().min(1, 'Please Select at least one item'),
  customer: Yup.object()
    .shape({
      name: Yup.string().required(),
      email: Yup.string().required(),
      phone: Yup.string().required(),
      store: Yup.string().required(),
    })
    .required('Customer Information is required'),
  // delivery_method: Yup.mixed<DELIVERY_METHODS>().oneOf(Object.values(DELIVERY_METHODS)).required(),
  delivery_method: Yup.string()
    .oneOf(Object.values(DELIVERY_METHODS), 'Invalid delivery method')
    .required('Delivery method is required'),

  delivery_info: Yup.object().when('delivery_method', ([delivery_method], schema) => {
    if (Boolean(delivery_method === DELIVERY_METHODS.DELIVERY)) {
      return Yup.object()
        .shape({
          delivery_address: Yup.string().required('Delivery address is required'),
          area: Yup.string().required('Please select delivery area'),
          name: Yup.string().required('Full name is required'),
          phone: Yup.string(),
        })
        .required();
    }
    return schema.notRequired();
  }),

  coupon: Yup.string(),
  order_origin: Yup.string(),
  delivery_phone: Yup.object()
    .shape({
      code: Yup.string().notRequired(),
      digits: Yup.string()
        .notRequired()
        .test('minlen', 'Phone number must be at least 7 digits', val => !val || val.length >= 7)
        .test('maxlen', 'Phone number must be at most 15 digits', val => !val || val.length <= 15)
        .test('digits', 'Phone number should contain only digits', val => !val || /^\d+$/.test(val)),
    })
    .notRequired(),
});

const sourceType = [
  {
    value: ORDER_CHANNELS.INSTAGRAM,
    label: 'Instagram',
    leftElement: (
      <CircledIcon className="bg-accentOrange-pastel p-8">
        <Instagram size={wp(15)} variant={'Bold'} color={colors.accentOrange.main} />
      </CircledIcon>
    ),
  },
  {
    value: ORDER_CHANNELS.WHATSAPP,
    label: 'Whatsapp',
    leftElement: (
      <CircledIcon className="bg-accentGreen-pastel p-8">
        <Whatsapp size={wp(15)} variant={'Bold'} color={colors.accentGreen.main} />
      </CircledIcon>
    ),
  },
  {
    value: ORDER_CHANNELS.TWITTER,
    label: 'Twitter',
    leftElement: (
      <CircledIcon className="bg-accentYellow-pastel p-8">
        <Twitter size={wp(15)} primaryColor={colors.accentYellow.main} />
      </CircledIcon>
    ),
  },
  {
    value: ORDER_CHANNELS.PHYSICAL_STORE,
    label: 'My Physical Sore',
    leftElement: (
      <CircledIcon className="bg-accentRed-pastel p-8">
        <Shop size={wp(15)} variant={'Bold'} color={colors.accentRed.main} />
      </CircledIcon>
    ),
  },
  {
    value: ORDER_CHANNELS.OTHERS,
    label: 'Others',
    leftElement: (
      <CircledIcon className="bg-accentOrange-pastel p-8">
        <Category2 size={wp(15)} color={colors.accentOrange.main} />
      </CircledIcon>
    ),
  },
];

export default RecordOrder;
