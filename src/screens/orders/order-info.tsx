import {
  ORDER_STATUSES,
  OrderInterface,
  GET_ORDER,
  UPDATE_ORDER,
  GetOrderParms,
  removeCountryCode,
} from 'catlog-shared';
import { useEffect, useMemo, useRef, useState } from 'react';
import { ScrollView, View } from 'react-native';
import Toast from 'react-native-toast-message';
import { FINALIZED_ORDERS } from 'src/assets/utils/js/constants';
import useModals from 'src/hooks/use-modals';

import { alertPromise, hp, toCurrency, wp } from '@/assets/utils/js';
import ContactWidget from '@/components/customer/contact-widget';
import CustomerInformationModal from '@/components/customer/customer-information-modal';
import DeliveryDetailSection from '@/components/orders/order-info/delivery-detail-section';
import InvoiceAndReceiptSection from '@/components/orders/order-info/invoice-and-receipt-section';
import OrderDetailSection from '@/components/orders/order-info/order-detail-section';
import { BaseText, Row, WhiteCardBtn } from '@/components/ui';
import { ButtonVariant, TextColor } from '@/components/ui/buttons/button';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { ArrowUpRight } from '@/components/ui/icons';
import ImageTextPlaceholder from '@/components/ui/image-text-placholder';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import Separator from '@/components/ui/others/separator';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import Shimmer from '@/components/ui/shimmer';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import { ResponseWithoutPagination, useApi } from '@/hooks/use-api';
import useRouteParams from '@/hooks/use-route-params';
import colors from '@/theme/colors';
import Animated, { Easing, useAnimatedStyle, useSharedValue, withDelay, withTiming } from 'react-native-reanimated';

const OrderInfo = () => {
  const params = useRouteParams<'OrderInfo'>();
  const { modals, toggleModal } = useModals(['customerInfo']);
  const [order, setOrder] = useState<OrderInterface | null>(null);

  const orderId = params?.id;

  const changeStatusRequest = useApi({ apiFunction: UPDATE_ORDER, method: 'PUT', key: 'update-order' });
  const getOrderRequest = useApi<GetOrderParms, ResponseWithoutPagination<OrderInterface>>(
    {
      apiFunction: GET_ORDER,
      method: 'GET',
      key: 'get-order',
      onSuccess: response => {
        setOrder(response.data);
      },
    },
    {
      id: orderId!,
    },
  );

  const handleStatusUpdate = async (status: ORDER_STATUSES) => {
    const alertResponse = await alertPromise(
      'Update Status',
      `Clicking "Yes, Update" will update your Order status`,
      'Yes, Update',
      'Cancel',
      true,
    );
    if (alertResponse === false) {
      return;
    }
    const [response] = await changeStatusRequest.makeRequest({
      id: order?.id,
      status: order?.status === ORDER_STATUSES.PENDING ? ORDER_STATUSES.PROCESSING : ORDER_STATUSES.FULFILLED,
    });

    if (response) {
      Toast.show({ type: 'success', text1: 'Order status updated successfully' });
      setOrder({ ...order, status });
    }
  };

  const buttons = [
    {
      text: 'Cancel Order',
      onPress: () => handleStatusUpdate(ORDER_STATUSES.CANCELLED),
      variant: ButtonVariant.LIGHT,
      textColor: TextColor.NEGATIVE,
      isLoading: changeStatusRequest.isLoading,
    },
    {
      text: order?.status === ORDER_STATUSES.PENDING ? 'Confirm Order' : 'Mark as Fulfilled',
      onPress: () =>
        handleStatusUpdate(
          order?.status === ORDER_STATUSES.PENDING ? ORDER_STATUSES.PROCESSING : ORDER_STATUSES.FULFILLED,
        ),
      variant: ButtonVariant.SUCCESS,
      isLoading: changeStatusRequest.isLoading,
    },
  ];

  const actionsCallback = (updatedData: Partial<OrderInterface>) => {
    setOrder(prev => ({ ...prev, ...updatedData }));
  };

  const orderIsFinalized = order?.status ? FINALIZED_ORDERS.includes(order?.status) : true;

  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = () => {
    setIsRetrying(true);
    getOrderRequest.refetch();
    setTimeout(() => setIsRetrying(false), 2000);
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Order Details',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <QueryErrorBoundary
        error={getOrderRequest.error}
        isLoading={getOrderRequest.isLoading}
        refetch={handleRetry}
        isRetrying={isRetrying}
        variant="fullPage"
        errorTitle="Failed to load order details"
        customErrorMessage="We couldn't load the order details. Please check your connection and try again.">
        {getOrderRequest?.isLoading ? (
          <OrderInfoSkeletonLoader />
        ) : (
          <ScrollView contentContainerStyle={{ paddingBottom: hp(20) }}>
            <AnimatedSection index={0}>
              <View className="px-20 py-30 items-center bg-accentYellow-pastel">
                <StatusPill
                  title={order?.status}
                  className="bg-white"
                  statusType={orderStatusPillTypeMap[order?.status]}
                />
                <BaseText fontSize={wp(22)} classes="mt-5 leading-[30px] max-w-[320px] text-center" type="heading">
                  Order {order?.id}
                </BaseText>
              </View>
            </AnimatedSection>

            <AnimatedSection index={1}>
              <Row className="px-20 mt-24 items-start">
                <ImageTextPlaceholder size="md" text={order?.customer?.name} />
                <View className="flex-1 mx-10">
                  <BaseText fontSize={18} type="heading" classes="capitalize">
                    {order?.customer?.name}
                  </BaseText>
                  <BaseText classes="text-black-placeholder mt-2">
                    {order?.customer?.phone ? removeCountryCode(order?.customer?.phone) : '-'}
                  </BaseText>
                  <ContactWidget phone={order?.customer?.phone} />
                </View>
                <WhiteCardBtn
                  className="bg-grey-bgOne rounded-full"
                  onPress={() => toggleModal('customerInfo')}
                  icon={<ArrowUpRight size={wp(14)} strokeWidth={2} currentColor={colors.primary.main} />}>
                  View Profile
                </WhiteCardBtn>
              </Row>
            </AnimatedSection>

            <AnimatedSection index={2}>
              <Separator />
            </AnimatedSection>

            <AnimatedSection index={3}>
              <OrderDetailSection order={order} callBack={actionsCallback} />
            </AnimatedSection>

            <AnimatedSection index={4}>
              <DeliveryDetailSection order={order} {...{ getOrderRequest }} />
            </AnimatedSection>

            <AnimatedSection index={5}>
              <InvoiceAndReceiptSection order={order} />
            </AnimatedSection>

            <AnimatedSection index={6}>
              <View className="h-40" />
            </AnimatedSection>
          </ScrollView>
          //   <ScrollView contentContainerStyle={{ paddingBottom: hp(20) }}>
          //   <View className="px-20 py-30 items-center bg-accentYellow-pastel">
          //     <StatusPill title={order?.status} className="bg-white" statusType={orderStatusPillTypeMap[order?.status]} />
          //     <BaseText fontSize={wp(22)} classes="mt-5 leading-[30px] max-w-[320px] text-center" type="heading">
          //       Order {order?.id}
          //     </BaseText>
          //   </View>
          //   <Row className="px-20 mt-24 items-start">
          //     <ImageTextPlaceholder size="md" text={order?.customer?.name} />
          //     <View className="flex-1 mx-10">
          //       <BaseText fontSize={18} type="heading" classes="capitalize">
          //         {order?.customer?.name}
          //       </BaseText>
          //       <BaseText classes="text-black-placeholder mt-2">
          //         {order?.customer?.phone ? removeCountryCode(order?.customer?.phone) : '-'}
          //       </BaseText>
          //       <ContactWidget phone={order?.customer?.phone} />
          //     </View>
          //     <WhiteCardBtn
          //       className="bg-grey-bgOne rounded-full"
          //       onPress={() => toggleModal('customerInfo')}
          //       icon={<ArrowUpRight size={wp(14)} strokeWidth={2} currentColor={colors.primary.main} />}>
          //       View Profile
          //     </WhiteCardBtn>
          //   </Row>
          //   <Separator />
          //   <OrderDetailSection order={order} callBack={actionsCallback} />
          //   <DeliveryDetailSection order={order} {...{ getOrderRequest }} />
          //   <InvoiceAndReceiptSection order={order} />
          //   <View className="h-40" />
          // </ScrollView>
        )}
        {orderIsFinalized ? null : <FixedBtnFooter buttons={buttons} />}
      </QueryErrorBoundary>
      {order?.customer && (
        <CustomerInformationModal
          isVisible={modals.customerInfo}
          activeCustomer={{ id: order?.customer?.id }}
          closeModal={() => toggleModal('customerInfo', false)}
          showButton={false}
        />
      )}
    </DashboardLayout>
  );
};

export default OrderInfo;

export const OrderInfoSkeletonLoader = () => {
  return (
    <View className="flex-1">
      <View className="py-30 bg-accentYellow-pastel items-center">
        <View className="flex-row items-center h-24 w-[90px] bg-white rounded-40" />
        <View className="flex-row items-center h-30 w-[250px] bg-white rounded-40 mt-10" />
      </View>
      <View className="items-start flex-row px-20 mt-30">
        <Shimmer borderRadius={hp(40)} height={hp(40)} width={hp(40)} />
        <View className="flex-1 ml-10">
          <Shimmer borderRadius={hp(40)} height={hp(20)} width={wp(180)} />
          <Shimmer borderRadius={hp(40)} height={hp(12)} width={wp(120)} marginTop={hp(5)} />
          <Row className="justify-start">
            <Shimmer borderRadius={hp(100)} height={hp(20)} width={hp(80)} marginTop={hp(10)} classes="mr-10" />
          </Row>
        </View>
        <Shimmer borderRadius={hp(40)} height={hp(20)} width={wp(50)} />
      </View>
      <View className="px-20 mt-15">
        <View className="h-1 bg-grey-border my-15" />
        <View className="flex-row justify-between">
          <BaseText type="heading" fontSize={15} className="text-grey-muted">
            Order Details
          </BaseText>

          <Shimmer borderRadius={hp(8)} height={hp(15)} width={wp(30)} />
        </View>
        <View className="mt-10 bg-grey-bgTwo rounded-15" style={{ columnGap: 10 }}>
          <Row className="my-15 px-15">
            <Shimmer {...{ height: hp(40), width: wp(40), borderRadius: wp(10) }} />
            <View className="mx-12 flex-1">
              <Shimmer {...{ height: hp(10), width: wp(150), borderRadius: wp(10) }} classes="mb-5" />
              <Shimmer {...{ height: hp(15), width: wp(80), borderRadius: wp(10) }} />
            </View>
            <Shimmer {...{ height: hp(15), width: wp(50), borderRadius: wp(100) }} />
          </Row>
          <View className="h-1 bg-grey-border my-10" />
          <Row className="my-15 px-15">
            <Shimmer {...{ height: hp(40), width: wp(40), borderRadius: wp(10) }} />
            <View className="mx-12 flex-1">
              <Shimmer {...{ height: hp(10), width: wp(150), borderRadius: wp(10) }} classes="mb-5" />
              <Shimmer {...{ height: hp(15), width: wp(80), borderRadius: wp(10) }} />
            </View>
            <Shimmer {...{ height: hp(15), width: wp(50), borderRadius: wp(100) }} />
          </Row>
          <View className="h-1 bg-grey-border my-10" />
          <Row className="my-15 px-15">
            <Shimmer {...{ height: hp(40), width: wp(40), borderRadius: wp(10) }} />
            <View className="mx-12 flex-1">
              <Shimmer {...{ height: hp(10), width: wp(150), borderRadius: wp(10) }} classes="mb-5" />
              <Shimmer {...{ height: hp(15), width: wp(80), borderRadius: wp(10) }} />
            </View>
            <Shimmer {...{ height: hp(15), width: wp(50), borderRadius: wp(100) }} />
          </Row>
          <View className="h-1 bg-grey-border my-10" />
          <Row className="my-15 px-15">
            <Shimmer {...{ height: hp(40), width: wp(40), borderRadius: wp(10) }} />
            <View className="mx-12 flex-1">
              <Shimmer {...{ height: hp(10), width: wp(150), borderRadius: wp(10) }} classes="mb-5" />
              <Shimmer {...{ height: hp(15), width: wp(80), borderRadius: wp(10) }} />
            </View>
            <Shimmer {...{ height: hp(15), width: wp(50), borderRadius: wp(100) }} />
          </Row>
          <View className="h-1 bg-grey-border my-10" />
          <Row className="my-15 px-15">
            <Shimmer {...{ height: hp(20), width: wp(20), borderRadius: wp(10) }} />
            <View className="mx-12 flex-1">
              <Shimmer {...{ height: hp(10), width: wp(150), borderRadius: wp(10) }} classes="mb-5" />
              <Shimmer {...{ height: hp(15), width: wp(80), borderRadius: wp(10) }} />
            </View>
            <Shimmer {...{ height: hp(15), width: wp(50), borderRadius: wp(100) }} />
          </Row>
        </View>
        <View className="h-1 bg-grey-border my-10" />
      </View>
    </View>
  );
};

export const orderStatusPillTypeMap = {
  [ORDER_STATUSES.PENDING]: StatusType.WARN,
  [ORDER_STATUSES.PROCESSING]: StatusType.PROCESSING,
  [ORDER_STATUSES.FULFILLED]: StatusType.SUCCESS,
  [ORDER_STATUSES.CANCELLED]: StatusType.DANGER,
  [ORDER_STATUSES.ABANDONED]: StatusType.DANGER,
};

const AnimatedSection = ({
  children,
  index,
  delay = 100,
}: {
  children: React.ReactNode;
  index: number;
  delay?: number;
}) => {
  const opacity = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: withDelay(
        index * delay,
        // delay,
        withTiming(opacity.value, {
          duration: 200,
          easing: Easing.ease,
        }),
      ),
      transform: [
        {
          translateY: withDelay(
            index * delay,
            withTiming(opacity.value ? 0 : 10, {
              duration: 100,
              easing: Easing.ease,
            }),
          ),
        },
      ],
    };
  });

  useEffect(() => {
    opacity.value = 1;
  }, []);

  return <Animated.View style={animatedStyle}>{children}</Animated.View>;
};
