import { useNavigation } from '@react-navigation/native';
import { GET_INVOICE_STATS, GET_INVOICES, GetInvoicesParams, INVOICE_STATUSES, InvoiceInterface } from 'catlog-shared';
import React, { useEffect, useState } from 'react';
import { ensureUniqueItems } from 'src/assets/utils/js';
import { useApi } from 'src/hooks/use-api';

import InvoiceInfoModal from '@/components/invoices/invoice-info-modal';
import FAB from '@/components/ui/buttons/fab';
import SearchModal from '@/components/ui/modals/search-modal';
import usePagination from 'src/hooks/use-pagination';

import useInvoiceActions from 'src/hooks/use-invoice-actions';

import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import InvoiceAnalyticsModal from 'src/components/invoices/invoice-analytics-modal';
import InvoiceList from 'src/components/invoices/invoice-list';
import ScreenActionBtn from 'src/components/ui/buttons/screen-action-btn';
import SearchContainer, { SEARCH_BG_VARIANT } from 'src/components/ui/others/search-container';
import TopTabs from 'src/components/ui/others/top-tabs';
import useModals from 'src/hooks/use-modals';
import useScrollHandler from 'src/hooks/use-scroll-handler';
import { SCOPES } from 'src/assets/utils/js/permissions';
import Can from 'src/components/ui/can';

enum InvoiceAnalyticsType {
  DRAFT_INVOICES = 'Draft',
  PENDING_INVOICES = 'Pending',
  PAID_ORDERS = 'Paid',
  ALL_INVOICES = 'All Invoices',
}

export interface InvoiceStatsResponse {
  data: {
    currencies: string[];
    grouped_data: {
      [key: string]: {
        total_invoices: number;
        total_amount_received: number;
        total_paid_invoices: number;
        invoices_unpaid: number;
      };
    };
  };
}
export interface InvoicesResponse {
  data: {
    invoices: InvoiceInterface[];
  };
}
interface Params {
  navigateToCreate: boolean;
}
function Invoices({ navigateToCreate }: Params) {
  const [invoices, setInvoices] = useState<InvoiceInterface[]>([]);
  // const [currentInvoice, setCurrentInvoice] = useState<InvoiceInterface>();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [tabIndex, setTabIndex] = useState(0);
  // const { modals, toggleModal, switchModals } = useModals(['info', 'search', 'options', 'preview', 'send']);

  const { currentPage, goNext, setPage } = usePagination();
  const navigation = useNavigation();
  const { modals: m, toggleModal: tm } = useModals(['analyticsModal']);

  const { openInvoice, handleInvoiceItemAction, currentInvoice, modals, toggleModal } = useInvoiceActions(
    invoices,
    setInvoices,
  );

  useEffect(() => {
    if (navigateToCreate) {
      navigation.navigate('CreateInvoice');
    }
  }, [navigateToCreate]);

  const getInvoiceStatsReq = useApi<any, InvoiceStatsResponse>({
    apiFunction: GET_INVOICE_STATS,
    key: GET_INVOICE_STATS.name,
    method: 'GET',
  });

  const getInvoicesReq = useApi<GetInvoicesParams, InvoicesResponse>(
    {
      apiFunction: GET_INVOICES,
      key: GET_INVOICES.name,
      method: 'GET',

      onSuccess: response => {
        if (searchTerm !== '') {
          setInvoices(response?.data?.invoices);
        } else setInvoices(prev => ensureUniqueItems([...prev!, ...response?.data?.invoices]));
      },
    },
    {
      filter: {
        search: searchTerm,
      },
      page: currentPage,
      per_page: 10,
    },
  );

  const handlePullToRefresh = () => {
    try {
      setInvoices([]);
      setPage(0);
    } catch (error) {
      console.log(error);
    }
  };

  const handleSearch = async (text: string) => {
    setInvoices([]);
    setSearchTerm(text);
    getInvoicesReq.reset();
  };

  const isSearch = searchTerm !== '';
  const { scrollHandler, headerStyle, inputStyle } = useScrollHandler(100);

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentGreen-pastel2',
        pageTitle: 'Invoices',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <SearchContainer
        {...{ headerStyle, inputStyle }}
        color={SEARCH_BG_VARIANT.GREEN}
        onPressSearch={() => navigation.navigate('SearchInvoices')}
        placeholder="Search Invoices"
      />
      <Can data={{ permission: SCOPES.WALLETS.CAN_MANAGE_WALLET }}>
        <ScreenActionBtn
          title={'See Analytics'}
          className="bg-accentGreen-pastel3"
          onPress={() => tm('analyticsModal')}
        />
      </Can>

      <TopTabs
        setIndex={setTabIndex}
        currentIndex={tabIndex}
        tabItems={[
          {
            key: InvoiceAnalyticsType.ALL_INVOICES,
            title: InvoiceAnalyticsType.ALL_INVOICES,
            component: <InvoiceList invoiceStatus={INVOICE_STATUSES.PAID} scrollHandler={scrollHandler} />,
            func: () => {},
          },
        ]}
      />
      {/* <Animated.FlatList
        className="flex-1 px-20 pb-40"
        contentContainerStyle={{ flexGrow: 1 }}
        data={invoices.length > 0 ? [null, ...invoices] : []}
        stickyHeaderIndices={[1]}
        keyExtractor={(item, index) => item?.id ?? 'null' + index}
        onEndReachedThreshold={0.3}
        onEndReached={() => {
          if (!getInvoicesReq?.isLoading && !isSearch) {
            goNext();
          }
        }}
        ListHeaderComponent={
          <InvoiceAnalyticsCards data={getInvoiceStatsReq.response?.data} isLoading={getInvoiceStatsReq.isLoading} />
        }
        refreshControl={<RefreshControl refreshing={getInvoicesReq?.isReLoading} onRefresh={handlePullToRefresh} />}
        ListEmptyComponent={() =>
          getInvoicesReq?.isLoading ? (
            <View key="skeleton" className="mt-20">
              <InvoicesSkeletonLoader />
            </View>
          ) : (
            <>
              {isSearch && (
                <Column classes="bg-white py-10">
                  <Row className="">
                    <BaseText type="heading" fontSize={15}>
                      All Invoices
                    </BaseText>
                    <Pressable onPress={() => toggleModal('search', true)}>
                      <CircledIcon iconBg="bg-grey-bgOne">
                        <SearchNormal1 variant="Linear" color={colors.black.muted} size={wp(18)} />
                      </CircledIcon>
                    </Pressable>
                  </Row>
                </Column>
              )}
              <ClearSearch classes="" searchValue={searchTerm} setSearchValue={handleSearch} />
              <EmptyState
                icon={
                  <CircledIcon
                    className="mb-20 p-15 bg-white"
                    style={{
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 10,
                      },
                      shadowOpacity: 0.05,
                      shadowRadius: 6.84,

                      elevation: 5,
                    }}>
                    <Receipt2 variant="Bulk" size={wp(40)} color={colors.grey.muted} />
                  </CircledIcon>
                }
                onPressBtn={() => navigation.navigate('CreateInvoice')}
                btnText="Create Invoice"
                text={isSearch ? 'No results found' : 'No invoices created yet'}
              />
            </>
          )
        }
        renderItem={({ item, index }) =>
          item === null ? (
            <Column key={index} classes="bg-white py-20">
              <Row className="">
                <BaseText type="heading" fontSize={15}>
                  All Invoices
                </BaseText>
                <Pressable onPress={() => toggleModal('search', true)}>
                  <CircledIcon iconBg="bg-grey-bgOne">
                    <SearchNormal1 variant="Linear" color={colors.black.muted} size={wp(18)} />
                  </CircledIcon>
                </Pressable>
              </Row>
              <ClearSearch classes="mt-10" searchValue={searchTerm} setSearchValue={handleSearch} />
            </Column> //Todo: @kayode this should be a component, we've used it in two places
          ) : (
            <InvoiceCard
              onAction={action => handleInvoiceItemAction(action, item)}
              onPress={() => openInvoice(item)}
              index={index}
              item={item}
              key={index}
            />
          )
        }
        ListFooterComponent={
          <View style={{ marginBottom: 80 }}>
            {getInvoicesReq?.isLoading && (
              <View className="mt-5">
                <InvoicesSkeletonLoader />
              </View>
            )}
          </View>
        }
      /> */}

      <FAB onPress={() => navigation.navigate('CreateInvoice')} />
      <SearchModal
        onSearch={handleSearch}
        placeholder="Search invoices"
        show={modals.search}
        toggle={() => toggleModal('search', false)}
      />
      {currentInvoice && (
        <InvoiceInfoModal
          copyInvoiceLink={() => handleInvoiceItemAction('copy')}
          editInvoice={() => handleInvoiceItemAction('edit')}
          openOptions={() => handleInvoiceItemAction('options')}
          activeInvoice={currentInvoice}
          isVisible={modals.info}
          {...{ modals, toggleModal, handleInvoiceItemAction }}
          closeModal={() => toggleModal('info', false)}
        />
      )}
      <InvoiceAnalyticsModal
        isVisible={m.analyticsModal}
        stats={getInvoiceStatsReq.response?.data}
        isLoading={getInvoiceStatsReq.isLoading}
        closeModal={() => tm('analyticsModal', false)}
      />
    </DashboardLayout>
  );
}

export default Invoices;
