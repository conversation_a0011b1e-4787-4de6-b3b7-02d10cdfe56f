import React, { useMemo, useState } from 'react';
import { ScrollView, View, Image, ActivityIndicator } from 'react-native';
import PhoneNumberInput from '@/components/ui/inputs/phone-number-input';
import { BaseText, CircledIcon } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import Button from '@/components/ui/buttons/button';
import CheckBox from '@/components/ui/buttons/check-box';
import Container from '@/components/ui/container';
import { CloudUpload, GhanaFlag, NigeriaFlag } from '@/components/ui/icons';
import SelectDropdown, { DropDownItem } from '@/components/ui/inputs/select-dropdown';
import Input from '@/components/ui/inputs/input';
import Row from '@/components/ui/row';
import ScreenInfoHeader from '@/components/ui/screen-info-header';
import useStatusbar from '@/hooks/use-statusbar';
import UploadImageBtn from '@/components/ui/buttons/upload-image-btn';
import { useNavigation } from '@react-navigation/native';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import useAuthStore from '@/contexts/auth/store';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { phoneValidation } from '@/assets/utils/js/common-validations';
import { useApi } from '@/hooks/use-api';
import { delay, getFieldvalues } from '@/assets/utils/js';
import useAuthContext from '@/contexts/auth/auth-context';
import ScreenModal from '@/components/ui/modals/screen-modal';
import { CREATE_STORE, CreateStoreParams, GET_COUNTRIES, PhoneInterface, phoneObjectFromString } from 'catlog-shared';
import AvoidKeyboard from 'src/components/ui/layouts/avoid-keyboard';
import useGuestContext from 'src/contexts/auth/guest-context';
import { SUPPORTED_COUNTRIES } from '../auth/sign-up-v2';

interface CreateStoreFormParams extends Omit<CreateStoreParams, 'phone'> {
  phone: PhoneInterface;
}

const CreateStore = () => {
  const { user, updateUser, getNewToken, stores, switchStore, isSwitchingStore } = useAuthContext();
  const { visitorCountry } = useGuestContext();
  const navigation = useNavigation();
  const { setStatusBar } = useStatusbar();
  setStatusBar('dark', 'transparent', true);

  const [useSamePhone, setUseSamePhone] = useState(false);
  const [selectedItem, setSelectedItem] = useState('');
  const [errorText, setErrorText] = useState(null);

  const firstStore = user?.stores.filter(s => s.owner === user?.id)?.[0];

  const createStoreReq = useApi({ key: 'create-store', apiFunction: CREATE_STORE, method: 'POST' });
  const supportedCountriesReq = useApi({ key: 'supported-countries', apiFunction: GET_COUNTRIES, method: 'GET' });
  const supportedCountries: { name: string; emoji: string; code: string }[] =
    supportedCountriesReq?.response?.data ?? [];

  const countryOptions = useMemo(
    () => supportedCountries.map(c => ({ label: `${c.name} ${c.emoji}`, value: c.code })),
    [supportedCountries],
  );

  const prefilledCountry = useMemo(() => {
    if (visitorCountry && visitorCountry.code && SUPPORTED_COUNTRIES.includes(visitorCountry.code)) {
      return visitorCountry.code;
    }

    return SUPPORTED_COUNTRIES[0];
  }, [visitorCountry]);

  const form = useFormik<CreateStoreFormParams>({
    initialValues: {
      name: '',
      phone: phoneObjectFromString(user?.phone),
      country: prefilledCountry,
      description: '',
      store_type: STORE_TYPES.REGULAR,
      logo: '',
      copy_config: !!firstStore,
    },
    validationSchema,
    onSubmit: async values => {
      // if (values.store_type === STORE_TYPES.RESTAURANT) {
      //   toggleModal('confirm_chowbot_selection');
      //   return;
      // }
      createStore();
    },
  });

  const createStore = async () => {
    const values = form.values;
    const phone = `${values.phone.code}-${values.phone.digits}`;
    values.store_type = values.country === 'NG' ? values.store_type : STORE_TYPES.REGULAR;
    const [response, error] = await createStoreReq.makeRequest({ ...values, phone });

    if (error) {
      if (error.fields && Object.keys(error.fields).length > 0) {
        form.setErrors({ ...error.fields });
      } else {
        setErrorText(error.message);
      }
    } else {
      updateUser(response.data);

      const storesData = response.data.stores;
      if (hasMultipleStores) {
        await delay(500);
        switchStore(storesData[storesData?.length - 1].id, true);
        return;
      }

      getNewToken(response.data.stores[0].id);
      navigation.navigate('SetupBusiness');

      // if (stores.length > 0) {
      //   const stores = response.data.stores;
      //   switchStore(stores?.[stores?.length - 1].id);
      //   router.push("/setup/add-products");
      //   return;
      // }
      // getNewToken(response.data.stores[0].id);
      // router.push("/setup/add-products");
    }
  };

  const handleUseSamePhoneToggle = () => {
    const nextState = !useSamePhone;

    if (nextState) {
      let phone = user?.phone.split('-') ?? '';

      form.setFieldValue('phone', { code: phone[0], digits: phone[1] });
    } else {
      form.setFieldValue('phone', { code: '+234', digits: '' });
    }

    setUseSamePhone(nextState);
  };

  const hasMultipleStores = stores?.length ?? 0 > 1;

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-primary-pastel',
        pageTitle: 'Create Store',
        variant: hasMultipleStores ? HeaderVariants.SUB_LEVEL : HeaderVariants.ROOT_LEVEL,
        showNotifications: false,
      }}>
      <AvoidKeyboard>
        <ScrollView>
          <ScreenInfoHeader
            iconElement={
              <Image
                source={require('@/assets/images/storefront.png')}
                resizeMode={'contain'}
                className="w-[80px] h-[60px]"
              />
            }
            pageTitleTop={`Welcome ${user?.name.split(' ')[0]},`}
            pageTitleBottom={'Create your Store'}
          />
          <Container className="pt-24">
            <Input label="Business Name" {...getFieldvalues('name', form)} />
            <SelectDropdown
              items={countryOptions}
              label={'Where is this business based?'}
              onPressItem={value => form.setFieldValue('country', value)}
              selectedItem={form.values.country}
              containerClasses="mt-15"
            />
            <PhoneNumberInput
              containerClasses="mt-15"
              {...getFieldvalues('phone', form)}
              onChange={value => form.setFieldValue('phone', value)}
              label="Business Phone"
            />
            {/* <Pressable className="mt-10" onPress={handleUseSamePhoneToggle}>
                <Row classes="justify-start">
                  <CheckBox checked={useSamePhone} />
                  <BaseText classes="text-black-placeholder ml-10">Same as Whatsapp</BaseText>
                </Row>
              </Pressable> */}
            {/* <SelectDropdown
                items={storeTypeOptions}
                label={'How would you like to use Catlog?'}
                onPressItem={value => form.setFieldValue('store_type', value)}
                selectedItem={form.values.store_type}
                containerClasses="mt-15"
              /> */}
            <Input
              label={'Describe your business - What you sell'}
              multiline
              className="h-[80]"
              containerClasses="mt-15"
              {...getFieldvalues('description', form)}
            />
            <UploadImageBtn setTempImage={() => null} setImageUrl={url => form.setFieldValue('logo', url)} />
            {firstStore && (
              <Pressable onPress={() => form.setFieldValue('copy_config', !form.values.copy_config)} className='mt-15'>
                <Row classes="justify-start">
                  <CheckBox
                    checked={form.values.copy_config}
                    
                  />
                  <BaseText classes="text-black-placeholder ml-10">Copy configurations from {firstStore?.name}</BaseText>
                </Row>
              </Pressable>
            )}
          </Container>
        </ScrollView>
      </AvoidKeyboard>
      <FixedBtnFooter
        buttons={[
          {
            text: createStoreReq?.isLoading ? 'Creating Business...' : 'Create Business',
            disabled: createStoreReq?.isLoading,
            onPress: () => form.handleSubmit(),
          },
        ]}
      />
      <ScreenModal show={isSwitchingStore} toggleModal={() => null}>
        <View className="min-h-[25%] w-full rounded-xl bg-transparent justify-center items-center">
          <ActivityIndicator className="text-white" size="small" animating />
          <BaseText classes="mt-10 text-white">{'Switching Store...'}</BaseText>
        </View>
      </ScreenModal>
    </DashboardLayout>
  );
};

enum STORE_TYPES {
  REGULAR = 'REGULAR',
  RESTAURANT = 'RESTAURANT',
}

const storeTypeOptions: DropDownItem[] = [
  {
    label: 'Regular - Storefronts & Business Management',
    value: STORE_TYPES.REGULAR,
  },
  {
    label: 'Chowbot + Everything in Regular (Restaurant)',
    value: STORE_TYPES.RESTAURANT,
  },
];

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Store name is required'),
  phone: phoneValidation(),
  description: Yup.string()
    .required('Store description is required')
    .max(150, 'Description cannot be more than 150 characters'),
  store_type: Yup.string().required("Please select how you'll like to use Catlog"),
});

export default CreateStore;
