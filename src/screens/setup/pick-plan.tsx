import { delay, showLoader, wp } from '@/assets/utils/js';
import PlanCard from '@/components/select-plan/plan-card';
import PlanFeatureModal from '@/components/select-plan/plan-feature-modal';
import { BaseText, CircledIcon } from '@/components/ui';
import Container from '@/components/ui/container';
import Row from '@/components/ui/row';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import colors from '@/theme/colors';
import { CountryInterface, GET_PLANS, GetPlansParams, PLAN_TYPE, Plan, PlanOption } from 'catlog-shared';
import { Gift } from 'iconsax-react-native/src';
import React, { useMemo, useState } from 'react';
import { Image, ScrollView, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import PickPlanModal from 'src/components/select-plan/pick-plan-modal';
import SelectPlanDurationModal from 'src/components/select-plan/select-plan-duration-modal';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import DashboardLayout from 'src/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from 'src/components/ui/layouts/header';
import useAuthContext from 'src/contexts/auth/auth-context';
import { ApiData, useApi } from 'src/hooks/use-api';
import useModals from 'src/hooks/use-modals';
import Pressable from 'src/components/ui/base/pressable';
import { ArrowUpRight } from 'src/components/ui/icons';
import SubscriptionBenefitModal from 'src/components/select-plan/subscription-benefit-modal';

const PickPlan = () => {
  const { store } = useAuthContext();
  const getPlansReq = useApi(
    {
      apiFunction: GET_PLANS,
      key: GET_PLANS.name,
      method: 'GET',
      autoRequest: true,
    },
    {
      country: typeof store?.country === 'string' ? store?.country : store?.country?.code,
    },
  );

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: 'Select Preference',
        variant: HeaderVariants.SUB_LEVEL,
        showNotifications: false,
        showBackButton: false,
      }}
      isLoading={getPlansReq.isLoading}>
      <View className="flex-1">
        {/* <ScreenInfoHeader
          iconElement={
            <Image
              source={require('@/assets/images/giftBox.png')}
              resizeMode={'contain'}
              className="w-[80px] h-[60px]"
            />
          }
          colorPalette={ColorPaletteType.RED}
          pageTitleTop={'Final Step'}
          pageTitleBottom={'Select a Plan'}
        /> */}
        <PickPlanMain getPlansReq={getPlansReq} />
      </View>
    </DashboardLayout>
  );
};

export default PickPlan;

export interface GroupedPlan {
  options: { [key: string]: PlanOption };
  name: string;
  description: { title: string; features: string[] }[];
  interval: number;
  type: PLAN_TYPE;
  id: string;
  interval_text: string;
  country: CountryInterface;
  is_paid_plan?: boolean;
  amount: number;
}

function groupPlans(plans: Plan[]) {
  const thePlans: Plan[] = [];

  if (!plans) {
    return [];
  }

  plans.forEach(plan => {
    if (plan.type === PLAN_TYPE.KITCHEN || plan.type === PLAN_TYPE.STARTER) return;

    const planDuration = (() => {
      switch (plan.interval) {
        case 30:
          return 'MONTHLY';
        case 90:
          return 'QUARTERLY';
        case 180:
          return 'BI-ANNUALLY';
        default:
          return 'YEARLY';
      }
    })();

    const planIndex = thePlans.findIndex(p => p.type === plan.type);
    const planExists = planIndex !== -1;

    //add plan to plan options if plan already exists
    if (planExists) {
      thePlans[planIndex].options[planDuration] = {
        id: plan.plan_option_id,
        interval: plan.interval,
        interval_text: plan.interval_text,
        amount: plan.amount,
        discount: plan?.discount,
        plan_id: plan.id,
        actual_amount: plan?.actual_amount,
      } as PlanOption;

      return;
    }

    //add fresh plan if plan doesnt exist
    thePlans.push({
      ...plan,
      options: {
        [planDuration]: {
          id: plan.plan_option_id,
          interval: plan.interval,
          interval_text: plan.interval_text,
          amount: plan.amount,
          discount: plan?.discount,
          plan_id: plan.id,
          actual_amount: plan?.actual_amount,
        } as PlanOption,
      },
    });
  });

  return thePlans;
}
interface Props {
  getPlansReq: ApiData<GetPlansParams, any>;
  isSetup?: boolean;
}

const PickPlanMain: React.FC<Props> = ({ getPlansReq, isSetup = true }) => {
  const { modals, toggleModal, switchModals } = useModals(['features', 'pick_plan', 'subscriptionBenefit']);
  const [selectedPlan, setSelectedPlan] = useState<GroupedPlan>(null);
  const { refetchSession } = useAuthContext();
  const navigation = useNavigation();

  const plans: Plan[] = useMemo(() => {
    if (getPlansReq.response) {
      const thePlans = getPlansReq.response?.data?.plans ?? [];
      thePlans.sort((a, b) => (a?.amount < b?.amount ? -1 : 1));
      return thePlans;
    }
    return [];
  }, [getPlansReq.response]);

  const groupedPlans = useMemo(() => {
    return groupPlans(plans) as GroupedPlan[];
  }, [plans]);

  const showFeatures = (plan: GroupedPlan) => {
    setSelectedPlan(plan);
    toggleModal('features');
  };

  const handlePaymentSuccess = async () => {
    toggleModal('pick_plan', false);
    await delay(1000);
    showLoader('Updating store...', false, true);
    await refetchSession();
    navigation.goBack();
  };

  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        {isSetup && (
          <ScreenInfoHeader
            iconElement={
              <Image
                source={require('@/assets/images/giftBox.png')}
                resizeMode={'contain'}
                className="w-[80px] h-[60px]"
              />
            }
            colorPalette={ColorPaletteType.RED}
            pageTitleTop={'Final Step'}
            pageTitleBottom={'Select a Plan'}
          />
        )}
        <Container className="pt-24 pb-24">
          {
            <Row classes="items-start p-12 bg-grey-bgOne rounded-[10px]">
              {isSetup && (
                <CircledIcon iconBg="bg-white">
                  <Gift variant={'Bold'} color={colors.accentRed.main} size={wp(16)} />
                </CircledIcon>
              )}
              <View className="flex-1 mx-10">
                <BaseText classes="text-black-secondary" fontSize={12}>
                  {isSetup
                    ? 'All plans are free for 7 days, NO CARD REQUIRED!'
                    : 'Your current active plan will be used to discount the subscription amount when changing plans.'}
                </BaseText>
                {isSetup && (
                  <Pressable onPress={() => toggleModal('subscriptionBenefit', true)}>
                    <Row classes="justify-start mt-2">
                      <BaseText fontSize={12} weight={'medium'} classes={'text-primary-main mr-4'}>
                      What your subscription gives you
                      </BaseText>
                      <ArrowUpRight size={wp(15)} strokeWidth={1.5} currentColor={colors.primary.main} />
                    </Row>
                  </Pressable>
                )}
              </View>
            </Row>
          }
          <View className="mt-15">
            {groupedPlans.map((plan, idx) => (
              <PlanCard
                key={plan?.name!}
                onPress={() => setSelectedPlan(plan)}
                onPressPlansFeatures={() => showFeatures(plan)}
                selected={selectedPlan?.name === plan?.name}
                greatChoice={isSetup ? idx == 0 : false}
                className="mt-15"
                plan={plan}
              />
            ))}
          </View>
        </Container>

        {selectedPlan && (
          <>
            <PlanFeatureModal
              planName={selectedPlan.name}
              features={[...selectedPlan.description[0].features]}
              isVisible={modals.features}
              closeModal={() => toggleModal('features', false)}
            />
            <PickPlanModal
              isSetup={isSetup}
              plans={plans}
              isVisible={modals.pick_plan}
              closeModal={() => toggleModal('pick_plan', false)}
              plan={selectedPlan}
              onComplete={handlePaymentSuccess}
            />
          </>
        )}
        <SubscriptionBenefitModal
          isVisible={modals.subscriptionBenefit}
          closeModal={() => toggleModal('subscriptionBenefit', false)}
        />
      </ScrollView>
      <FixedBtnFooter
        buttons={[
          {
            text: false ? '..' : 'Pick Plan',
            disabled: !Boolean(selectedPlan),
            onPress: () => toggleModal('pick_plan'),
          },
        ]}
      />
    </View>
  );
};
export { PickPlanMain };
