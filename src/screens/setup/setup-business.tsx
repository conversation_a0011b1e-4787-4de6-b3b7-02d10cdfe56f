import { delay, getFieldvalues, removeEmptyAndUndefined, showSuccess } from '@/assets/utils/js';
import { phoneValidation } from '@/assets/utils/js/common-validations';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import UploadImageBtn from '@/components/ui/buttons/upload-image-btn';
import Container from '@/components/ui/container';
import Input from '@/components/ui/inputs/input';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import ScreenInfoHeader from '@/components/ui/screen-info-header';
import useAuthContext from '@/contexts/auth/auth-context';
import useStatusbar from '@/hooks/use-statusbar';
import { useNavigation } from '@react-navigation/native';
import {
  CreateStoreParams,
  GET_BUSINESS_CATEGORIES,
  PhoneInterface,
  StoreAdditionalDetails,
  UPDATE_STORE_ADDITIONAL_DETAILS,
  capitalizeFirstLetter,
  extractUsername,
} from 'catlog-shared';
import { useFormik } from 'formik';
import { Facebook, Instagram, Snapchat } from 'iconsax-react-native/src';
import React, { useMemo } from 'react';
import { Image, ScrollView, View } from 'react-native';
import { BaseText } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import { Twitter } from 'src/components/ui/icons';
import SelectDropdown, { DropDownItem } from 'src/components/ui/inputs/select-dropdown';
import AvoidKeyboard from 'src/components/ui/layouts/avoid-keyboard';
import { useApi } from 'src/hooks/use-api';
import colors from 'src/theme/colors';
import * as Yup from 'yup';

const SetupBusiness = () => {
  const { user, updateStore, getToken, store, stores, switchStore, isSwitchingStore } = useAuthContext();
  const navigation = useNavigation();
  const { setStatusBar } = useStatusbar();
  setStatusBar('dark', 'transparent', true);

  const getBusinessCategoriesReq = useApi({
    apiFunction: GET_BUSINESS_CATEGORIES,
    key: GET_BUSINESS_CATEGORIES.name,
    method: 'GET',
  });

  const updateStoreAddDetailsReq = useApi({
    apiFunction: UPDATE_STORE_ADDITIONAL_DETAILS,
    key: UPDATE_STORE_ADDITIONAL_DETAILS.name,
    method: 'PUT',
  });

  const businessCategories: string[] = getBusinessCategoriesReq?.response?.data ?? [];

  const handleSelectBusinessCategory = (value: string) => {
    form.setFieldValue('business_category', value);
  };

  const categoriesOptions = useMemo<DropDownItem[]>(() => {
    return businessCategories.map(k => ({ label: k, value: k }));
  }, [businessCategories]);

  const form = useFormik<StoreAdditionalDetails>({
    initialValues: {
      business_category: '',
      social_media_platform: 'instagram',
      social_media_username: '',
      logo: store?.logo ?? '',
      description: store?.description ?? '',
      monthly_orders: '',
      business_type: '',
    },
    validationSchema,
    onSubmit: async values => {
      const [response, error] = await updateStoreAddDetailsReq.makeRequest({
        additional_details: { ...values },
        id: store.id,
      });
      if (error) {
        if (error.fields && Object.keys(error.fields).length > 0) {
          form.setErrors(error.fields);
        }
      } else {
        updateStore({ ...response.data });
        showSuccess('Successfully updated store');
        await delay(2000);
        navigation.navigate('PickPlan');
      }
    },
  });

  const handleUserNameInputs = (text: string) => {
    const cleanedUsername = extractUsername(text, error => form.setFieldError('social_media_username', error));
    form.setFieldValue('social_media_username', cleanedUsername);
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-primary-pastel',
        pageTitle: 'Setup Business',
        variant: HeaderVariants.ROOT_LEVEL,
        showNotifications: false,
      }}>
      <AvoidKeyboard>
        <ScrollView>
          <ScreenInfoHeader
            iconElement={
              <Image
                source={require('@/assets/images/storefront.png')}
                resizeMode={'contain'}
                className="w-[80px] h-[60px]"
              />
            }
            pageTitleTop={`Tell us more,`}
            pageTitleBottom={'About your business'}
          />
          <Container className="pt-24">
            <SelectDropdown
              label="What does your business do?"
              items={[
                { value: BUSINESS_TYPES.PHYSICAL, label: 'I sell physical products (e.g. food, clothes)' },
                { value: BUSINESS_TYPES.DIGITAL, label: 'I sell digital products (e.g. ebooks, courses)' },
                { value: BUSINESS_TYPES.PROPERTIES, label: 'I sell properties (e.g. houses, cars)' },
                { value: BUSINESS_TYPES.SERVICE, label: "I'm service-based (e.g. graphic design, hair salon)" },
                { value: BUSINESS_TYPES.OTHERS, label: 'Others' },
              ]}
              {...getFieldvalues('business_type', form, 'select')}
            />
            <View className="mt-15">
              <SelectDropdown
                label="Select business category"
                items={categoriesOptions}
                {...getFieldvalues('business_category', form, 'select')}
              />
            </View>
            <View className="pt-15">
              <BaseText classes="text-black-main" fontSize={15} type="heading" weight="medium">
                What’s your business social media account?
              </BaseText>
              <View className="flex flex-row gap-x-10 mt-15">
                {socialOptions.map((item, index) => {
                  const color = item.value == form?.values?.social_media_platform ? item.color : '#a5a5a5';
                  return (
                    <Pressable
                      onPress={() => form.setFieldValue('social_media_platform', item.value)}
                      key={index}
                      className="flex flex-row gap-x-5 items-center">
                      <View
                        style={{ backgroundColor: color + '10', borderColor: color + '15' }}
                        className="p-10 border bg-opacity-5 rounded-10">
                        {item.icon(color)}
                      </View>
                    </Pressable>
                  );
                })}
              </View>
              <Input
                label={`Your ${form?.values?.social_media_platform} username`}
                containerClasses="mt-15"
                {...getFieldvalues('social_media_username', form)}
                onChangeText={handleUserNameInputs}
              />
            </View>
            {!store?.description && (
              <Input
                label={'Describe your business - What you sell'}
                multiline
                className="h-[80]"
                containerClasses="mt-15"
                {...getFieldvalues('description', form)}
              />
            )}
            {!store?.logo && (
              <UploadImageBtn setTempImage={() => null} setImageUrl={url => form.setFieldValue('logo', url)} />
            )}
          </Container>
        </ScrollView>
      </AvoidKeyboard>
      <FixedBtnFooter
        buttons={[
          {
            text: updateStoreAddDetailsReq.isLoading ? 'Updating Store...' : 'Continue',
            disabled: updateStoreAddDetailsReq.isLoading,
            onPress: () => form.handleSubmit(),
          },
        ]}
      />
    </DashboardLayout>
  );
};

const socialOptions = [
  {
    value: 'instagram',
    icon: (color: string) => <Instagram variant="Bold" color={color} size={25} />,
    color: '#9B4F96',
  },
  {
    value: 'twitter',
    icon: (color: string) => <Twitter primaryColor={color} size={25} />,
    color: '#1DA1F2',
  },
  {
    value: 'snapchat',
    icon: (color: string) => <Snapchat variant="Bold" color={color} size={25} />,
    color: '#FFCC00',
  },
  {
    value: 'facebook',
    icon: (color: string) => <Facebook variant="Bold" color={color} size={25} />,
    color: '#1877F2',
  },
];

const validationSchema = Yup.object().shape({
  logo: Yup.string().optional(),
  description: Yup.string()
    .required('Store description is required')
    .max(150, 'Description cannot be more than 150 characters'),
  business_type: Yup.string().required('Please select your business type'),
  business_category: Yup.string().required('Please select a business category'),
  social_media_platform: Yup.string().required('Please select a social media platform'),
  social_media_username: Yup.string().required('Please enter your social media username'),
});

enum BUSINESS_TYPES {
  PHYSICAL = 'PHYSICAL',
  DIGITAL = 'DIGITAL',
  PROPERTIES = 'PROPERTIES',
  SERVICE = 'SERVICE',
  OTHERS = 'OTHERS',
}

export default SetupBusiness;
