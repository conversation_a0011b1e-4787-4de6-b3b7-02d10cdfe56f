import { wp } from '@/assets/utils/js';
import GetStartedTasks from '@/components/get-started/tasks';
import { BaseText, CircledIcon } from '@/components/ui';
import { CheckActive } from '@/components/ui/icons';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import Row from '@/components/ui/row';
import useAuthContext from '@/contexts/auth/auth-context';
import useStatusbar from '@/hooks/use-statusbar';
import colors from '@/theme/colors';
import { useNavigation } from '@react-navigation/native';
import { PLAN_TYPE, StoreInterface } from 'catlog-shared';
import { FlashCircle, InfoCircle } from 'iconsax-react-native/src';
import { useEffect, useMemo } from 'react';
import { ScrollView, View, ViewStyle } from 'react-native';
import Animated, { useAnimatedStyle, useSharedValue, withDelay, withSpring } from 'react-native-reanimated';
import SetupProgressTasks from 'src/components/auth/setup-progress-tasks';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import { useFeatureFlags } from 'src/contexts/feature-flags/use-feature-flags';

interface StepStatusBarProps {
  progressStyle: ViewStyle;
  completedCount: number;
  stepCount: number;
}

const SetupProgress = () => {
  const { setStatusBar } = useStatusbar();
  setStatusBar('dark', 'transparent', true);
  const navigation = useNavigation();
  const progressWidth = useSharedValue(0);
  const { store, user } = useAuthContext();
  const { isFeatureEnabled } = useFeatureFlags();

  const subscriptionEnabled = isFeatureEnabled('subscriptions');

  const progressStyle = useAnimatedStyle(() => {
    return {
      width: `${progressWidth.value}%`,
    };
  });

  const accountSetupSteps = useMemo(() => getAccountSetupSteps(store!, user!, subscriptionEnabled), [store, user, subscriptionEnabled]);
  const completed = accountSetupSteps.filter(value => value.isCompleted).length;

  useEffect(() => {
    const percentage = Math.ceil((completed * 100) / accountSetupSteps?.length);
    progressWidth.value = withDelay(300, withSpring(percentage));
  }, [completed]);

  const handleContinue = () => {
    // navigation.navigate('SetupComplete');
    // return

    for (const step of accountSetupSteps) {
      if (step.isCompleted === false && step.route) {
        navigation.navigate(step.route as any);
        break;
      }
    }
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-primary-pastel',
        pageTitle: 'Continue Your Setup',
        variant: HeaderVariants.SUB_LEVEL,
        showNotifications: false,
        showBackButton: false,
      }}>
      <ScrollView className="flex-1" stickyHeaderIndices={[0]} scrollIndicatorInsets={{ right: 1 }}>
        <StepStatusBar completedCount={completed} stepCount={accountSetupSteps.length} progressStyle={progressStyle} />
        <SetupProgressTasks
          tasks={accountSetupSteps as any}
          storeId={store?.id!}
          onboardingSteps={store?.onboarding_steps ?? {}}
          storeCountry={store?.country!}
          disabled
        />
      </ScrollView>
      <FixedBtnFooter
        buttons={[
          {
            text: 'Continue',
            onPress: () => handleContinue(),
          },
        ]}
      />
    </DashboardLayout>
  );
};

const getAccountSetupSteps = (store: StoreInterface, user: any, subscriptionEnabled: boolean) => {
  const rightIcon = (completed: boolean) => {
    if (completed) {
      return (
        <CircledIcon iconBg="bg-grey-bgOne">
          <CheckActive size={wp(13)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
        </CircledIcon>
      );
    }
    return (
      <CircledIcon iconBg="bg-primary-pastel">
        <InfoCircle size={wp(16)} variant="Linear" color={colors.primary.main} strokeWidth={1.5} />
      </CircledIcon>
    );
  };

  return [
    {
      rightIcon: rightIcon(true),
      title: 'Create Account',
      isCompleted: true,
      type: 0,
    },
    {
      rightIcon: rightIcon((user?.stores ?? [])?.length > 0),
      title: 'Create Store',
      isCompleted: (user?.stores ?? [])?.length > 0,
      type: 1,
      route: 'CreateStore',
    },
    {
      rightIcon: rightIcon((store?.item_count ?? 0) >= 1),
      // rightIcon: rightIcon(store?.onboarding_steps?.products_added ?? false),
      title: 'Upload 2 Products',
      isCompleted: Boolean((store?.item_count ?? 0) >= 1),
      // isCompleted: store?.onboarding_steps?.products_added ?? false,
      type: 3,
      route: 'SetupAddProducts',
    },
    {
      rightIcon: rightIcon(Boolean(store?.business_category?.name) && Boolean(store?.business_category?.type)),
      title: 'Provide Business Information',
      isCompleted: Boolean(store?.business_category?.name) && Boolean(store?.business_category?.type),
      type: 2,
      route: 'SetupBusiness',
    },
    ...(subscriptionEnabled
      ? [
          {
            rightIcon: rightIcon(Boolean(store?.subscription) ?? false),
            // rightIcon: rightIcon(store?.subscription && store?.subscription?.plan?.type !== PLAN_TYPE.STARTER ? true : false),
            title: 'Select Preference',
            isCompleted: Boolean(store?.subscription) ?? false,
            // isCompleted: store?.subscription && store?.subscription?.plan.type !== PLAN_TYPE.STARTER ? true : false,
            route: 'PickPlan',
            type: 4,
          },
        ]
      : []),
  ];
};

const StepStatusBar = ({ progressStyle, completedCount, stepCount }: StepStatusBarProps) => (
  <View className=" bg-grey-bgTwo">
    <Row className="py-15 bg-grey-bgTwo px-20">
      <CircledIcon iconBg="bg-accentGreen-pastel">
        <FlashCircle variant={'Bulk'} size={wp(20)} color={colors?.accentGreen.main} />
      </CircledIcon>
      <BaseText fontSize={14} type="heading" classes="flex-1 mx-10 text-black-secondary">
        Setup Progress
      </BaseText>
      <BaseText fontSize={12} classes="text-black-secondary">
        {completedCount}/{stepCount} complete
      </BaseText>
    </Row>
    <Animated.View style={progressStyle} className="h-5 bg-accentGreen-main rounded-r-full w-2/5" />
  </View>
);

export default SetupProgress;
