import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { Copy, Link, Link2 } from 'iconsax-react-native/src';
import { ScrollView, View, ViewStyle } from 'react-native';
import { copyToClipboard, wp } from 'src/assets/utils/js';
import PreviewStoreFront from 'src/components/payments/preview-store';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import Container from 'src/components/ui/container';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import useAuthContext from 'src/contexts/auth/auth-context';
import useModals from 'src/hooks/use-modals';
import colors from 'src/theme/colors';
import ConfettiCannon from 'react-native-confetti-cannon';

const SetupComplete = () => {
  const { storeLink, refetchSession, decideNextRoute, userLoading } = useAuthContext();
  const { toggleModal, modals } = useModals(['preview']);

  const handleContinue = async () => {
    await refetchSession();
    decideNextRoute();
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-primary-pastel',
        pageTitle: 'Setup Complete',
        variant: HeaderVariants.SUB_LEVEL,
        showNotifications: false,
        showBackButton: false,
      }}>
      <ScrollView className="flex-1">
        <Container className="items-center mt-40">
          <SuccessCheckmark />
          <View className={`items-center justify-center mt-10`}>
            <BaseText fontSize={20} classes={`mt-10 font-fhOscarLight text-black-main`} type="heading">
              You’re all setup
            </BaseText>
            <BaseText fontSize={20} weight={'bold'} classes={`text-black-main mt-3`} type="heading">
              Your Store is Live!🎉
            </BaseText>
            <BaseText className="text-center text-black-placeholder mt-15 max-w-[300px]">
              Your Catlog store is now ready and you're all set to start selling to customers!
            </BaseText>
          </View>
          <View className={`items-center justify-center mt-20 bg-grey-bgTwo rounded-15`}>
            <BaseText fontSize={12} weight="medium" className="text-center text-black-muted mt-15">
              Your Store Link:
            </BaseText>
            <Pressable onPress={() => copyToClipboard(storeLink ?? '')}>
              <Row className={'justify-start py-15 px-20 mt-10 rounded-full border-t border-grey-border'}>
                <View className="mr-5">
                  <BaseText fontSize={12} weight={'medium'} classes={'text-primary-main'}>
                    {storeLink}
                  </BaseText>
                </View>
                <Copy size={wp(12)} color={colors?.primary.main} />
              </Row>
            </Pressable>
          </View>
        </Container>
      </ScrollView>
      <ConfettiCannon count={200} origin={{ x: -10, y: 0 }} fadeOut={true} />
      <PreviewStoreFront toggle={() => toggleModal('preview')} show={modals.preview} />
      <FixedBtnFooter
        buttons={[
          {
            text: 'Preview Store',
            onPress: () => toggleModal('preview'),
            variant: ButtonVariant.LIGHT,
          },
          {
            text: userLoading ? 'Loading user data...' : 'Proceed to Home',
            onPress: () => handleContinue(),
            isLoading: userLoading,
          },
        ]}
      />
    </DashboardLayout>
  );
};

export default SetupComplete;
