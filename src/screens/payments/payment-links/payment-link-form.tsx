import React from 'react';
import { useFormik } from 'formik';
import { AddCircle, ArrowRight } from 'iconsax-react-native/src';
import { forwardRef, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { getFieldvalues, testYupValidation, wp } from 'src/assets/utils/js';
import AddCustomerModal from '@/components/customer/add-customer-modal';
import CustomerInitial from '@/components/customer/customer-initial';
import { CustomerResponse } from '@/components/invoices/create/basic-info-section';
import { CircledIcon, Container } from '@/components/ui';
import ListItemCard from '@/components/ui/cards/list-item-card';
import { Search } from '@/components/ui/icons';
import Input from '@/components/ui/inputs/input';
import SelectDropdown, { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import useModals from 'src/hooks/use-modals';
import colors from 'src/theme/colors';
import * as Yup from 'yup';
import {
  CreatePaymentLinkParams,
  PaginateSearchParams,
  GET_CUSTOMERS,
  InvoiceInterface,
  CURRENCIES,
  CREATE_PAYMENT_LINK,
  UPDATE_PAYMENT_LINK,
  CustomerInterface,
} from 'catlog-shared';
import SelectCustomer from 'src/components/customer/select-customer';
import { View } from 'react-native';
import MoneyInput from 'src/components/ui/inputs/money-input';

export interface PaymentLinkFormParams {
  id?: string;
  narration: string;
  customer: string;
  amount: number;
  currency: CURRENCIES;
}
interface PaymentLinkFormProps {
  initialValues?: PaymentLinkFormParams;
  onComplete: (item: InvoiceInterface) => void;
  setIsSubmitting?: (state: boolean) => void;
  formValidityCallBack?: (isValid: boolean) => void;
}

export interface GenericFormRef {
  submitForm: () => Promise<void>;
}

export const PaymentLinkForm: React.ForwardRefExoticComponent<
  PaymentLinkFormProps & React.RefAttributes<GenericFormRef>
> = forwardRef(({ initialValues, setIsSubmitting, onComplete, formValidityCallBack }, ref) => {
  const { store } = useAuthContext();
  const dropDownRef = useRef<DropDownMethods>(null);
  const { modals, toggleModal } = useModals(['addCustomerModal']);

  const createLinkRequest = useApi<CreatePaymentLinkParams>({
    key: CREATE_PAYMENT_LINK.name,
    apiFunction: CREATE_PAYMENT_LINK,
    method: 'POST',
  });

  const updateLinkRequest = useApi<CreatePaymentLinkParams>({
    key: UPDATE_PAYMENT_LINK.name,
    apiFunction: UPDATE_PAYMENT_LINK,
    method: 'PUT',
  });

  const submitForm = async () => {
    form.submitForm();
  };

  useImperativeHandle(ref, () => ({ submitForm }), []);

  const isEditForm = !!initialValues;
  const currentReq = isEditForm ? updateLinkRequest : createLinkRequest;

  const form = useFormik<PaymentLinkFormParams>({
    initialValues: initialValues ?? {
      amount: 0,
      narration: '',
      currency: store?.currencies?.products,
      customer: '',
    },
    validationSchema,
    onSubmit: async values => {
      setIsSubmitting(true);
      if (isEditForm) values['id'] = initialValues.id;
      const [res, err] = await currentReq.makeRequest({
        ...values,
        amount: Number.parseFloat(values.amount.toString()),
      });

      onComplete(res?.data);
      setIsSubmitting(false);
    },
  });

  const handleAddCustomer = () => {
    toggleModal('addCustomerModal', false);
    setTimeout(() => {
      dropDownRef.current?.open();
    }, 600);
  };

  const disableSubmission = useMemo(() => {
    const { isValid } = testYupValidation<any>(validationSchema, form.values);
    formValidityCallBack?.(!isValid);
    return !isValid;
  }, [form.values]);

  return (
    <>
      <Container className="mt-15">
        <Input {...getFieldvalues('narration', form)} label="What is this payment for" />
        <View className="mt-15">
          {/* No Currency Here @kayode */}
          <SelectCustomer
            onSelectCustomer={(customer: CustomerInterface) => {
              form.setFieldValue('customer', customer.id!);
            }}
            selectedCustomer={form.values?.customer}
            showLeftAccessory={false}
            externalRef={dropDownRef}
            closeAfterSelection={false}
            showButton={true}
            buttons={[{ text: 'Continue', onPress: () => dropDownRef.current.close() }]}
          />
        </View>
        <MoneyInput
          {...getFieldvalues('amount', form, 'number')}
          label={`Amount to be paid in ${store?.currencies?.default}`}
          // containerClasses="mt-15"
        />
      </Container>
      <AddCustomerModal
        isVisible={modals.addCustomerModal}
        closeModal={() => toggleModal('addCustomerModal', false)}
        onPressButton={handleAddCustomer}
      />
    </>
  );
});

const validationSchema = Yup.object().shape({
  customer: Yup.string().required('Please select a customer'),
  narration: Yup.string().required('Please provide a narration'),
  amount: Yup.number()
    .required('Amount is required')
    .min(1, 'Amount must be greater than 0')
    .typeError('Amount must be a number'),
  currency: Yup.string()
    .required('Please select a currency')
    .oneOf(
      [CURRENCIES.EUR, CURRENCIES.USD, CURRENCIES.GBP, CURRENCIES.NGN, CURRENCIES.GHC, CURRENCIES.ZAR],
      'Please select a valid currency',
    ),
});
