import React, { useEffect, useRef, useState } from 'react';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from 'src/components/ui/layouts/header';
import { ScrollView, View } from 'react-native';
import { alertPromise, enumToHumanFriendly, hp, toCurrency, toNaira, wp } from 'src/assets/utils/js';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import { BaseText } from 'src/components/ui';
import useRouteParams from 'src/hooks/use-route-params';
import { useApi } from 'src/hooks/use-api';
import {
  capitalizeFirstLetter,
  GET_TRANSACTION,
  Transaction,
  PaymentInterface,
  removeCountryCode,
  humanFriendlyDate,
  WithdrawalAccount,
  CustomerInterface,
  LINK_CUSTOMER_TO_PAYMENTS,
} from 'catlog-shared';
import { TRANSACTION_TYPE } from 'src/components/search/search-transactions-filter-modal';
import Row from 'src/components/ui/row';
import ImageTextPlaceholder from 'src/components/ui/image-text-placholder';
import ContactWidget from 'src/components/customer/contact-widget';
import WhiteCardBtn from 'src/components/ui/buttons/white-card-btn';
import { ArrowUpRight } from 'src/components/ui/icons';
import colors from 'src/theme/colors';
import LoadingScreen from '../loading';
import ProductInfoRow from 'src/components/products/product-info-row';
import CircledIcon from 'src/components/ui/circled-icon';
import {
  ArrowCircleRight2,
  Calendar2,
  DollarCircle,
  MoneyForbidden,
  Note1,
  PercentageCircle,
  Signpost,
  TickCircle,
  TicketDiscount,
} from 'iconsax-react-native/src';
import classNames from 'node_modules/classnames';
import SectionContainer from 'src/components/ui/section-container';
import InfoRow from 'src/components/ui/others/info-row';
import Column from 'src/components/ui/column';
import { getBankImageElement } from 'src/components/payments/withdraw-cash/add-bank-modal';
import Separator from 'src/components/ui/others/separator';
import SelectCustomer from 'src/components/customer/select-customer';
import { DropDownMethods } from 'src/components/ui/inputs/select-dropdown';
import Toast from 'react-native-toast-message';
import CustomerInformationModal from 'src/components/customer/customer-information-modal';
import useModals from 'src/hooks/use-modals';
import useAuthContext from 'src/contexts/auth/auth-context';

const PaymentInfo = () => {
  const params = useRouteParams<'PaymentInfo'>();
  const transactionId = params?.id ?? '67e9424211b21e00066db4ad';
  const [customer, setCustomer] = useState<CustomerInterface | null>(null);
  const [selectedCustomer, setSelectedCustomer] = React.useState<string | null>(null);
  const dropdownRef = useRef<DropDownMethods>(null);
  const { modals, toggleModal } = useModals(['customerInfo']);
    const { userAccountDeactivated } = useAuthContext();
  

  const getTransactionReq = useApi(
    {
      apiFunction: GET_TRANSACTION,
      key: GET_TRANSACTION.name,
      method: 'GET',
    },
    { id: transactionId },
  );

  const linkCustomerToPaymentReq = useApi({
    apiFunction: LINK_CUSTOMER_TO_PAYMENTS,
    key: LINK_CUSTOMER_TO_PAYMENTS.name,
    method: 'PUT',
  });

  const transaction: Transaction = getTransactionReq?.response?.data ?? null;
  const payment: PaymentInterface = (transaction as any)?.payment;
  const isDebit = transaction?.type === TRANSACTION_TYPE.DEBIT;
  const withdrawalAccount: WithdrawalAccount = (transaction as any)?.withdrawal_account;

  useEffect(() => {
    if (payment && payment?.customer) {
      setCustomer(payment?.customer);
    }
  }, [payment]);

  const linkCustomerToPayment = async (customer: CustomerInterface) => {
    const alertResponse = await alertPromise(
      'Link Customer',
      `Are you sure you want to link ${customer.name} to this payment?`,
      'Yes, Link',
      'Cancel',
      false,
    );

    if (!alertResponse) return;

    dropdownRef.current.close();

    const [res, err] = await linkCustomerToPaymentReq.makeRequest({
      reference: payment?.reference,
      customer_id: customer.id,
    });

    if (res) {
      setCustomer(customer);
      Toast.show({ type: 'success', text1: 'Customer linked successfully' });
    } else {
      Toast.show({ type: 'error', text1: err?.message ?? 'Something went wrong, please try again' });
    }
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentGreen-pastel',
        pageTitle: 'Payment Details',
        variant: HeaderVariants.SUB_LEVEL,
        showNotifications: !userAccountDeactivated,
      }}>
      {getTransactionReq?.isLoading && !transaction && <LoadingScreen />}

      {transaction && !getTransactionReq?.isLoading && (
        <ScrollView contentContainerStyle={{ paddingBottom: hp(20) }}>
          <View className="px-20 py-30 items-center bg-accentGreen-pastel">
            <StatusPill
              title={transaction?.type.toLocaleUpperCase()}
              className="bg-white"
              statusType={txTypeMap[transaction?.type]}
            />
            <BaseText fontSize={wp(22)} classes="mt-5 leading-[30px] max-w-[350px] text-center" type="heading">
              {transaction?.narration}
            </BaseText>
          </View>
          <View className="p-20">
            <Row className="border-b border-grey-border pb-20">
              <View>
                <BaseText classes="text-black-muted" fontSize={12}>
                  Amount
                </BaseText>
                <BaseText fontSize={16} weight="bold" type="heading" classes="uppercase">
                  {toCurrency(toNaira(transaction?.amount), transaction?.currency)}
                </BaseText>
              </View>
              <CircledIcon
                className={classNames({ 'bg-[#F4E2E6]': isDebit, 'bg-[#E7F3EF]': !isDebit })}
                iconBg="bg-grey-bgOne">
                <ArrowCircleRight2
                  color={isDebit ? colors.accentRed.main : colors.accentGreen.main}
                  style={{ transform: [{ rotate: isDebit ? '-45deg' : '135deg' }] }}
                  variant="Bold"
                  size={wp(24)}
                />
              </CircledIcon>
            </Row>
            {payment && customer && (
              <Row className="items-center mt-15 pb-15 border-b border-grey-border">
                <ImageTextPlaceholder size="midi" text={customer?.name} />
                <View className="flex-1 mx-10">
                  <BaseText fontSize={14} type="heading" classes="capitalize">
                    {customer?.name}
                  </BaseText>
                  <BaseText classes="text-black-placeholder mt-2" fontSize={12}>
                    {customer?.phone ? removeCountryCode(customer?.phone) : '-'}
                  </BaseText>
                  {/* <ContactWidget phone={customer?.phone} /> */}
                </View>
                <WhiteCardBtn
                  className="bg-grey-bgOne rounded-full"
                  onPress={() => toggleModal('customerInfo')}
                  icon={<ArrowUpRight size={wp(14)} strokeWidth={2} currentColor={colors.primary.main} />}>
                  View Profile
                </WhiteCardBtn>
              </Row>
            )}
            {payment && !customer && (
              <Row className="items-center mt-15 pb-15 border-b border-grey-border">
                <BaseText fontSize={14} type="heading" classes="capitalize">
                  Customer Information
                </BaseText>
                <WhiteCardBtn
                  className="bg-grey-bgOne rounded-full"
                  onPress={() => dropdownRef.current.open()}
                  icon={<ArrowUpRight size={wp(14)} strokeWidth={2} currentColor={colors.primary.main} />}>
                  Link Customer
                </WhiteCardBtn>
              </Row>
            )}
            <SectionContainer classes="pt-15">
              {transaction?.channel === 'WITHDRAWAL' && withdrawalAccount && (
                <>
                  <BaseText fontSize={12} type="heading" classes="mb-10 text-black">
                    Bank Account
                  </BaseText>
                  <Row classes="bg-white rounded-15 items-start py-10 px-15">
                    <Column classes="flex-1">
                      <BaseText fontSize={11} classes=" text-black-muted">
                        {enumToHumanFriendly(withdrawalAccount.bank_name, ' ')}
                      </BaseText>
                      <BaseText fontSize={14} type="heading" classes="mt-3 text-black-muted">
                        {withdrawalAccount.account_number}
                      </BaseText>
                      <BaseText fontSize={13} weight="medium" classes="mt-7 text-black">
                        {withdrawalAccount.account_name}
                      </BaseText>
                    </Column>
                    {getBankImageElement(withdrawalAccount.image, 'h-[50px] w-[50px]', 50)}
                  </Row>
                  <Separator className="mx-0" />
                </>
              )}
              <InfoRow
                iconBg="white"
                title="Status"
                icon={<TickCircle size={wp(15)} color={colors.black.placeholder} />}
                value={<StatusPill title="SUCCESS" whiteBg statusType={StatusType.SUCCESS} />}
              />

              <InfoRow
                iconBg="white"
                title="Fee"
                icon={<MoneyForbidden size={wp(15)} color={colors.black.placeholder} />}
                value={toCurrency(toNaira(transaction?.fee), transaction?.currency)}
              />

              <InfoRow
                iconBg="white"
                title="Date"
                icon={<Calendar2 size={wp(15)} color={colors.black.placeholder} />}
                value={humanFriendlyDate(transaction?.created_at, true)}
              />

              <InfoRow
                iconBg="white"
                title="Purpose"
                icon={<Note1 size={wp(15)} color={colors.black.placeholder} />}
                value={transaction?.source?.purpose}
              />

              {!isDebit && transaction?.source?.method && (
                <InfoRow
                  iconBg="white"
                  title="Method"
                  icon={<Signpost size={wp(15)} color={colors.black.placeholder} />}
                  value={transaction?.source?.method}
                />
              )}
            </SectionContainer>
          </View>
        </ScrollView>
      )}
      <SelectCustomer
        showAnchor={false}
        onSelectCustomer={linkCustomerToPayment}
        selectedCustomer={selectedCustomer}
        showLeftAccessory={false}
        externalRef={dropdownRef}
        closeAfterSelection={false}
        showButton={true}
        buttons={[{ text: 'Continue', onPress: () => dropdownRef.current.close() }]}
        hideAddCustomerBtn
      />
      {customer && (
        <CustomerInformationModal
          isVisible={modals.customerInfo}
          activeCustomer={{ id: customer?.id }}
          closeModal={() => toggleModal('customerInfo', false)}
          showButton={false}
        />
      )}
    </DashboardLayout>
  );
};

const txTypeMap = {
  credit: StatusType.SUCCESS,
  debit: StatusType.DANGER,
};

export default PaymentInfo;
