import React from 'react';
import { View, Linking, Platform } from 'react-native';
import { BaseText } from '@/components/ui';
import Button from '@/components/ui/buttons/button';
import { StatusBar } from 'expo-status-bar';

const STORE_URLS = {
  ios: 'https://apps.apple.com/app/your-app-id',
  android: 'https://play.google.com/store/apps/details?id=your.package.name',
};

const ForceUpdate = () => {
  const handleUpdate = () => {
    const storeUrl = Platform.select({
      ios: STORE_URLS.ios,
      android: STORE_URLS.android,
    });

    Linking.openURL(storeUrl);
  };

  return (
    <View className="flex-1 bg-white px-20 justify-center">
      <StatusBar style="dark" />
      <BaseText type="heading" fontSize={24} classes="text-center mb-10">
        Update Required
      </BaseText>
      <BaseText fontSize={16} classes="text-center mb-30 text-gray-600">
        A new version of the app is available. Please update to continue using the app.
      </BaseText>
      <Button text="Update Now" onPress={handleUpdate} />
    </View>
  );
};

export default ForceUpdate;
