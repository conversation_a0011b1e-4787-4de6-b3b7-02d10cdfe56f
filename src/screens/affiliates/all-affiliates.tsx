import { useNavigation } from '@react-navigation/native';
import {
  AffiliateInterface,
  AffiliateStatisticsInterface,
  GET_AFFILIATE_ANALYTICS,
  GET_AFFILIATES,
  PaginateSearchParams,
} from 'catlog-shared';
import { useState } from 'react';
import { View } from 'react-native';

import { ensureUniqueItems } from '@/assets/utils/js';
import AnalyticsBtn from '@/components/ui/buttons/analytics-btn';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import SearchContainer, { SEARCH_BG_VARIANT } from '@/components/ui/others/search-container';
import { ResponseWithoutPagination, ResponseWithPagination, useApi } from '@/hooks/use-api';
import useModals from '@/hooks/use-modals';
import usePagination from '@/hooks/use-pagination';
import useScrollHandler from '@/hooks/use-scroll-handler';
import AffiliateListSection from 'src/components/affiliates/affiliate-list-section';
import AffiliateAnalyticsModal from 'src/components/affiliates/affiliate-analytics-modal';

interface AffiliateResponseWithPagination extends ResponseWithPagination<AffiliateInterface[]> {}
interface AffiliateResponse {
  data: AffiliateResponseWithPagination;
}

const PER_PAGE = 10;
const AllAffiliates = () => {
  const [affiliates, setAffiliates] = useState<AffiliateInterface[]>([]);
  const [affiliatesAnalytics, setAffiliatesAnalytics] = useState<AffiliateStatisticsInterface>({
    total_affiliates: 0,
    total_orders: 0,
    total_customers: 0,
    top_affiliate: '',
  });

  const { toggleModal, modals } = useModals(['affiliateAnalytics']);

  const { currentPage, setPage, goNext } = usePagination();

  const navigation = useNavigation();

  const getAffiliatesRequest = useApi<PaginateSearchParams, AffiliateResponse>(
    {
      key: GET_AFFILIATES.name,
      apiFunction: GET_AFFILIATES,
      method: 'GET',
      onSuccess: response => {
        setAffiliates(prev => ensureUniqueItems([...prev, ...response?.data?.data]));
      },
    },
    {
      filter: {},
      page: currentPage,
      per_page: PER_PAGE,
    },
  );

  const getAffiliateAnalytics = useApi<void, ResponseWithoutPagination<AffiliateStatisticsInterface>>({
    apiFunction: GET_AFFILIATE_ANALYTICS,
    method: 'GET',
    // autoRequest: false,
    key: 'get-affiliate-analytics',
    onSuccess: response => {
      console.log(response.data);
      setAffiliatesAnalytics(response.data);
    },
  });

  const handleGetAffiliateAnalytics = async () => {
    const [response, error] = await getAffiliateAnalytics.makeRequest();
    if (response) {
      setAffiliatesAnalytics(response.data);
    }
    if (error) {
      // console.log(error);
    }
  };

  const handlePullToRefresh = () => {
    setAffiliates([]);
    handleGetAffiliateAnalytics();
    if (currentPage === 1) {
      getAffiliatesRequest.makeRequest({
        filter: { search: '' },
        page: currentPage,
        per_page: PER_PAGE,
        // sort: 'desc',
      });
      return;
    }

    setPage(1);
  };

  const { scrollHandler, headerStyle, inputStyle } = useScrollHandler(100);

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: 'Affiliates',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <SearchContainer
        {...{ headerStyle, inputStyle }}
        color={SEARCH_BG_VARIANT.RED}
        onPressSearch={() => navigation.navigate('SearchAffiliates')}
        placeholder="Search Affiliates"
      />
      <AnalyticsBtn onPress={() => toggleModal('affiliateAnalytics')} bgColorVariant="RED" />
      <View className="pt-10 flex-1">
        <AffiliateListSection
          isSearch={false}
          {...{ affiliates, setAffiliates, currentPage, goNext, setPage }}
          pullToRefreshFunc={handlePullToRefresh}
          isReLoading={getAffiliatesRequest.isReLoading}
          isLoading={getAffiliatesRequest.isLoading}
          total_pages={getAffiliatesRequest?.response?.data?.total_pages}
          error={getAffiliatesRequest.error}
          scrollHandler={scrollHandler}
        />
      </View>
      <AffiliateAnalyticsModal
        stats={affiliatesAnalytics}
        isLoading={getAffiliateAnalytics.isLoading}
        isVisible={modals.affiliateAnalytics}
        closeModal={() => toggleModal('affiliateAnalytics', false)}
      />
    </DashboardLayout>
  );
};

export default AllAffiliates;
