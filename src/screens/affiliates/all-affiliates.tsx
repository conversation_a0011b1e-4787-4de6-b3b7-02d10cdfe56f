import { useNavigation } from '@react-navigation/native';
import {
  AffiliateInterface,
  AffiliateStatisticsInterface,
  CustomerInterface,
  DELETE_AFFILIATE,
  DELETE_CUSTOMER,
  GET_AFFILIATE_ANALYTICS,
  GET_AFFILIATES,
  GET_CUSTOMERS,
  PaginateSearchParams,
} from 'catlog-shared';
import { Bag, Edit2, Trash, UserOctagon } from 'iconsax-react-native/src';
import { useEffect, useState } from 'react';
import { Alert, LayoutAnimation, RefreshControl, View } from 'react-native';
import Animated from 'react-native-reanimated';
import Toast from 'react-native-toast-message';

import { delay, ensureUniqueItems, updateOrDeleteItemFromList, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import AnalyticsBtn from '@/components/ui/buttons/analytics-btn';
import FAB from '@/components/ui/buttons/fab';
import EmptyState from '@/components/ui/empty-states/empty-state';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import SearchContainer, { SEARCH_BG_VARIANT } from '@/components/ui/others/search-container';
import Shimmer from '@/components/ui/shimmer';
import { ResponseWithoutPagination, ResponseWithPagination, useApi } from '@/hooks/use-api';
import useModals from '@/hooks/use-modals';
import usePagination from '@/hooks/use-pagination';
import useScrollHandler from '@/hooks/use-scroll-handler';
import colors from '@/theme/colors';
import { actionIsAllowed, SCOPES } from 'src/assets/utils/js/permissions';
import useAuthContext from 'src/contexts/auth/auth-context';
import Can from 'src/components/ui/can';
import AffiliateCard from 'src/components/affiliates/affiliate-card';
import AffiliateInformationModal from 'src/components/affiliates/affiliate-info-modal';
import AddAffiliateModal from 'src/components/affiliates/add-affiliate-modal';
import { OptionWithIcon } from 'src/components/ui/more-options';
import AffiliateAnalyticsModal from 'src/components/affiliates/affiliate-analytics-modal';

interface AffiliateResponseWithPagination extends ResponseWithPagination<AffiliateInterface[]> {}
interface AffiliateResponse {
  data: AffiliateResponseWithPagination;
}

const PER_PAGE = 10;
const AllAffiliates = () => {
  const { userRole } = useAuthContext();
  const [isEdit, setIsEdit] = useState(false);
  const [affiliates, setAffiliates] = useState<AffiliateInterface[]>([]);
  const [affiliatesAnalytics, setAffiliatesAnalytics] = useState<AffiliateStatisticsInterface>({
    total_affiliates: 0,
    total_orders: 0,
    total_customers: 0,
    top_affiliate: '',
  });
  const [activeAffiliate, setActiveAffiliate] = useState<AffiliateInterface>({} as AffiliateInterface);

  const { toggleModal, modals } = useModals(['affiliateInfo', 'addAffiliateModal', 'affiliateAnalytics']);

  const { currentPage, setPage, goNext } = usePagination();

  const navigation = useNavigation();

  const deleteAffiliateRequest = useApi({ key: 'delete-affiliate', apiFunction: DELETE_AFFILIATE, method: 'DELETE' });
  const getAffiliatesRequest = useApi<PaginateSearchParams, AffiliateResponse>(
    {
      key: GET_AFFILIATES.name,
      apiFunction: GET_AFFILIATES,
      method: 'GET',
      onSuccess: response => {
        setAffiliates(prev => ensureUniqueItems([...prev, ...response?.data?.data]));
      },
    },
    {
      filter: {},
      page: currentPage,
      per_page: PER_PAGE,
    },
  );

  const getAffiliateAnalytics = useApi<void, ResponseWithoutPagination<AffiliateStatisticsInterface>>({
    apiFunction: GET_AFFILIATE_ANALYTICS,
    method: 'GET',
    // autoRequest: false,
    key: 'get-affiliate-analytics',
    onSuccess: response => {
      console.log(response.data);
      setAffiliatesAnalytics(response.data);
    },
  });

  const handleGetAffiliateAnalytics = async () => {
    const [response, error] = await getAffiliateAnalytics.makeRequest();
    if (response) {
      setAffiliatesAnalytics(response.data);
    }
    if (error) {
      // console.log(error);
    }
  };

  const handlePullToRefresh = () => {
    setAffiliates([]);
    handleGetAffiliateAnalytics();
    if (currentPage === 1) {
      getAffiliatesRequest.makeRequest({
        filter: { search: '' },
        page: currentPage,
        per_page: PER_PAGE,
        // sort: 'desc',
      });
      return;
    }

    setPage(1);
  };

  const handleOnPressEdit = () => {
    setIsEdit(true);
    toggleModal('affiliateInfo', false);
    setTimeout(() => {
      toggleModal('addAffiliateModal', true);
    }, 600);
  };

  const handleDeleteAffiliate = async (item: AffiliateInterface, index: number) => {
    const layoutAnimConfig = {
      duration: 300,
      update: {
        type: LayoutAnimation.Types.easeInEaseOut,
      },
      delete: {
        duration: 100,
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
    };
    const delFunc = async () => {
      if (item.id) {
        const [response, error] = await deleteAffiliateRequest.makeRequest({
          id: item.id!,
        });
        if (response) {
          Toast.show({ type: 'success', text1: 'Affiliates deleted successfully' });
          affiliates.splice(index, 1);
          LayoutAnimation.configureNext(layoutAnimConfig);
          setAffiliates(affiliates);
        }
        if (error) {
          Toast.show({ type: 'error', text1: error.body.message });
        }
      }
    };

    Alert.alert('Delete affiliates', 'Delete affiliates permanently', [
      { text: 'Cancel', onPress: () => {} },
      { text: 'Delete', onPress: delFunc, style: 'destructive' },
    ]);
  };

  const createAffiliateCallback = (updatedData?: AffiliateInterface) => {
    toggleModal('addAffiliateModal', false);
    if (isEdit) {
      setAffiliates(updateOrDeleteItemFromList(affiliates, 'id', activeAffiliate.id, updatedData ?? null));
      return;
    }
    setAffiliates(prev => [updatedData!, ...prev]);
  };

  const moreOptions = (item: AffiliateInterface, index: number) => [
    {
      optionElement: (
        <OptionWithIcon icon={<Edit2 size={wp(15)} color={colors.black.placeholder} />} label={'Edit Affiliate'} />
      ),
      title: 'Edit Affiliate',
      onPress: () => {
        setActiveAffiliate(item);
        handleOnPressEdit();
      },
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Trash size={wp(15)} color={colors.black.placeholder} />} label={'Delete Affiliate'} />
      ),
      title: 'Delete Affiliate',
      onPress: () => handleDeleteAffiliate(item, index),
    },
  ];

  const { scrollHandler, headerStyle, inputStyle } = useScrollHandler(100);

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: 'Affiliates',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <SearchContainer
        {...{ headerStyle, inputStyle }}
        color={SEARCH_BG_VARIANT.RED}
        onPressSearch={() => navigation.navigate('SearchCustomers')}
        placeholder="Search Affiliates"
      />
      <AnalyticsBtn onPress={() => toggleModal('affiliateAnalytics')} bgColorVariant="RED" />
      <Animated.FlatList
        data={affiliates}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        keyExtractor={(item, index) => item.id + index}
        refreshControl={<RefreshControl refreshing={false} onRefresh={() => handlePullToRefresh()} />}
        ListEmptyComponent={() =>
          getAffiliatesRequest?.isLoading ? (
            <AffiliateListSkeletonLoader />
          ) : (
            <EmptyState
              icon={<UserOctagon variant="Bulk" size={wp(40)} color={colors.grey.muted} />}
              btnText="Add new affiliate"
              text={"Your affiliates' list \n  will appear here"}
            />
          )
        }
        className="flex-1 px-20"
        contentContainerStyle={{ flexGrow: 1 }}
        renderItem={({ item, index }) => (
          <AffiliateCard
            affiliate={item}
            index={index}
            onPress={() => {
              setActiveAffiliate(item);
              toggleModal('affiliateInfo');
            }}
            moreOptions={moreOptions(item, index)}
          />
        )}
        onEndReached={() => {
          if (
            !getAffiliatesRequest?.isLoading &&
            affiliates?.length > 0 &&
            currentPage < getAffiliatesRequest?.response?.data?.total_pages
          ) {
            goNext(getAffiliatesRequest?.response?.data?.total_pages);
          }
        }}
        ListFooterComponent={
          <View style={{ marginBottom: 120 }}>
            {affiliates?.length > 0 && getAffiliatesRequest?.isLoading && (
              <View className="mt-5">
                <AffiliateListSkeletonLoader />
              </View>
            )}
          </View>
        }
      />
      <Can data={{ permission: SCOPES.CUSTOMERS.UPDATE_CUSTOMERS }}>
        <FAB
          onPress={() => {
            setIsEdit(false);
            toggleModal('addAffiliateModal');
          }}
        />
      </Can>
      {modals.addAffiliateModal && (
        <AddAffiliateModal
          isEdit={isEdit}
          activeAffiliate={activeAffiliate}
          isVisible={modals.addAffiliateModal}
          closeModal={() => toggleModal('addAffiliateModal', false)}
          callBack={data => createAffiliateCallback(data)}
        />
      )}
      {modals.affiliateInfo && (
        <AffiliateInformationModal
          isVisible={modals.affiliateInfo}
          activeAffiliate={activeAffiliate}
          closeModal={() => toggleModal('affiliateInfo', false)}
          onPressButton={() => handleOnPressEdit()}
          canManageAffiliates={true}
        />
      )}
      <AffiliateAnalyticsModal
        stats={affiliatesAnalytics}
        isLoading={getAffiliateAnalytics.isLoading}
        isVisible={modals.affiliateAnalytics}
        closeModal={() => toggleModal('affiliateAnalytics', false)}
      />
    </DashboardLayout>
  );
};

export default AllAffiliates;

export const AffiliateSkeleton = () => {
  return (
    <View className="flex-row items-center border-b border-b-grey-border py-15">
      <Shimmer {...{ height: 50, width: 50, borderRadius: 100 }} />
      <View className="mx-12 flex-1">
        <Row className="mr-12 mb-10">
          <Shimmer {...{ height: 15, width: 150, borderRadius: 50 }} className="mr-2" />
          <Shimmer {...{ height: 5, width: 15, borderRadius: 50 }} />
        </Row>
        <Shimmer {...{ height: 10, width: 70, borderRadius: 50 }} />
      </View>
    </View>
  );
};

export const AffiliateListSkeletonLoader = () => {
  return (
    <View>
      {Array.from({ length: 10 }, (_, i) => i).map(i => (
        <AffiliateSkeleton key={i} />
      ))}
    </View>
  );
};

const getStats = (data: AffiliateStatisticsInterface) => {
  return [
    {
      icon:
        // prettier-ignore
        <svg width="25" height="25" viewBox="0 0 24 24" fill="none">
          <path d="M17.2903 4.14004L17.2203 7.93004C17.2103 8.45004 17.5403 9.14004 17.9603 9.45004L20.4403 11.33C22.0303 12.53 21.7703 14 19.8703 14.6L16.6403 15.61C16.1003 15.78 15.5303 16.37 15.3903 16.92L14.6203 19.86C14.0103 22.18 12.4903 22.41 11.2303 20.37L9.47027 17.52C9.15027 17 8.39027 16.61 7.79027 16.64L4.45027 16.81C2.06027 16.93 1.38027 15.55 2.94027 13.73L4.92027 11.43C5.29027 11 5.46027 10.2 5.29027 9.66004L4.27027 6.42004C3.68027 4.52004 4.74027 3.47004 6.63027 4.09004L9.58027 5.06004C10.0803 5.22004 10.8303 5.11004 11.2503 4.80004L14.3303 2.58004C16.0003 1.39004 17.3303 2.09004 17.2903 4.14004Z" fill="currentColor"/>
          <path d="M21.4403 20.4702L18.4103 17.4402C18.1203 17.1502 17.6403 17.1502 17.3503 17.4402C17.0603 17.7302 17.0603 18.2102 17.3503 18.5002L20.3803 21.5302C20.5303 21.6802 20.7203 21.7502 20.9103 21.7502C21.1003 21.7502 21.2903 21.6802 21.4403 21.5302C21.7303 21.2402 21.7303 20.7602 21.4403 20.4702Z" fill="currentColor"/>
        </svg>,
      label: 'Total Affiliates',
      value: data.total_affiliates,
      formatted_value: data.total_affiliates,
      color: 'bg-accent-yellow-500',
    },
    {
      icon:
        // prettier-ignore
        <svg width="25" height="25" viewBox="0 0 25 25" fill="none">
          <path d="M21.0521 8.14583L13.0312 12.7917C12.7083 12.9792 12.3021 12.9792 11.9687 12.7917L3.9479 8.14583C3.37498 7.8125 3.22915 7.03125 3.66665 6.54167C3.96873 6.19792 4.31248 5.91667 4.67706 5.71875L10.3229 2.59375C11.5312 1.91667 13.4896 1.91667 14.6979 2.59375L20.3437 5.71875C20.7083 5.91667 21.0521 6.20833 21.3541 6.54167C21.7708 7.03125 21.625 7.8125 21.0521 8.14583Z" fill="white"/>
          <path d="M11.9063 14.7292V21.8333C11.9063 22.625 11.1042 23.1458 10.3959 22.8021C8.25008 21.75 4.6355 19.7813 4.6355 19.7813C3.36466 19.0625 2.323 17.25 2.323 15.7604V10.3854C2.323 9.56251 3.18758 9.04167 3.89591 9.44792L11.3855 13.7917C11.698 13.9896 11.9063 14.3438 11.9063 14.7292Z" fill="white"/>
          <path d="M13.0938 14.7292V21.8333C13.0938 22.625 13.8958 23.1458 14.6042 22.8021C16.75 21.75 20.3646 19.7813 20.3646 19.7813C21.6354 19.0625 22.6771 17.25 22.6771 15.7604V10.3854C22.6771 9.56251 21.8125 9.04167 21.1042 9.44792L13.6146 13.7917C13.3021 13.9896 13.0938 14.3438 13.0938 14.7292Z" fill="white"/>
        </svg>,
      label: 'Total Orders',
      formatted_value: data.total_orders,
      value: data.total_orders,
      color: 'bg-accent-red-500',
    },
    {
      icon:
        // prettier-ignore
        <svg width="25" height="25" viewBox="0 0 25 25" fill="none">
          <path d="M12.5007 2.08331C9.77148 2.08331 7.55273 4.30206 7.55273 7.03123C7.55273 9.70831 9.64648 11.875 12.3757 11.9687C12.459 11.9583 12.5423 11.9583 12.6048 11.9687C12.6257 11.9687 12.6361 11.9687 12.6569 11.9687C12.6673 11.9687 12.6673 11.9687 12.6777 11.9687C15.3444 11.875 17.4382 9.70831 17.4486 7.03123C17.4486 4.30206 15.2298 2.08331 12.5007 2.08331Z" fill="white"/>
          <path d="M17.791 14.7396C14.8848 12.8021 10.1452 12.8021 7.2181 14.7396C5.89518 15.625 5.16602 16.8229 5.16602 18.1041C5.16602 19.3854 5.89518 20.5729 7.20768 21.4479C8.66602 22.4271 10.5827 22.9166 12.4993 22.9166C14.416 22.9166 16.3327 22.4271 17.791 21.4479C19.1035 20.5625 19.8327 19.375 19.8327 18.0833C19.8223 16.8021 19.1035 15.6146 17.791 14.7396Z" fill="white"/>
        </svg>,
      label: 'Customers',
      value: data.total_customers,
      formatted_value: data.total_customers,
      color: 'bg-accent-orange-500',
    },
    {
      icon:
        // prettier-ignore
        <svg width="25" height="25" viewBox="0 0 24 24" fill="none">
          <path d="M12 15C15.7279 15 18.75 12.0899 18.75 8.5C18.75 4.91015 15.7279 2 12 2C8.27208 2 5.25 4.91015 5.25 8.5C5.25 12.0899 8.27208 15 12 15Z" fill="currentColor"/>
          <path d="M15.79 15.6091C16.12 15.4391 16.5 15.6891 16.5 16.0591V20.9091C16.5 21.8091 15.87 22.2491 15.09 21.8791L12.41 20.6091C12.18 20.5091 11.82 20.5091 11.59 20.6091L8.91 21.8791C8.13 22.2391 7.5 21.7991 7.5 20.8991L7.52 16.0591C7.52 15.6891 7.91 15.4491 8.23 15.6091C9.36 16.1791 10.64 16.4991 12 16.4991C13.36 16.4991 14.65 16.1791 15.79 15.6091Z" fill="currentColor"/>
        </svg>,
      label: 'Top Affiliate',
      value: data.top_affiliate,
      formatted_value: data.top_affiliate ?? 'N/A',
      color: 'bg-accent-green-500',
    },
  ];
};
