import { ScrollView, Text, View } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Home2 } from 'iconsax-react-native/src';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import Container from '@/components/ui/container';
import QuickActionListCard from '@/components/quick-actions/quick-action-list-card';
import {
  AddCircle,
  ArrowRight,
  Bag,
  Copy,
  Moneys,
  PercentageCircle,
  Profile2User,
  Shop,
  TicketDiscount,
  Truck,
} from 'iconsax-react-native/src';
import { wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { navigationRef } from '@/navigation';
import { useNavigation } from '@react-navigation/native';
import AddCustomerModal from 'src/components/customer/add-customer-modal';
import useModals from 'src/hooks/use-modals';
import RequestPaymentModal from 'src/components/payments/modals/request-payment-modal';

const QuickActions = () => {
  const navigation = useNavigation();
  const { modals, toggleModal } = useModals(['addCustomerModal', 'requestPayment']);
  // const { createCustomerCallback } = useCustomer();

  const quickActions = [
    {
      icon: <AddCircle variant="Bold" size={wp(20)} color={colors.accentOrange.main} />,
      title: 'Add Products',
      description: 'Upload store products',
      iconBgColor: 'bg-accentOrange-pastel',
      onPress: () => navigation.navigate('CreateProducts'),
    },
    {
      icon: <Bag variant="Bold" size={wp(20)} color={colors.accentRed.main} />,
      title: 'Record Order',
      description: 'Record orders you received',
      iconBgColor: 'bg-accentRed-pastel',
      onPress: () => navigation.navigate('RecordOrder'),
    },
    {
      icon: <Moneys variant={'Bold'} size={wp(20)} color={colors.accentGreen.main} />,
      title: 'Request a payment',
      description: 'Collect payment with invoice',
      iconBgColor: 'bg-accentGreen-pastel',
      // onPress: () => navigation.navigate('CreatePaymentLink'),
      onPress: () => toggleModal('requestPayment'),
    },
    {
      icon: <Truck variant="Bold" size={wp(20)} color={colors.accentOrange.main} />,
      title: 'Initiate Delivery',
      description: 'Request a delivery for your item',
      iconBgColor: 'bg-accentOrange-pastel',
      onPress: () => navigation.navigate('RequestDelivery'),
    },
    {
      icon: <Profile2User variant="Bold" size={wp(20)} color={colors.accentYellow.main} />,
      title: 'Add Customer',
      description: 'Add customer to your store',
      iconBgColor: 'bg-accentYellow-pastel',
      onPress: () => toggleModal('addCustomerModal'),
    },
    {
      icon: <TicketDiscount variant="Bold" size={wp(20)} color={colors.accentRed.main} />,
      title: 'Create coupons',
      description: 'Manage coupons for products',
      iconBgColor: 'bg-accentRed-pastel',
      onPress: () => navigation.navigate('CreateCoupon'),
    },
    {
      icon: <PercentageCircle variant="Bold" size={wp(20)} color={colors.accentYellow.main} />,
      title: 'Create Discounts',
      description: 'Mange discounts for products',
      iconBgColor: 'bg-accentYellow-pastel',
      onPress: () => navigation.navigate('CreateDiscount'),
    },
  ];

  return (
    <DashboardLayout
      headerProps={{ variant: HeaderVariants.ROOT_LEVEL, headerBg: 'bg-accentRed-pastel', pageTitle: 'Quick Actions' }}>
      <ScrollView>
        <Container className="px-20 mt-10">
          <>
            {quickActions.map(item => (
              <QuickActionListCard key={item.title} {...item} />
            ))}
          </>
        </Container>
      </ScrollView>
            <RequestPaymentModal isVisible={modals.requestPayment} closeModal={() => toggleModal('requestPayment', false)} />

      <AddCustomerModal
        isEdit={false}
        isVisible={modals.addCustomerModal}
        closeModal={() => toggleModal('addCustomerModal', false)}
        // callBack={data => createCustomerCallback(data)}
      />
    </DashboardLayout>
  );
};

export default QuickActions;
