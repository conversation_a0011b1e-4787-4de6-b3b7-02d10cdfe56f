import { useEffect, useMemo, useState } from 'react';
import { FlatList, RefreshControl, View } from 'react-native';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { BaseText, Row } from '@/components/ui';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { useNavigation } from '@react-navigation/native';
import EmptyState from '@/components/ui/empty-states/empty-state';
import dayjs from 'dayjs';
import NotificationListCard from '@/components/notifications/notification-list-card';
import { ensureUniqueItems, groupTransactionsByDate, hp, wp } from 'src/assets/utils/js';
import { useApi } from 'src/hooks/use-api';
import { GET_USER_NOTIFICATIONS } from 'catlog-shared';
import usePagination from 'src/hooks/use-pagination';
import { NOTIFICATION_TYPE, NotificationInterface } from 'catlog-shared';
import Shimmer from 'src/components/ui/shimmer';
import Column from 'src/components/ui/column';
import Separator from 'src/components/ui/others/separator';
import colors from 'src/theme/colors';
import { Notification } from 'iconsax-react-native/src';

interface NotificationSectionProps {
  content: NotificationInterface[];
  title: string;
}

const PER_PAGE = 10;

const NotificationList = () => {
  const navigation = useNavigation();

  const [notifications, setNotifications] = useState<NotificationInterface[]>([]);

  const { currentPage, goNext, setPage } = usePagination();

  const notificationsDay = useMemo(() => {
    const grouped = notifications.reduce(
      (groupedNotifications, notification) => {
        const date = notification.created_at?.toString()?.split('T')[0];

        if (!groupedNotifications[date]) {
          groupedNotifications[date] = [];
        }

        groupedNotifications[date].push(notification);
        return groupedNotifications;
      },
      {} as { [date: string]: NotificationInterface[] },
    );

    return Object.entries(grouped)
      .map(([dateStr, notifications]) => ({
        date: new Date(dateStr),
        notifications,
      }))
      .sort((a, b) => b.date.getTime() - a.date.getTime());
  }, [notifications]);

  const notificationListReq = useApi(
    {
      key: 'get-notifications',
      apiFunction: GET_USER_NOTIFICATIONS,
      method: 'GET',
      onSuccess: res => {
        setNotifications(prev => ensureUniqueItems([...prev, ...res?.data?.notifications]));
      },
    },
    {
      page: currentPage,
      per_page: PER_PAGE,
    },
  );

  const NotificationSection = ({ title, content }: NotificationSectionProps) => {
    return (
      <View className="mt-20 px-20">
        <BaseText fontSize={13} className={'font-interMedium text-black-muted'}>
          {title}
        </BaseText>
        <View className="rounded-12 bg-grey-bgTwo mt-12 overflow-hidden">
          {content?.map((item, index) => (
            <NotificationListCard
              content={item}
              setNotifications={setNotifications}
              isLast={index === content.length - 1}
              key={index}
            />
          ))}
        </View>
      </View>
    );
  };

  const handlePullToRefresh = () => {
    try {
      setNotifications([]);
      if (currentPage === 1) {
        notificationListReq.reset();
        return;
      }
      setPage(1);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <DashboardLayout
      headerProps={{ headerBg: 'bg-primary-pastel', pageTitle: 'Notifications', variant: HeaderVariants.SUB_LEVEL }}>
      <FlatList
        data={notificationsDay}
        refreshControl={<RefreshControl refreshing={false} onRefresh={handlePullToRefresh} />}
        keyExtractor={(item, index) => item.date.toString() + index}
        onEndReachedThreshold={0.3}
        ListEmptyComponent={() =>
          notificationListReq?.isLoading ? (
            <View className="mt-15">
              <SkeletonLoader />
            </View>
          ) : (
            <EmptyState
              classes="mt-20"
              icon={<Notification variant="Bold" color={colors.grey.muted} />}
              title="No Notifications Yet"
              text="It looks like there are no notifications yet. Once you start actively using your catalog store, the notifications will start rolling in!"
              showBtn={false}
            />
          )
        }
        className="flex-1"
        contentContainerStyle={{ flexGrow: 1 }}
        onEndReached={() => {
          if (!notificationListReq?.isLoading /* && transactionsByDay.length === 0 */) {
            goNext(notificationListReq?.response?.total_pages);
          }
        }}
        renderItem={({ item }) => (
          <NotificationSection
            key={item.date.toDateString()}
            title={item.date.toDateString()}
            content={item.notifications}
          />
        )}
        ListFooterComponent={
          <View style={{ marginBottom: 80 }}>
            {notificationListReq?.isLoading && (
              <View className="mt-20">
                <SkeletonLoader />
              </View>
            )}
          </View>
        }
      />
    </DashboardLayout>
  );
};

export default NotificationList;

const dummyRow = new Array(15).fill(0);

const SkeletonLoader = () => {
  return (
    <View className="mx-20 mb-25">
      <Shimmer classes="mb-15" borderRadius={wp(10)} height={hp(15)} width={wp(70)} />
      <View className="rounded-12 bg-grey-bgTwo pt-15 overflow-hidden">
        {dummyRow.map((_, index) => (
          <View key={index} className="px-15">
            <Row className="items-start" spread>
              <Row>
                <Shimmer borderRadius={wp(10)} height={hp(40)} width={wp(40)} className="bg-grey-bgOne" />
                <Column className="ml-10" spread>
                  <Shimmer classes="mb-10" borderRadius={wp(10)} height={hp(12)} width={wp(80)} />
                  <Shimmer classes="" borderRadius={wp(10)} height={hp(10)} width={wp(45)} />
                </Column>
              </Row>
              <Shimmer classes="ml-10" borderRadius={wp(10)} height={hp(10)} width={wp(40)} />
            </Row>
            {index !== dummyRow.length && <Separator className="mx-0" />}
          </View>
        ))}
      </View>
    </View>
  );
};

const notificationLists = [
  {
    title: 'New Order Pending Payment',
    date: new Date(),
    created_at: new Date(),
    description: 'Joseph Atada just ordered items worth, NGN 55,000.00',
    type: NOTIFICATION_TYPE.NEW_ORDER,
  },
  {
    title: 'New Payment for Order',
    date: new Date(),
    created_at: new Date(),
    description: 'Joseph Atada just ordered items worth, NGN 55,000.00',
    type: NOTIFICATION_TYPE.PAYMENT_RECEIVED_ORDER,
  },
  {
    title: 'New Payment for Invoice',
    date: new Date(),
    created_at: new Date(),
    description: 'Joseph Atada just ordered items worth, NGN 55,000.00',
    type: NOTIFICATION_TYPE.PAYMENT_RECEIVED_INVOICE,
  },
  {
    title: "You've received some money 🤑",
    date: new Date(),
    created_at: new Date(),
    description: 'Joseph Atada sent you NGN 30,000.00 via your GTB account',
    type: NOTIFICATION_TYPE.PAYMENT_RECEIVED_BANK,
  },
  {
    title: 'KYC Approved 🎉',
    date: new Date().setDate(new Date().getDate() - 1),
    created_at: new Date().setDate(new Date().getDate() - 1),
    description:
      'Your KYC was Approved, you can now collect payments with Catlog, we also created a bank account for you.',
    type: NOTIFICATION_TYPE.KYC_APPROVED,
  },
  {
    title: 'KYC Declined 😔',
    date: new Date().setDate(new Date().getDate() - 1),
    created_at: new Date().setDate(new Date().getDate() - 1),
    description: "Your KYC was declined, here's the reason: please provide a clear image of your NIN",
    type: NOTIFICATION_TYPE.KYC_DECLINED,
  },
  {
    title: 'KYC Declined 😔',
    date: new Date().setDate(new Date().getDate() - 1),
    created_at: new Date().setDate(new Date().getDate() - 1),
    description: "Your KYC was declined, here's the reason: please provide a clear image of your NIN",
    type: NOTIFICATION_TYPE.KYC_DECLINED,
  },
  {
    title: 'Delivery has been booked',
    date: new Date().setDate(new Date().getDate() - 1),
    created_at: new Date().setDate(new Date().getDate() - 1),
    description: 'Your delivery has been booked and is now scheduled for confirmation',
    type: NOTIFICATION_TYPE.DELIVERY_STATUS,
  },
  {
    title: 'Delivery has been confirmed',
    date: new Date().setDate(new Date().getDate() - 1),
    created_at: new Date().setDate(new Date().getDate() - 1),
    description: 'Your delivery has been confirmed and is ready for pick up',
    type: NOTIFICATION_TYPE.DELIVERY_STATUS,
  },
  {
    title: 'Delivery has been picked up',
    date: new Date().setDate(new Date().getDate() - 1),
    created_at: new Date().setDate(new Date().getDate() - 1),
    description: 'Your delivery has been picked up and is on its way to Joseph',
    type: NOTIFICATION_TYPE.DELIVERY_STATUS,
  },
  {
    title: 'Order delivered successfully',
    date: new Date().setDate(new Date().getDate() - 1),
    created_at: new Date().setDate(new Date().getDate() - 1),
    description: 'Your delivery has been picked up and is on its way to Joseph',
    type: NOTIFICATION_TYPE.DELIVERY_STATUS,
  },
  {
    title: 'Your subscription is expiring 😕',
    date: new Date().setDate(new Date().getDate() - 1),
    created_at: new Date().setDate(new Date().getDate() - 1),
    description: "We're unable to automatically renew your subscription, please click here to renew it",
    type: NOTIFICATION_TYPE.SUBSCRIPTION_EXPIRING,
  },
  {
    title: 'Your Catlog free trial is ending 😕',
    date: new Date().setDate(new Date().getDate() - 1),
    created_at: new Date().setDate(new Date().getDate() - 1),
    description: 'Your Catlog free trial is ending soon. Upgrade and keep enjoying your features',
    type: NOTIFICATION_TYPE.SUBSCRIPTION_EXPIRING,
  },
  {
    title: 'Your subscription has expired 😕',
    date: new Date().setDate(new Date().getDate() - 1),
    created_at: new Date().setDate(new Date().getDate() - 1),
    description: 'Please renew your subscription to continue using your Catlog store',
    type: NOTIFICATION_TYPE.SUBSCRIPTION_CANCELLED,
  },
  {
    title: 'YMilestone Unlocked!  🎉',
    date: new Date().setDate(new Date().getDate() - 1),
    created_at: new Date().setDate(new Date().getDate() - 1),
    description: 'Congratulations! You’ve officially crossed NGN 10M in payments!',
    type: NOTIFICATION_TYPE.MILESTONE_ORDER,
  },
];
