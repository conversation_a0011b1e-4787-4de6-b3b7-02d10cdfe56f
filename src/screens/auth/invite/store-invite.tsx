import React from 'react';
import { ScrollView, View, Image } from 'react-native';
import PasswordInput from '@/components/ui/inputs/password-input';
import Button from '@/components/ui/buttons/button';
import Container from '@/components/ui/container';
import Input from '@/components/ui/inputs/input';
import AuthLayout from '@/components/ui/layouts/auth-layout';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';

const StoreInvite = () => {
  return (
    <AuthLayout headerProps={{ backgrounColor: 'bg-accentRed-pastel' }}>
      <ScrollView>
        <ScreenInfoHeader
          iconElement={
            <Image
              source={require('@/assets/images/storefront.png')}
              resizeMode={'contain'}
              className="w-[80px] h-[60px]"
            />
          }
          pageTitleTop={'Reset your'}
          colorPalette={ColorPaletteType.ORANGE}
          pageTitleBottom={'Password'}
        />
        <Container className="pt-24">
          <Input keyboardType={'email-address'} label={'Email Address'} containerClasses="mt-15" />
          <PasswordInput label={'Password'} containerClasses="mt-15" />
        </Container>
      </ScrollView>
      <View className="pt-10 px-20 border-t border-t-grey-border">
        <Button onPress={() => {}} text={'Join store'} />
      </View>
    </AuthLayout>
  );
};

export default StoreInvite;
