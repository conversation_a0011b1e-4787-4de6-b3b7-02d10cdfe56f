import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ScrollView, View, Image, TextInput } from 'react-native';
import AuthQuestion from '@/components/auth/auth-question';
import PasswordInput from '@/components/ui/inputs/password-input';
import Container from '@/components/ui/container';
import Input from '@/components/ui/inputs/input';
import AuthLayout from '@/components/ui/layouts/auth-layout';
import ScreenInfoHeader from '@/components/ui/screen-info-header';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { useNavigation } from '@react-navigation/native';
import TopTabs from '@/components/ui/others/top-tabs';
import PhoneNumberInput from '@/components/ui/inputs/phone-number-input';
import { BaseText } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import useAuthContext from '@/contexts/auth/auth-context';
import { useFormik } from 'formik';
import { getFieldvalues } from '@/assets/utils/js';
import * as Yup from 'yup';
import useGuestContext from '@/contexts/auth/guest-context';
import { phoneValidation } from 'src/assets/utils/js/common-validations';
import { Toast } from 'react-native-toast-message/lib/src/Toast';
import { LoginParams } from 'catlog-shared';
import useKeyboard from 'src/hooks/use-keyboard';
import BaseScrollView from 'src/components/ui/base/base-scrollview';
import AvoidKeyboard from 'src/components/ui/layouts/avoid-keyboard';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';

enum SelectionPillType {
  EMAIL_ADDRESS = 'Email Address',
  PHONE_NUMBER = 'Phone Number',
}

const Login = () => {
  const { login, loginRequest } = useGuestContext();
  const [tabIndex, setTabIndex] = useState(0);
  const navigation = useNavigation();
  const isKeyboardVisible = useKeyboard();

  const keyboardActive = useSharedValue(isKeyboardVisible ? 1 : 0);

  useEffect(() => {
    console.log('isKeyboardVisible: ', isKeyboardVisible);
    keyboardActive.value = isKeyboardVisible ? 1 : 0;
  }, [isKeyboardVisible]);

  const emailInputRef = useRef<TextInput>(null);
  const passwordInputRef = useRef<TextInput>(null);

  const form = useFormik<Pick<LoginParams, 'email' | 'password'> & { phone: { code: string; digits: string } }>({
    initialValues: {
      phone: {
        code: '+234',
        digits: '',
      },
      email: '',
      password: '',
    },
    validationSchema: validationSchema(tabIndex === 1),
    onSubmit: async ({ phone, ...values }) => {
      const isPhoneLogin = tabIndex === 1;

      if (isPhoneLogin) {
        const [_, error] = await login({ ...values, phone: `${phone.code}-${phone.digits}` });
        if (error) {
          Toast.show({ type: 'error', text1: error?.message ?? error?.body?.message });
        }
        return;
      }

      const [_, error] = await login(values);
      if (error) {
        Toast.show({ type: 'error', text1: error?.message ?? error?.body?.message });
      }
    },
  });

  const getIndex = (index: number) => {
    setTabIndex(index);
  };

  const headerStyle = useAnimatedStyle(() => {
    return {
      opacity: keyboardActive.value === 1 ? withTiming(0) : withTiming(1),
    };
  });

  const keyboardActiveHeaderStyle = useAnimatedStyle(() => {
    return {
      opacity: keyboardActive.value === 1 ? withTiming(1) : withTiming(0),
    };
  });

  return (
    <AuthLayout headerProps={{ backgrounColor: 'bg-primary-pastel' }}>
      <ScreenInfoHeader
        hideIconOnKeyboardActive
        iconElement={
          <Image
            source={require('@/assets/images/user-invite.png')}
            resizeMode={'contain'}
            className="w-[80px] h-[80px]"
          />
        }
        pageTitleTop={'Welcome back'}
        pageTitleBottom={'Login to your account'}
      />
      <TopTabs
        setIndex={getIndex}
        currentIndex={tabIndex}
        tabItems={[
          {
            key: SelectionPillType.EMAIL_ADDRESS,
            title: SelectionPillType.EMAIL_ADDRESS,
            component: (
              <AvoidKeyboard className="flex-1">
                <BaseScrollView>
                  <Container>
                    <Input
                      keyboardType={'email-address'}
                      label={'Email Address'}
                      autoCapitalize="none"
                      containerClasses="mt-15"
                      {...getFieldvalues('email', form)}
                      returnKeyType="next"
                      onSubmitEditing={() => passwordInputRef?.current?.focus()}
                      ref={emailInputRef}
                    />
                    <PasswordInput
                      {...getFieldvalues('password', form)}
                      label={'Password'}
                      onSubmitEditing={() => form.handleSubmit()}
                      inputRef={passwordInputRef}
                      containerClasses="mt-15"
                    />
                    <View className="items-end">
                      <Pressable onPress={() => navigation.navigate('EnterEmail')}>
                        <BaseText fontSize={13} weight={'semiBold'} classes="text-primary-main mt-10">
                          Forgot password?
                        </BaseText>
                      </Pressable>
                    </View>
                  </Container>
                </BaseScrollView>
              </AvoidKeyboard>
            ),
          },
          {
            key: SelectionPillType.PHONE_NUMBER,
            title: SelectionPillType.PHONE_NUMBER,
            component: (
              <ScrollView keyboardShouldPersistTaps={'handled'}>
                <Container>
                  <PhoneNumberInput
                    containerClasses="mt-15"
                    {...getFieldvalues('phone', form)}
                    onChange={v => form.setFieldValue('phone', v)}
                  />
                  <PasswordInput {...getFieldvalues('password', form)} label={'Password'} containerClasses="mt-15" />
                </Container>
              </ScrollView>
            ),
          },
        ]}
      />
      <AuthQuestion
        question={'New to catlog?'}
        actionText={'Create an account'}
        onPress={() => navigation.navigate('Signup')}
      />
      <FixedBtnFooter
        buttons={[
          {
            text: loginRequest?.isLoading ? 'Logging In...' : 'Login',
            disabled: loginRequest?.isLoading,
            onPress: () => form.handleSubmit(),
          },
        ]}
      />
    </AuthLayout>
  );
};

const validationSchema = (isPhone: boolean) =>
  isPhone
    ? Yup.object().shape({
        phone: phoneValidation('phone'),
        password: Yup.string().required('Password is required'),
      })
    : Yup.object().shape({
        email: Yup.string().email('Invalid email address').required('Email Address is required'),
        password: Yup.string().required('Password is required'),
      });

export default Login;
