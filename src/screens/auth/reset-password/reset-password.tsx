import InfoBadge from '@/components/store-settings/info-badge';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import Container from '@/components/ui/container';
import PasswordInput from '@/components/ui/inputs/password-input';
import AuthLayout from '@/components/ui/layouts/auth-layout';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import { Key } from 'node_modules/iconsax-react-native/src';
import React from 'react';
import { ScrollView } from 'react-native';
import { wp } from 'src/assets/utils/js';
import { CircledIcon } from 'src/components/ui';
import colors from 'src/theme/colors';

const ResetPassword = () => {
  return (
    <AuthLayout headerProps={{ backgrounColor: 'bg-accentOrange-pastel' }}>
      <ScrollView>
        <ScreenInfoHeader
        iconElement={
          <CircledIcon className="bg-accentOrange-main p-15">
            <Key variant={'Bold'} color={colors.white} size={wp(30)} />
          </CircledIcon>
        }
          pageTitleTop={'Reset your'}
          colorPalette={ColorPaletteType.ORANGE}
          pageTitleBottom={'Catlog password'}
        />
        <Container className="pt-24">
          <InfoBadge text={"We've sent a 6 digit code to your email"} />
          <PasswordInput label={'Create new password'} containerClasses="mt-15" />
        </Container>
      </ScrollView>
      <FixedBtnFooter buttons={[{ text: 'Update Password' }]} />
    </AuthLayout>
  );
};

export default ResetPassword;
