import AuthQuestion from '@/components/auth/auth-question';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import Container from '@/components/ui/container';
import Input from '@/components/ui/inputs/input';
import PasswordInput from '@/components/ui/inputs/password-input';
import AuthLayout from '@/components/ui/layouts/auth-layout';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import { useNavigation } from '@react-navigation/native';
import { useFormik } from 'formik';
import { Key, TickCircle } from 'iconsax-react-native/src';
import React, { useState } from 'react';
import { ScrollView, View } from 'react-native';
import { Toast } from 'react-native-toast-message/lib/src/Toast';
import { getFieldvalues, wp } from 'src/assets/utils/js';
import InfoBadge from '@/components/store-settings/info-badge';
import { BaseText } from '@/components/ui';
import Button, { ButtonVariant } from '@/components/ui/buttons/button';
import CircledIcon from '@/components/ui/circled-icon';
import PinInput from '@/components/ui/inputs/pin-input';
import { useApi } from 'src/hooks/use-api';
import colors from 'src/theme/colors';
import * as Yup from 'yup';
import { RESET_PASSWORD, ResetPasswordParams, REQUEST_PASSWORD_RESET } from 'catlog-shared';

const ResetPassword = () => {
  const navigation = useNavigation();
  const [step, setStep] = useState<'submit-email' | 'new-password' | 'success'>('submit-email');

  const resetPasswordReq = useApi({
    apiFunction: RESET_PASSWORD,
    key: RESET_PASSWORD.name,
    method: 'POST',
  });

  const requestResetPasswordReq = useApi({
    apiFunction: REQUEST_PASSWORD_RESET,
    key: REQUEST_PASSWORD_RESET.name,
    method: 'POST',
  });

  const form = useFormik<ResetPasswordParams & { email: string }>({
    initialValues: {
      email: '',
      token: '',
      password: '',
    },
    validationSchema: validationSchema(step),
    onSubmit: async values => {
      if (step === 'submit-email') {
        const [res, error] = await requestResetPasswordReq.makeRequest({ email: values.email, url_path: '' });
        if (error) {
          Toast.show({ type: 'error', text1: error?.body?.message });
        }
        if (res) {
          setStep('new-password');
        }
      } else {
        const [res, err] = await resetPasswordReq.makeRequest(values);
        if (err) {
          Toast.show({ type: 'error', text1: err?.body?.message });
          setStep('new-password');
          form.resetForm();
          return;
        }

        setStep('success');
      }
    },
  });

  return (
    <AuthLayout headerProps={{ backgrounColor: 'bg-accentOrange-pastel' }}>
      <ScrollView>
        <ScreenInfoHeader
          iconElement={
            <CircledIcon className="bg-accentOrange-main p-15">
              <Key variant={'Bold'} color={colors.white} size={wp(30)} />
            </CircledIcon>
          }
          pageTitleTop={'Reset your'}
          colorPalette={ColorPaletteType.ORANGE}
          pageTitleBottom={'Catlog password'}
        />
        <Container className="pt-24">
          {step === 'submit-email' && (
            <>
              <InfoBadge text={'We will send you a 6 digit code'} />
              <Input
                {...getFieldvalues('email', form)}
                keyboardType={'email-address'}
                label={'Email Address'}
                containerClasses="mt-15"
              />
            </>
          )}
          {step === 'new-password' && (
            <>
              <InfoBadge text={"We've sent a 6 digit code to your email"} />
              <PinInput setValue={v => form.setFieldValue('token', v)} value={form.values.token} />
              <PasswordInput
                {...getFieldvalues('password', form)}
                label={'Create new password'}
                containerClasses="mt-15"
              />
            </>
          )}
          {step === 'success' && (
            <View className={'items-center justify-center mt-30'}>
              <CircledIcon className="bg-accentGreen-pastel p-15">
                <CircledIcon className="bg-accentGreen-main p-25">
                  <TickCircle variant={'Bold'} size={wp(50)} color={colors.white} />
                </CircledIcon>
              </CircledIcon>
              <BaseText fontSize={22} type={'heading'} classes="text-center mt-10 max-w-[325px]">
                Your password has{'\n'}been successfully updated
              </BaseText>
              <Button
                text="Back to Login"
                onPress={() => navigation.navigate('Login')}
                variant={ButtonVariant.LIGHT}
                className="mt-20"
              />
            </View>
          )}
        </Container>
      </ScrollView>
      {step !== 'success' && (
        <>
          <AuthQuestion
            onPress={() => navigation.navigate('Login')}
            question={'Remember password?'}
            actionText={'Back to Login'}
          />
          <FixedBtnFooter
            buttons={[
              {
                text: step === 'submit-email' ? 'Get Token' : 'Reset Password',
                isLoading: requestResetPasswordReq.isLoading || resetPasswordReq.isLoading,
                onPress: () => form.submitForm(),
              },
            ]}
          />
        </>
      )}
    </AuthLayout>
  );
};

export default ResetPassword;

const validationSchema = (step: string) =>
  step === 'submit-email'
    ? Yup.object().shape({
        email: Yup.string().email('Invalid email address').required('Email Address is required'),
      })
    : Yup.object().shape({
        password: Yup.string().required('Password is required').min(6, 'Password should be at least 6 characters long'),
        token: Yup.string()
          .required('Token is required')
          .length(6, 'Token should be 6 digits')
          .test('digits', 'Token should contain only digits', value => /^\d+$/.test(value)),
      });
