import React, { useCallback, useState } from 'react';
import { View, Image, FlatList, Dimensions, ImageBackground } from 'react-native';
import BaseScrollView from '@/components/ui/base/base-scrollview';
import PasswordInput from '@/components/ui/inputs/password-input';
import PhoneNumberInput from '@/components/ui/inputs/phone-number-input';
import StoreInviteInfo from '@/components/auth/store-invite-info';
import Button from '@/components/ui/buttons/button';
import Container from '@/components/ui/container';
import Input from '@/components/ui/inputs/input';
import AuthLayout from '@/components/ui/layouts/auth-layout';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import { Animated } from 'react-native';
import { hp, wp } from 'src/assets/utils/js';
import CustomImage from 'src/components/ui/others/custom-image';
import { BaseText } from 'src/components/ui';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import colors from 'src/theme/colors';
import { useNavigation } from '@react-navigation/native';
import useStatusbar from 'src/hooks/use-statusbar';
import { useAsyncStorage } from '@react-native-async-storage/async-storage';

const windowWidth = Dimensions.get('window').width;

const scrollX = new Animated.Value(0);

const Onboarding = () => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const {setStatusBar} = useStatusbar();

  const isOnboarded = useAsyncStorage('isOnboarded');
  
  setStatusBar('dark', 'transparent', true);

  const renderItem = ({ item, index }: { item: (typeof onboardingData)[0]; index: number }) => {
    return (
      <View
        className="flex-1"
        style={{
          flex: 1,
          width: windowWidth,
          paddingBottom: insets.bottom + 12,
        }}>
        <ImageBackground
          className={`flex-1 justify-end`}
          style={{ marginBottom: hp(40) }}
          source={item.imageBg}>
          <CustomImage
            className="w-full"
            style={{ height: hp(450) }}
            imageProps={{ source: item.image, contentFit: 'contain' }}
          />
          {item.otherElement}
        </ImageBackground>
        <View className="px-20 mt-10">
          <View className="mb-24">
            <BaseText type="heading" fontSize={26} classes="mt-10 text-center">
              {item.title}
            </BaseText>
            <BaseText fontSize={16} classes="text-center mt-10 text-black-muted">
              {item.description}
            </BaseText>
          </View>
          <Button text="Get Started" onPress={onPressGetStarted} />
        </View>
      </View>
    );
  };

  const onPressGetStarted = async () => {
    await isOnboarded.setItem(JSON.stringify(false));
    navigation.reset({ index: 0, routes: [{ name: 'Login' }] });
  };

  return (
    <View className="flex-1 bg-white">
      <FlatList
        pagingEnabled
        data={onboardingData}
        keyExtractor={item => item.title}
        horizontal
        scrollEnabled
        showsHorizontalScrollIndicator={false}
        snapToAlignment={'center'}
        scrollEventThrottle={20}
        renderItem={renderItem}
        onScroll={Animated.event(
          [
            {
              nativeEvent: { contentOffset: { x: scrollX } },
            },
          ],
          { useNativeDriver: false },
        )}
      />
      {/* <InfoContainer /> */}
      <Dots scrollX={scrollX} length={onboardingData.length} />
    </View>
  );
};

export default Onboarding;

interface DotsProps {
  scrollX: Animated.Value;
  onboardingData?: typeof onboardingData;
  length?: number;
}

const Dots = (props: DotsProps) => {
  const { scrollX, length } = props;
  const stepPosition = Animated.divide(scrollX, windowWidth);

  const dotColors = { active: colors.primary.main, inactive: colors.primary.extraLight };

  return (
    <View className="w-full flex-row justify-center py-16 items-center absolute" style={{ bottom: hp(210) }}>
      {Array(length)
        .fill(null)
        .map((item, index) => {
          const backgroundColor = stepPosition.interpolate({
            inputRange: [index - 1, index, index + 1],
            outputRange: [dotColors.inactive, dotColors.active, dotColors.inactive],
            extrapolate: 'clamp',
          });

          const height = stepPosition.interpolate({
            inputRange: [index - 1, index, index + 1],
            outputRange: [hp(6), hp(6), hp(6)],
            extrapolate: 'clamp',
          });

          const width = stepPosition.interpolate({
            inputRange: [index - 1, index, index + 1],
            outputRange: [hp(6), hp(30), hp(6)],
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              key={`step-${index}`}
              className="mx-3 rounded-full"
              style={{ backgroundColor, height, width }}
            />
          );
        })}
    </View>
  );
};

const onboardingData = [
  {
    title: 'Take orders without back-\nand-forths with customers',
    description: 'Get a customized online store with your IG page in 5 minutes',
    imageBg: require('@/assets/images/onboarding/onboarding1-bg.png'),
    image: require('@/assets/images/onboarding/onboarding1-image.png'),
    otherElement: (
      <View className="absolute z-30 bottom-4 self-end items-end">
        <CustomImage
          style={{ height: hp(121), width: wp(206) }}
          imageProps={{ source: require('@/assets/images/onboarding/1-element.png'), contentFit: 'contain' }}
        />
      </View>
    ),
  },
  {
    title: 'Manage your business like\nthe big boys do',
    description: 'See records of all your orders and customers, book deliveries with ease',
    imageBg: require('@/assets/images/onboarding/onboarding2-bg.png'),
    image: require('@/assets/images/onboarding/onboarding2-image.png'),
    otherElement: (
      <View className="absolute w-full z-30 -bottom-10 self-end items-end px-12">
        <CustomImage
          className="w-full"
          style={{ height: hp(97) }}
          imageProps={{ source: require('@/assets/images/onboarding/2-element.png'), contentFit: 'contain' }}
        />
      </View>
    ),
  },
  {
    title: 'Collect payments without\nbreaking a sweat',
    description: 'Get paid with different currencies & methods, create invoices and payment links',
    imageBg: require('@/assets/images/onboarding/onboarding3-bg.png'),
    image: require('@/assets/images/onboarding/onboarding3-image.png'),
    otherElement: (
      <View className="absolute w-full z-30 -bottom-10 self-end items-end px-12">
        <CustomImage
          className="w-full"
          style={{ height: hp(97) }}
          imageProps={{ source: require('@/assets/images/onboarding/3-element.png'), contentFit: 'contain' }}
        />
      </View>
    ),
  },
];
