import { CloseCircle, Copy, InfoCircle, TickCircle } from 'iconsax-react-native/src';
import { ActivityIndicator, ScrollView, View } from 'react-native';
import { cx, delay, hideLoader, hp, showLoader, showSuccess, toCurrency, wp, Yup } from 'src/assets/utils/js';
import { BaseText, Row, SelectionPill } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import SheetModal, { SheetModalProps } from 'src/components/ui/modals/sheet-modal';
import SectionContainer from 'src/components/ui/section-container';
import colors from 'src/theme/colors';
import Input from 'src/components/ui/inputs/input';
import Button, { ButtonSize, ButtonVariant } from 'src/components/ui/buttons/button';
import { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { useApi } from 'src/hooks/use-api';
import { ADD_DOMAIN, CHECK_DOMAIN } from 'catlog-shared';
import { useEffect, useMemo, useRef, useState } from 'react';
import useStatusbar from 'src/hooks/use-statusbar';
import Shimmer from 'src/components/ui/shimmer';

interface Props extends Partial<SheetModalProps> {
  closeModal: () => void;
}

const BuyDomainModal = ({ closeModal, ...props }: Props) => {
  const [domain, setDomain] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isValid, setIsValid] = useState(false);
  const [selectedPill, setSelectedPill] = useState<'suggestions' | 'others'>('suggestions');

  const activeDomain = useRef<string | null>(null);

  const checkDomainReq = useApi({
    apiFunction: CHECK_DOMAIN,
    key: CHECK_DOMAIN.name,
    autoRequest: false,
    method: 'GET',
  });

  const hasError = checkDomainReq.error;
  const domainData = checkDomainReq.response?.data ?? null;
  const showAlternativesTabs = domainData?.alternatives.length > 0 && domainData?.recommended.length > 0;
  const suggestionData = useMemo(() => {
    return domainData?.[selectedPill === 'suggestions' ? 'recommended' : 'alternatives'];
  }, [selectedPill, domainData]);

  // Validate domain whenever it changes
  useEffect(() => {
    validateDomain(domain);
  }, [domain]);

  const validateDomain = async (value: string) => {
    try {
      await domainSchema.validate(value);
      setError(null);
      setIsValid(true);
    } catch (err) {
      if (err instanceof Yup.ValidationError) {
        setError(err.message);
      } else {
        setError('Invalid domain');
      }
      setIsValid(false);
    }
  };

  const handleCheckDomain = async () => {
    try {
      await domainSchema.validate(domain);
      activeDomain.current = domain;
      if (!isValid) return;
      const [response, error] = await checkDomainReq.makeRequest({
        domain,
      });
      // hideLoader();
      // if (response) {
      //   // await delay(1000);
      //   // showSuccess('Domain added successfully');
      // }
      // if (error) {
      //   console.log(error);
      // }
    } catch (error) {}
  };

  const { setStatusBarStyle } = useStatusbar();

  setStatusBarStyle(props.visible ? 'light' : 'dark', true);

  return (
    <SheetModal closeModal={closeModal} title="Buy New Domain" {...props}>
      <ScrollView style={{ paddingHorizontal: wp(20) }}>
        <View className="mt-15">
          <BaseText className="text-black-muted" lineHeight={20}>
            Enter the domain you'd like to buy (e.g. storename.com, storename.ng, etc) to check it's availability and
            pricing.
          </BaseText>
          <View className="mt-15">
            <BaseText fontSize={13} type="heading">
              Domain Name
            </BaseText>
            <Input
              hasError={!isValid}
              error={error}
              autoFocus
              keyboardType="url"
              placeholder="yourdomain.com"
              value={domain}
              onSubmitEditing={handleCheckDomain}
              onChangeText={setDomain}
              returnKeyType="search"
              autoCapitalize="none"
              containerClasses="mt-15"
              rightAccessory={checkDomainReq.isLoading && <ActivityIndicator size={wp(14)} color={colors.grey.muted} />}
            />
          </View>
          {checkDomainReq.isLoading && <DomainCardSkeletonLoader />}
          {(domainData?.available === false) && !checkDomainReq.isLoading  && <DomainUnavailableMessage domain={activeDomain.current} />}
          {domainData?.available && (
            <DomainAvailableMessage
              domain={activeDomain.current}
              price={domainData?.price}
              currency={domainData?.currency}
              onClickBuyDomain={() => {}}
              isUserRequestedDomain={true}
            />
          )}
        </View>
        {showAlternativesTabs && (
          <View>
            <View className="flex-row pt-20">
              <SelectionPill
                onPress={() => setSelectedPill('suggestions')}
                selected={selectedPill === 'suggestions'}
                title={'Suggested Domain'}
              />
              <SelectionPill
                onPress={() => setSelectedPill('others')}
                selected={selectedPill === 'others'}
                title={'Other Domains'}
              />
            </View>
            <View>
              {/* <DomainCardSkeletonLoader /> */}
              {suggestionData?.map((domain) => (
                <DomainAvailableMessage key={domain?.domain} domain={domain.domain} price={domain.price} currency={domain.currency} onClickBuyDomain={() => {}} />
              ))}
            </View>
          </View>
        )}
        <View className="h-80" />
      </ScrollView>
    </SheetModal>
  );
};

export default BuyDomainModal;

// Define domain validation schema
const domainSchema = Yup.string()
  .required('Domain is required')
  .matches(
    /^(?!:\/\/)(?!www\.)([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]$/,
    'Please enter a valid domain without http:// or www. (e.g., yourdomain.com)',
  );

interface DomainUnavailableProps {
  domain: string;
}

const DomainUnavailableMessage = ({ domain }: DomainUnavailableProps) => {
  return (
    <Row
      className="px-10 py-8 rounded-10 mt-10"
      style={{
        backgroundColor: colors.notifications.error,
      }}>
      <CloseCircle size={wp(15)} color={colors.accentRed.main} variant={'Bold'} />
      <View className="flex-1">
        <BaseText fontSize={12} classes="text-black-muted mx-8">
          <BaseText fontSize={12} classes="text-black-secondary mx-8" weight={'medium'}>
            {domain}
          </BaseText>{' '}
          is currently unavailable
        </BaseText>
      </View>
    </Row>
  );
};

interface DomainAvailableProps {
  domain: string;
  price: number;
  currency?: string;
  onClickBuyDomain: () => void;
  isUserRequestedDomain?: boolean;
}

const DomainAvailableMessage = ({ domain, price, onClickBuyDomain, currency, isUserRequestedDomain }: DomainAvailableProps) => {
  return (
    <Row
      className={cx(
        'p-10 rounded-10 mt-10',
        { 'bg-grey-bgOne': isUserRequestedDomain },
        { 'border border-grey-border': !isUserRequestedDomain },
      )}
      alignCenter={false}>
      {isUserRequestedDomain && <TickCircle size={wp(20)} color={colors.accentGreen.main} variant={'Bold'} />}
      <View className="flex-1 mx-8">
        <BaseText classes="text-black-muted">
          <BaseText classes="text-black-secondary" weight={'medium'}>
            {domain}
          </BaseText>{' '}
          {isUserRequestedDomain && 'is available'}
        </BaseText>
        <BaseText classes="text-black-placeholder mt-8" weight={'medium'}>
          {toCurrency(price, currency)}
        </BaseText>
      </View>
      <Button
        text="Buy Domain"
        size={ButtonSize.SMALL}
        onPress={onClickBuyDomain}
        variant={isUserRequestedDomain ? ButtonVariant.PRIMARY : ButtonVariant.LIGHT}
      />
    </Row>
  );
};

const DomainCardSkeletonLoader = ({ useBorder = false }: { useBorder?: boolean }) => {
  return (
    <Row
      className={cx('p-10 rounded-10 mt-10', { 'border border-grey-border': useBorder, 'bg-grey-bgOne': !useBorder })}
      alignCenter={false}>
      <Shimmer borderRadius={wp(20)} height={wp(20)} width={wp(20)} />
      <View className="flex-1 mx-8">
        <Shimmer borderRadius={wp(8)} height={hp(14)} width={wp(150)} />
        <Shimmer borderRadius={wp(8)} height={hp(12)} width={wp(100)} marginTop={hp(8)} />
      </View>
      <Shimmer borderRadius={wp(8)} height={hp(30)} width={wp(90)} />
    </Row>
  );
};
