import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { BaseText } from '@/components/ui';
import { getStates, wp } from '@/assets/utils/js';
import TopTabs from '@/components/ui/others/top-tabs';
import { useMemo, useState } from 'react';
import BasicDetails from '@/components/store-settings/store-information/basic-details';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import LocationDetails from '@/components/store-settings/store-information/location-details';
import SocialLinks from '@/components/store-settings/store-information/social-links';
import ExtraInfo from '@/components/store-settings/store-information/extra-info';
import BusinessCategory from '@/components/store-settings/store-information/business-category';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { optionalPhoneValidation, phoneValidation } from 'src/assets/utils/js/common-validations';
import { useApi } from 'src/hooks/use-api';
import Toast from 'react-native-toast-message';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import useKeyboard from 'src/hooks/use-keyboard';
import ThirdPartyIntegrations from '@/components/store-settings/store-information/third-party-integration';
import { UPDATE_STORE_DETAILS, UpdateStoreParams } from 'catlog-shared';
import CustomImage from 'src/components/ui/others/custom-image';
import useRouteParams from 'src/hooks/use-route-params';
import { StoreInformationTabs } from 'src/components/store-settings/types';
import useTabs from 'src/hooks/use-tabs';
import AboutUs from '@/components/store-settings/store-information/about-us';
import Faqs from '@/components/store-settings/store-information/faqs';

export interface CustomUpdateStoreInformationParams extends Omit<UpdateStoreParams, 'phone' | 'secondary_phone'> {
  phone: {
    digits?: string;
    code?: string;
  };
  secondary_phone: {
    digits?: string;
    code?: string;
  };
}

enum SelectionPillType {
  BASIC_DETAILS = 'Basic Details',
  LOCATION_DETAILS = 'Location Details',
  SOCIAL_LINKS = 'Social Links',
  EXTRA_INFO = 'Extra Info',
  BUSINESS_CATEGORY = 'Business category',
  ABOUT_US = 'About Us',
  FAQS = 'FAQs',
  THIRD_PARTY_INTEGRATIONS = 'Third Party Integrations',
}

function StoreInformation() {
  const params = useRouteParams<'StoreInformation'>();
  const { tabs, active, switchTab } = useTabs({
    tabs: Object.values(StoreInformationTabs),
    initialActiveTab: params?.tab ?? 0,
  });

  const { store, updateStore } = useAuthContext();
  const keyboardIsVisible = useKeyboard();

  const country = store?.country;
  const states = getStates(country?.code ?? 'NG');

  const updateStoreDetailRequest = useApi({
    key: 'update-store-details',
    apiFunction: UPDATE_STORE_DETAILS,
    method: 'PUT',
  });

  const form = useFormik<CustomUpdateStoreInformationParams>({
    initialValues: {
      id: store?.id ?? '',
      name: store?.name ?? '',
      phone: {
        digits: store?.phone.split('-')[1],
        code: store?.phone.split('-')[0] ?? '+234',
      },
      secondary_phone: {
        digits: store?.secondary_phone?.split('-')[1],
        code: store?.secondary_phone ? store?.secondary_phone.split('-')[0] : '+234',
      },
      description: store?.description ?? '',
      state: store?.state ?? '',
      address: store?.address ?? '',
      delivery_locations: store?.delivery_locations ?? '',
      socials: {
        facebook: store?.socials?.facebook || '',
        instagram: store?.socials?.instagram || '',
        twitter: store?.socials?.twitter || '',
      },
      configuration: {
        fb_pixel: store?.configuration?.fb_pixel || '',
        facebook_pixel_enabled: (store?.configuration as any)?.facebook_pixel_enabled ?? false,
      },
      extra_info: {
        // production_timeline: store?.extra_info?.production_timeline || "",
        delivery_timeline: store?.extra_info?.delivery_timeline
          ? {
              count: store?.extra_info?.delivery_timeline.split(' ')[0]?.toLowerCase(),
              unit: store?.extra_info?.delivery_timeline.split(' ')[1]?.toLowerCase(),
            }
          : { count: '', unit: '' },
        refund_policy: store?.extra_info?.refund_policy || '',
        images: store?.extra_info?.images ?? [],
        images_label: store?.extra_info?.images_label || '',
      },
    },
    validationSchema,
    onSubmit: async ({ extra_info, ...values }) => {
      const phone = `${values?.phone?.code}-${values?.phone?.digits!}`;
      const secondary_phone = Boolean(values?.secondary_phone?.digits?.trim())
        ? `${values?.secondary_phone?.code}-${values?.secondary_phone?.digits}`
        : '';

      // return;
      const [response, error] = await updateStoreDetailRequest.makeRequest({
        ...values,
        phone,
        secondary_phone, //todo: @silas
        extra_info: {
          ...extra_info,
          delivery_timeline: `${extra_info?.delivery_timeline.count} ${extra_info?.delivery_timeline.unit}`.trim(),
        },
      });

      if (error) {
        Toast.show({ type: 'error', text1: error?.message });
      } else {
        updateStore({ ...response.data });
        Toast.show({ text1: 'Store details updated successfully' });
      }
    },
  });

  const tabItems = [
    {
      key: StoreInformationTabs.BASIC_DETAILS,
      title: SelectionPillType.BASIC_DETAILS,
      component: <BasicDetails form={form} country={country} isLoading={updateStoreDetailRequest.isLoading} />,
      func: () => {},
    },
    {
      key: StoreInformationTabs.LOCATION_DETAILS,
      title: SelectionPillType.LOCATION_DETAILS,
      component: <LocationDetails form={form} country={country} isLoading={updateStoreDetailRequest.isLoading} />,
      func: () => {},
    },
    {
      key: StoreInformationTabs.SOCIAL_LINKS,
      title: SelectionPillType.SOCIAL_LINKS,
      component: <SocialLinks form={form} isLoading={updateStoreDetailRequest.isLoading} />,
      func: () => {},
    },
    {
      key: StoreInformationTabs.EXTRA_INFO,
      title: SelectionPillType.EXTRA_INFO,
      component: <ExtraInfo form={form} isLoading={updateStoreDetailRequest.isLoading} />,
      func: () => {},
    },
    {
      key: StoreInformationTabs.BUSINESS_CATEGORY,
      title: SelectionPillType.BUSINESS_CATEGORY,
      component: <BusinessCategory />,
      func: () => {},
    },
    {
      key: StoreInformationTabs.ABOUT_US,
      title: SelectionPillType.ABOUT_US,
      component: <AboutUs />,
      func: () => {},
    },
    {
      key: StoreInformationTabs.FAQS,
      title: SelectionPillType.FAQS,
      component: <Faqs />,
      func: () => {},
    },
    {
      key: StoreInformationTabs.THIRD_PARTY_INTEGRATIONS,
      title: SelectionPillType.THIRD_PARTY_INTEGRATIONS,
      component: <ThirdPartyIntegrations form={form} isLoading={updateStoreDetailRequest.isLoading} />,
      func: () => {},
    },
  ];

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Store Information',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <AvoidKeyboard>
        <ScreenInfoHeader
          colorPalette={ColorPaletteType.YELLOW}
          hideIconOnKeyboardActive
          hideAllOnKeyboardActive
          iconElement={
            <CustomImage
              transparentBg
              className="w-[80px] h-[80px]"
              imageProps={{ source: require('@/assets/images/information.png'), contentFit: 'cover' }}
            />
          }
          customElements={
            <BaseText fontSize={20} weight={'bold'} classes={`text-black-primary mt-10`} type="heading">
              {tabItems[active].title}
            </BaseText>
          }
        />
        {/* {!keyboardIsVisible && (
          <ScreenInfoHeader
            colorPalette={ColorPaletteType.YELLOW}
            hideIconOnKeyboardActive
            iconElement={
              <CustomImage
                transparentBg
                className="w-[80px] h-[80px]"
                imageProps={{ source: require('@/assets/images/information.png'), contentFit: 'cover' }}
              />
            }
            customElements={
              <BaseText fontSize={20} weight={'bold'} classes={`text-black-primary mt-10`} type="heading">
                {tabItems[active].title}
              </BaseText>
            }
          />
        )} */}
        <TopTabs setIndex={switchTab} currentIndex={active} tabItems={tabItems} />
      </AvoidKeyboard>
    </DashboardLayout>
  );
}

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Store name is required'),
  phone: phoneValidation(),
  secondary_phone: optionalPhoneValidation,
  description: Yup.string().max(150, 'Description cannot be more than 150 characters'),
  address: Yup.string(),
  state: Yup.string(),
  delivery_locations: Yup.string(),
  socials: Yup.object().shape({
    facebook: Yup.string().min(3, 'Username should be at least 3 characters'),
    twitter: Yup.string().min(3, 'Username should be at least 3 characters'),
    instagram: Yup.string().min(3, 'Username should be at least 3 characters'),
  }),
  // delivery_timeline: Yup.string(),
  // production_timeline: Yup.string(),
  refund_policy: Yup.string().max(150, 'Description cannot be more than 150 characters'),
  extra_info_label: Yup.string(),
  configuration: Yup.object().shape({
    fb_pixel: Yup.string().when('facebook_pixel_enabled', {
      is: true,
      then: () =>
        Yup.string()
          .required('Facebook Pixel Code is required')
          .matches(/^\d{15,20}$/, 'Facebook pixel ID should be 15 digits'),
    }),
  }),
});

export default StoreInformation;
