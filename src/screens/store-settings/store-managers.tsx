import React from 'react';
import { BaseText, CircledIcon, Container } from '@/components/ui';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import { EXPO_PUBLIC_PUBLIC_URL } from '@env';
import { useFormik } from 'formik';
import { Clock, Profile2User } from 'iconsax-react-native/src';
import { useState } from 'react';
import { Alert, ScrollView, View } from 'react-native';
import { Toast } from 'react-native-toast-message/lib/src/Toast';
import { copyToClipboard, cx, wp } from 'src/assets/utils/js';
import { optionalPhoneValidation, phoneValidation } from 'src/assets/utils/js/common-validations';
import { SCOPES } from 'src/assets/utils/js/permissions';
import CustomerInitial from '@/components/customer/customer-initial';
import InfoBadge from '@/components/store-settings/info-badge';
import EditStoreManagerModal from '@/components/store-settings/store-manager/edit-store-manager-modal';
import InviteStoreManagerModal from '@/components/store-settings/store-manager/invite-store-manager-modal';
import TeamsSkeletonLoader from '@/components/store-settings/store-manager/teams-skeleton-loader';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import Can from '@/components/ui/can';
import ListItemCard from '@/components/ui/cards/list-item-card';
import EmptyState from '@/components/ui/empty-states/empty-state';
import MoreOptions, { MoreOptionsProps } from '@/components/ui/more-options';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import useKeyboard from 'src/hooks/use-keyboard';
import useModals from 'src/hooks/use-modals';
import colors from 'src/theme/colors';
import * as Yup from 'yup';
import {
  TeamMember,
  DELETE_INVITE,
  GET_TEAM_MEMBERS,
  REMOVE_TEAM_MEMBER,
  UPDATE_STORE_DETAILS,
  UPDATE_TEAM_MEMBER,
  toAppUrl,
} from 'catlog-shared';

function StoreManagers() {
  const { store, user } = useAuthContext();

  const { modals, toggleModal } = useModals(['inviteManager', 'editManager']);
  const [currentTeamMember, setCurrentTeamMember] = useState<TeamMember>();

  const getTeamMemberReq = useApi({
    apiFunction: GET_TEAM_MEMBERS,
    key: GET_TEAM_MEMBERS.name,
    method: 'GET',
  });

  const removeTeamMemberReq = useApi({
    apiFunction: REMOVE_TEAM_MEMBER,
    key: REMOVE_TEAM_MEMBER.name,
    method: 'DELETE',
    autoRequest: false,
  });

  const deleteInviteReq = useApi({
    apiFunction: DELETE_INVITE,
    key: DELETE_INVITE.name,
    method: 'DELETE',
    autoRequest: false,
  });

  const openEditModal = (teamMember: TeamMember) => {
    toggleModal('inviteManager', false);
    setCurrentTeamMember(teamMember);
    setTimeout(() => {
      toggleModal('editManager');
    }, 600);
  };

  const removeTeamMember = async (teamMember: TeamMember) => {
    const request = teamMember.status === 'PENDING' ? deleteInviteReq : removeTeamMemberReq;
    const id = teamMember.status === 'PENDING' ? teamMember.id! : teamMember.user!;

    const [res, error] = await request.makeRequest({
      id,
    });
    if (res) {
      getTeamMemberReq.refetch();
      Toast.show({ type: 'success', text1: 'Manager removed successfully' });
    }
    if (error) {
      Toast.show({ type: 'error', text1: error.body.message });
    }
  };

  const getOptions = (teamMember: TeamMember) =>
    [
      {
        onPress: () => openEditModal(teamMember),
        title: 'Edit',
      },
      ...(teamMember.status === 'PENDING'
        ? [
            {
              onPress: () => copyToClipboard(toAppUrl(`join-store?invite=${teamMember.id}`, true, EXPO_PUBLIC_PUBLIC_URL)),
              title: 'Copy Invite link',
            },
          ]
        : []),
      {
        onPress: () =>
          Alert.alert('Remove Member', 'This Team Member will be deleted permanently. This action cannot be undone.', [
            { text: 'Remove', onPress: () => removeTeamMember(teamMember), style: 'destructive' },
            { text: 'Cancel' },
          ]),
        title: 'Remove member',
      },
    ] as MoreOptionsProps['options'];

  const loading = getTeamMemberReq.isLoading || removeTeamMemberReq.isLoading || deleteInviteReq.isLoading;
  const teams: TeamMember[] = getTeamMemberReq.response?.data || [];
  const isEmpty = teams?.length === 0;

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: 'Store Managers',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <ScrollView>
        <ScreenInfoHeader
          colorPalette={ColorPaletteType.RED}
          iconElement={
            <CircledIcon className="bg-accentRed-main p-15">
              <Profile2User variant={'Bold'} color={colors.white} />
            </CircledIcon>
          }
          pageTitleTop={
            <BaseText fontSize={22} classes={`text-black-primary`} type="heading">
              Store Manager
            </BaseText>
          }
        />
        <Container className="mt-20">
          <InfoBadge text={"Here's a summary of the information you provided"} />
          <View className="mt-20">
            {loading && <TeamsSkeletonLoader />}

            {!isEmpty && !loading && (
              <>
                <Can data={{ permission: SCOPES.SETTINGS.MANAGE_TEAM_MEMBERS }}>
                  {isAllowed => {
                    return (
                      <>
                        {
                          teams?.map((team: TeamMember) => {
                            const isCurrentUser = team.id === user?.id;
                            return (
                              <>
                                <ListItemCard
                                  key={team.id}
                                  leftElement={
                                    team.status === 'PENDING' ? (
                                      <CircledIcon
                                        className={cx('py-0 px-5 bg-accentOrange-light')}
                                        style={{ aspectRatio: 1 / 1 }}>
                                        <Clock size={wp(20)} variant={'Bold'} color={colors.white} />
                                      </CircledIcon>
                                    ) : (
                                      <CustomerInitial initial={team?.name?.[0] ?? ''} />
                                    )
                                  }
                                  title={isCurrentUser ? 'You' : (team.name ?? team.email ?? (team as any).phone ?? '')}
                                  description={team.role ?? ''}
                                  descriptionProps={{ classes: 'text-grey-muted mt-5 uppercase' }}
                                  rightElement={!isCurrentUser && isAllowed && <MoreOptions options={getOptions(team)} />}
                                  showBorder
                                />
                              </>
                            );
                          })}
                      </>
                    );
                  }}
                </Can>
              </>
            )}
            {isEmpty && !loading && (
              <EmptyState
                icon={
                  <CircledIcon
                    className="mb-20 p-15 bg-white"
                    style={{
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 10,
                      },
                      shadowOpacity: 0.05,
                      shadowRadius: 6.84,

                      elevation: 5,
                    }}>
                    <Profile2User variant={'Bulk'} size={wp(40)} color={colors.grey.muted} />
                  </CircledIcon>
                }
                onPressBtn={() => toggleModal('inviteManager')}
                btnText={'Add Manager'}
                text={'No team members added yet'}
              />
            )}
          </View>
        </Container>
      </ScrollView>
      {!isEmpty && (
        <Can  data={{ permission: SCOPES.SETTINGS.MANAGE_TEAM_MEMBERS }}>
            {(isAllowed) => {
            return (
              <FixedBtnFooter buttons={[{ text: 'Invite Manager', disabled: !isAllowed, onPress: () => toggleModal('inviteManager') }]} />

            );
          }}
        </Can>
      )}
      <InviteStoreManagerModal
        isVisible={modals.inviteManager}
        closeModal={() => {
          getTeamMemberReq.refetch();
          toggleModal('inviteManager', false);
        }}
      />

      <EditStoreManagerModal
        isVisible={modals.editManager}
        teamMember={currentTeamMember!}
        closeModal={() => toggleModal('editManager', false)}
        onComplete={() => getTeamMemberReq.refetch()}
      />
    </DashboardLayout>
  );
}

export default StoreManagers;
