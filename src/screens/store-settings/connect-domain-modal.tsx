import { Close<PERSON>ir<PERSON>, Copy } from 'iconsax-react-native/src';
import { ScrollView, View } from 'react-native';
import { copyToClipboard, delay, hideLoader, showLoader, showSuccess, wp, Yup } from 'src/assets/utils/js';
import { BaseText, Row } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import SheetModal, { SheetModalProps } from 'src/components/ui/modals/sheet-modal';
import SectionContainer from 'src/components/ui/section-container';
import colors from 'src/theme/colors';
import Input from 'src/components/ui/inputs/input';
import Button, { ButtonSize } from 'src/components/ui/buttons/button';
import { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { useApi } from 'src/hooks/use-api';
import { ADD_DOMAIN } from 'catlog-shared';
import { useEffect, useState } from 'react';

interface Props extends Partial<BottomModalProps> {
  closeModal: () => void;
  onAddDomainCompleted: VoidFunction;
}

const ConnectDomainModal = ({ closeModal, onAddDomainCompleted, ...props }: Props) => {
  const [domain, setDomain] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isValid, setIsValid] = useState(false);

  const connectApiRequest = useApi({
    apiFunction: ADD_DOMAIN,
    key: ADD_DOMAIN.name,
    method: 'POST',
  });

  // Validate domain whenever it changes
  useEffect(() => {
    validateDomain(domain);
  }, [domain]);

  const validateDomain = async (value: string) => {
    try {
      await domainSchema.validate(value);
      setError(null);
      setIsValid(true);
    } catch (err) {
      if (err instanceof Yup.ValidationError) {
        setError(err.message);
      } else {
        setError('Invalid domain');
      }
      setIsValid(false);
    }
  };

  const handleConnectDomain = async () => {
    // Validate again before submission
    await domainSchema.validate(domain);

    showLoader('Adding Domain...');
    const [response, error] = await connectApiRequest.makeRequest({
      domain,
    });
    hideLoader();
    if (response) {
      await delay(1000);
      showSuccess('Domain added successfully');
      onAddDomainCompleted();
      setDomain(null);
      closeModal();
    }
    if (error) {
      console.log(error);
    }
  };

  return (
    <BottomModal closeModal={closeModal} title="Connect Custom Domain" {...props} enableDynamicSizing>
      <BottomSheetScrollView style={{ paddingHorizontal: wp(20) }}>
        <SectionContainer className="p-14 mt-10">
          <BaseText fontSize={12} type="heading" classes="text-black-placeholder">
            Connect your custom domain to your store.
          </BaseText>
          <View className="mt-15">
            <Row style={{ gap: 2 }}>
              <BaseText classes="text-black-muted">1.</BaseText>
              <View className="flex-1">
                <BaseText classes="text-black-muted">Verify your domain</BaseText>
              </View>
            </Row>
            <View className="mt-10">
              <Row disableSpread alignCenter={false} style={{ gap: 2 }}>
                <BaseText classes="text-black-muted">2.</BaseText>
                <View className="flex-1">
                  <BaseText classes="text-black-muted">
                    Add an A record in your domain provider's DNS settings that points to:
                  </BaseText>
                </View>
              </Row>
              <Row className="mt-10" style={{ gap: wp(8) }}>
                <View className="flex-1 p-14 bg-white rounded-10 border-grey-border border">
                  <BaseText classes="text-black-muted">*************</BaseText>
                </View>
                <Pressable className="p-12 bg-white rounded-8 border-grey-border border" onPress={() => copyToClipboard('*************')}>
                  <Copy size={wp(20)} color={colors?.primary.main} />
                </Pressable>
              </Row>
              <Row disableSpread alignCenter={false} style={{ gap: 2 }} className="mt-10">
                <BaseText classes="text-black-muted">3.</BaseText>
                <View className="flex-1">
                  <BaseText classes="text-black-muted">
                    After adding the A record, click the Verify Domain button
                  </BaseText>
                </View>
              </Row>
            </View>
          </View>
          <View className="py-8 px-12 bg-accentYellow-pastel mt-12">
            <BaseText fontSize={12} classes="text-black-muted">
              DNS changes can take 24-48 hours to update. If verification fails, please wait and try again later.
            </BaseText>
          </View>
        </SectionContainer>
        <View className="mt-15">
          <BaseText type="heading" classes="text-black-secondary">
            Connect Custom Domain
          </BaseText>
          <BaseText classes="text-black-muted mt-10">
            Enter an existing domain name to connect your custom domain
          </BaseText>
          <Row style={{ gap: wp(10) }} className="mt-15" alignCenter={false}>
            <View className="flex-1">
              <Input
                useBottomSheetInput
                hasError={!isValid}
                error={error}
                keyboardType="url"
                placeholder="yourdomain.com"
                value={domain}
                onChangeText={setDomain}
              />
            </View>
            <Button
              size={ButtonSize.MEDIUM}
              text="Add Domain"
              disabled={!isValid || connectApiRequest.isLoading}
              isLoading={connectApiRequest.isLoading}
              onPress={handleConnectDomain}
            />
          </Row>
        </View>
        <View className="h-80" />
      </BottomSheetScrollView>
    </BottomModal>
  );
};

export default ConnectDomainModal;

// Define domain validation schema
const domainSchema = Yup.string()
  .required('Domain is required')
  .matches(
    /^(?!:\/\/)(?!www\.)([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]$/,
    'Please enter a valid domain without http:// or www. (e.g., yourdomain.com)',
  );
