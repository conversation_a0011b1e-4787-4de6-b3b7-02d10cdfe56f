import { ActivityIndicator, ScrollView, Text, View } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { BagTick, Copy, Edit2, EmptyWallet, Gallery, Location, Profile, Setting, Shop } from 'iconsax-react-native/src';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { ImageBackground } from 'react-native';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import colors from '@/theme/colors';
import { copyToClipboard, wp } from '@/assets/utils/js';
import CustomerInitial from '@/components/customer/customer-initial';
import ListItemCard from '@/components/ui/cards/list-item-card';
import { ArrowRight } from '@/components/ui/icons';
import { useNavigation } from '@react-navigation/native';
import useAuthContext from 'src/contexts/auth/auth-context';
import Pressable from '@/components/ui/base/pressable';
import { useState } from 'react';
import * as ImagePicker from 'expo-image-picker';
import useImageUploads from 'src/hooks/use-file-uploads';
import CustomImage from '@/components/ui/others/custom-image';
import UploadImageBtn from '@/components/ui/buttons/upload-image-btn';
import { useApi } from 'src/hooks/use-api';
import Toast from 'react-native-toast-message';
import EditStoreLinkModal from '@/components/store-settings/store-information/edit-store-link-modal';
import useModals from 'src/hooks/use-modals';
import { UPDATE_STORE_DETAILS, UpdateStoreImagesParams } from 'catlog-shared';
import { SCOPES } from 'src/assets/utils/js/permissions';
import Can from 'src/components/ui/can';
import PermissionTest from '@/components/store-settings/permission-test';
import RoleSwitcher from '@/components/store-settings/role-switcher';

export type Image = {
  src: string;
  name: string;
  lastModified: number;
  file: File | Blob;
  isUploading?: boolean;
  uploadProgress?: number;
  url?: string;
  error?: boolean;
  key?: string;
  meta?: any;
  isPlaceholder?: boolean;
};

enum ImageTypes {
  LOGO = 'logo',
  HERO_IMAGE = 'hero_image',
}

const defaultCover = "https://stx.catlog.shop/images/default-store-hero.jpeg";

function StoreSettings() {
  const [loading, setLoading] = useState({ hero_image: false, logo: false });
  const [coverImage, setCoverImage] = useState<string | null>(null);
  const [logo, setLogo] = useState<string | null>(null);
  const { store, storeLink, updateStore } = useAuthContext();
  const navigation = useNavigation();
  const { modals, toggleModal } = useModals(['edit_store_link']);

  const updateStoreRequest = useApi({
    apiFunction: UPDATE_STORE_DETAILS,
    key: 'update-store-request',
    method: 'PUT',
  });

  // useImageUploads(coverImage, setCoverImage)

  const saveImages = async (url: string, type: ImageTypes) => {
    const reqData = { id: store?.id, [type]: url };
    const [res, err] = await updateStoreRequest.makeRequest(reqData);

    if (err) {
      Toast.show({ text1: 'Error uploading photo' });
    }

    if (res) {
      setLoading(prev => ({ ...prev, [type]: false }));
      updateStore({ [type]: res?.data[type] });
    }
  };

  const rightIcon = (
    <CircledIcon iconBg="bg-grey-bgOne p-10">
      <ArrowRight width={wp(16)} height={wp(16)} currentColor={colors.black.placeholder} />
    </CircledIcon>
  );

  const settings = [
    {
      leftIcon: (
        <CircledIcon iconBg="bg-accentOrange-pastel p-10">
          <Profile variant={'Bold'} size={wp(20)} color={colors.accentOrange.main} />
        </CircledIcon>
      ),
      rightIcon: rightIcon,
      title: 'Edit Store Link',
      description: 'Get a customized store link',
      onPress: () => toggleModal('edit_store_link'),
      permissionData: {
        permission: SCOPES.SETTINGS.UPDATE_STORE_DETAILS,
      },
    },
    {
      leftIcon: (
        <CircledIcon iconBg="bg-accentYellow-pastel p-10">
          <Shop variant={'Bold'} size={wp(20)} color={colors.accentYellow.main} />
        </CircledIcon>
      ),
      rightIcon: rightIcon,
      title: 'Store Information',
      description: 'Store name, Store ID, Store plan...',
      onPress: () => navigation?.navigate('StoreInformation'),
    },
    {
      leftIcon: (
        <CircledIcon iconBg="bg-accentRed-pastel p-10">
          <Setting variant={'Bold'} size={wp(20)} color={colors.accentRed.main} />
        </CircledIcon>
      ),
      rightIcon: rightIcon,
      title: 'Store Configurations',
      description: 'Custom color, Opening & closing hours, View modes and Currencies',
      onPress: () => navigation?.navigate('StoreConfigurations'),
    },
    {
      leftIcon: (
        <CircledIcon iconBg="bg-accentGreen-pastel p-10">
          <BagTick variant={'Bold'} size={wp(20)} color={colors.accentGreen.main} />
        </CircledIcon>
      ),
      rightIcon: rightIcon,
      title: 'Checkout Options',
      description: 'Collect payment with invoices',
      onPress: () => navigation?.navigate('CheckoutOptions'),
      permissionData: {
        countryPermission: SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS,
      },
    },
    {
      leftIcon: (
        <CircledIcon iconBg="bg-accentYellow-pastel p-10">
          <Location variant={'Bold'} size={wp(20)} color={colors.accentYellow.main} />
        </CircledIcon>
      ),
      rightIcon: rightIcon,
      title: 'Deliveries',
      description: 'Initiate order delivery, Delivery Area...',
      onPress: () => navigation?.navigate('DeliveryAreas'),
    },
    {
      leftIcon: (
        <CircledIcon iconBg="bg-accentOrange-pastel p-10">
          <EmptyWallet variant={'Bold'} size={wp(20)} color={colors.accentOrange.main} />
        </CircledIcon>
      ),
      rightIcon: rightIcon,
      title: 'Payments',
      description: 'Manage withdrawal accounts & enable direct checkout',
      onPress: () => navigation?.navigate('PaymentSettings'),
      showBorder: false,
    },
  ];

  const storeCover = store?.hero_image ? store?.hero_image : defaultCover;
  
  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Store Settings',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <ScrollView className="flex-1">
        <ImageBackground
          source={{ uri: coverImage ?? storeCover }}
          className="h-[120] w-full"
          resizeMode={'cover'}>
          <View className="absolute bottom-10 right-20">
            <Can data={{ permission: SCOPES.SETTINGS.UPDATE_STORE_DETAILS }}>
              {isAllowed => (
                <UploadImageBtn
                  setImageUrl={url => {
                    if (url !== null && isAllowed) saveImages(url, ImageTypes.HERO_IMAGE);
                  }}
                  setTempImage={i => isAllowed && setCoverImage(i)}
                  customBtn={(pickImage, isUploading) => {
                    return (
                      <WhiteCardBtn
                        disabled={isUploading || !isAllowed}
                        onPress={pickImage}
                        style={{ opacity: isAllowed ? 1 : 0.5 }}
                        leftIcon={
                          <View className="mr-5">
                            {isUploading ? (
                              <ActivityIndicator size={'small'} color={colors.primary.main} />
                            ) : (
                              <Gallery color={colors.primary.main} size={wp(14)} />
                            )}
                          </View>
                        }>
                        Change cover image
                      </WhiteCardBtn>
                    );
                  }}
                />
              )}
            </Can>
          </View>
        </ImageBackground>
        <View className={'flex-1 justify-start px-20 -mt-25'}>
          <Row className="justify-start self-start">
            {store?.logo ? (
              <CustomImage imageProps={{ source: { uri: logo ?? store.logo } }} className="w-50 h-50 rounded-full" />
            ) : (
              <CustomerInitial classes={'w-50 h-50'} initial={store?.name[0] ?? ''} textProps={{ fontSize: 22 }} />
            )}
            <Can data={{ permission: SCOPES.SETTINGS.UPDATE_STORE_DETAILS }}>
              {isAllowed => (
                <UploadImageBtn
                  setImageUrl={url => {
                    if (url !== null && isAllowed) saveImages(url, ImageTypes.LOGO);
                  }}
                  setTempImage={i => isAllowed && setLogo(i)}
                  customBtn={(pickImage, isUploading) => {
                    return (
                      <Pressable
                        onPress={pickImage}
                        disabled={isUploading || !isAllowed}
                        style={{ opacity: isAllowed ? 1 : 0.5 }}
                        className="items-center justify-center rounded-full bg-white p-5 absolute bottom-0 -right-10">
                        {isUploading ? (
                          <ActivityIndicator size={'small'} color={colors.primary.main} />
                        ) : (
                          <Edit2 size={wp(14)} color={colors.primary.main} />
                        )}
                      </Pressable>
                    );
                  }}
                />
              )}
            </Can>
          </Row>
          <BaseText fontSize={22} type={'heading'} weight={'bold'} classes={'mt-15'}>
            {store?.name}
          </BaseText>
          <Row className={'flex-1 justify-start'}>
            <View>
              <BaseText fontSize={12} weight={'medium'} classes={'text-black-muted'}>
                {storeLink}
              </BaseText>
            </View>
            <Pressable onPress={() => copyToClipboard(storeLink)}>
              <CircledIcon className={'bg-grey-bgOne rounded-[5px] p-5 ml-8'}>
                <Copy size={wp(10)} color={colors?.primary.main} />
              </CircledIcon>
            </Pressable>
          </Row>
        </View>
        <View className="px-20 mb-40">
          {/* <PermissionTest /> */}
          {/* <RoleSwitcher /> */}
          {settings?.map((item, idx) => (
            <ListItemCard
              key={idx}
              leftElement={item?.leftIcon}
              rightElement={item?.rightIcon}
              className="py-24"
              titleProps={{ fontSize: 15, weight: 'bold', type: 'heading' }}
              title={item?.title}
              description={item?.description}
              descriptionProps={{ fontSize: 12, weight: 'regular' }}
              onPress={item?.onPress}
              showBorder={item?.showBorder !== undefined ? item?.showBorder : true}
              permissionData={item.permissionData}
            />
          ))}
        </View>
      </ScrollView>
      <Can data={{ permission: SCOPES.SETTINGS.UPDATE_STORE_DETAILS }}>
        {isAllowed => isAllowed && (
          <EditStoreLinkModal show={modals.edit_store_link} toggle={() => toggleModal('edit_store_link')} />
        )}
      </Can>
    </DashboardLayout>
  );
}

export default StoreSettings;
