import {
  GET_STORE_ANALYTICS,
  GetStoreAnalyticsParams,
  GET_STORE_CREDITS,
  GET_ORDERS,
  GET_STORE_TOP_PRODUCTS,
  GetStoreTopProductsParams,
  GetOrdersParams,
} from 'catlog-shared'; //todo: @silas
import { Copy, Profile2User, Shop } from 'iconsax-react-native/src';
import { useEffect, useMemo, useState } from 'react';
import { Linking, RefreshControl, ScrollView, View } from 'react-native';
import { useApi } from 'src/hooks/use-api';

import { OrdersResponse } from '../orders/list';

import { copyToClipboard, delay, getCommunityLink, openLinkInBrowser, wp } from '@/assets/utils/js';
import HomeAnalyicsCards from '@/components/home/<USER>';
import HomeNotificationCard from '@/components/home/<USER>';
import DashboardOrdersAndTopProducts, { TopProductResponse } from '@/components/home/<USER>';
import { AreaGraph } from '@/components/ui';
import ScrollableActionPills from '@/components/ui/buttons/scrollable-action-pills';
import Container from '@/components/ui/container';
import { getFilter } from '@/components/ui/graph/area-graph';
import { ArrowUpRight, MoreHorizontal } from '@/components/ui/icons';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import useAuthContext from '@/contexts/auth/auth-context';
import useStatusbar from '@/hooks/use-statusbar';
import colors from '@/theme/colors';
import useNotificationSetup from 'src/hooks/use-notification-setup';
import useRouteParams from 'src/hooks/use-route-params';
import AcceptInviteModal from 'src/components/auth/accept-invite-modal';
import useModals from 'src/hooks/use-modals';
import { TimeRange } from 'src/@types/utils';

// export enum TimeRange {
//   TODAY = 'Today',
//   THIS_WEEK = 'This Week',
//   LAST_WEEK = 'Last Week',
//   LAST_30_DAYS = 'Last 30 Days',
//   THIS_YEAR = 'This Year',
//   ALL_TIME = 'All Time',
//   CUSTOM = 'Custom',
// }

const Home = () => {
  const [range, setRange] = useState(TimeRange.THIS_YEAR);

  const { store, storeLink, storeId, user } = useAuthContext();
  const { setStatusBar } = useStatusbar();
  const { modals, toggleModal } = useModals(['inviteModal']);
  setStatusBar('dark', 'transparent', true);

  const param = useRouteParams<'Home'>();

  const hasInvite = Boolean(param?.id);

  useEffect(() => {
    (async () => {
      if (hasInvite) {
        await delay(600);
        toggleModal('inviteModal');
      }
    })();
  }, [param]);

  useNotificationSetup();

  const analyticsRange = useMemo(() => {
    return getFilter(range);
  }, [range]);

  const storeAnalyticsRequest = useApi<GetStoreAnalyticsParams>(
    {
      apiFunction: GET_STORE_ANALYTICS,
      method: 'GET',
      key: 'get-store-analytics',
    },
    { id: store?.id, filter: { ...analyticsRange }, is_mobile: true },
  );

  const catlogCreditRequest = useApi({
    key: 'get-store-credit',
    apiFunction: GET_STORE_CREDITS,
    method: 'GET',
  });

  const getLatestOrdersReq = useApi<GetOrdersParams, OrdersResponse>(
    {
      apiFunction: GET_ORDERS,
      method: 'GET',
      key: `'get-latest-orders'${store?.id}`,
    },
    {
      page: 1,
      per_page: 5,
      sort: 'DESC',
      filter: {},
    },
  );

  const getTopProductReq = useApi<GetStoreTopProductsParams, TopProductResponse>(
    {
      apiFunction: GET_STORE_TOP_PRODUCTS,
      method: 'GET',
      key: 'get-top-product',
    },
    { id: storeId! },
  );

  const statisticData = {
    total_orders: storeAnalyticsRequest?.response?.data?.total_orders ?? 0,
    total_payments: storeAnalyticsRequest?.response?.data?.total_payments ?? 0,
    total_visits: storeAnalyticsRequest?.response?.data?.total_visits ?? 0,
  };

  const trends = storeAnalyticsRequest?.response?.data?.trends ?? {
    total_orders: 0,
    total_payments: 0,
    total_visits: 0,
  };

  const graphRawData = storeAnalyticsRequest?.response?.data?.visits ?? [];

  const actionPills = [
    {
      LeftIcon: ({ ...props }) => <Shop {...props} />,
      title: 'Visit Store',
      showArrow: true,
      onPress: () => openLinkInBrowser(storeLink),
      addon: <ArrowUpRight currentColor={colors.black.secondary} strokeWidth={1.5} size={wp(18)} />,
    },
    {
      LeftIcon: ({ ...props }) => <Copy {...props} />,
      title: 'Copy Store Link',
      onPress: () => copyToClipboard(storeLink),
    },
    {
      LeftIcon: ({ ...props }) => <Profile2User {...props} />,
      title: 'Join Community',
      onPress: () => Linking.openURL(getCommunityLink(store?.country)),
    },
  ];

  const handleHomePullToRefresh = async () => {
    storeAnalyticsRequest.refetch();
    catlogCreditRequest.refetch();
    getLatestOrdersReq.refetch();
    getTopProductReq.refetch();
  };

  // Check if all critical requests failed
  const criticalError = storeAnalyticsRequest.error && getLatestOrdersReq.error && getTopProductReq.error;

  // Function to retry all requests
  const retryAllRequests = () => {
    storeAnalyticsRequest.refetch();
    catlogCreditRequest.refetch();
    getLatestOrdersReq.refetch();
    getTopProductReq.refetch();
  };
  const [visible, setVisible] = useState(false);

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-primary-pastel',
        pageTitle: store?.name ?? 'Bernadines XX',
        variant: HeaderVariants.ROOT_LEVEL,
      }}>
      <ScrollView
        nestedScrollEnabled
        refreshControl={
          <RefreshControl
            onRefresh={handleHomePullToRefresh}
            // refreshing={catlogCreditRequest.isReLoading || storeAnalyticsRequest.isReLoading}
            refreshing={false}
          />
        }>
        <HomeNotificationCard store={store} user={user} />

        <View className="flex-1 pt-20">
          <ScrollableActionPills pills={actionPills} title="Overview" />
          <Container className="mt-20 z-50 overflow-visible pb-45">
            <QueryErrorBoundary
              error={storeAnalyticsRequest.error}
              isLoading={storeAnalyticsRequest.isLoading}
              refetch={storeAnalyticsRequest.refetch}
              variant="section"
              errorTitle="Failed to load analytics">
              <HomeAnalyicsCards
                isLoading={storeAnalyticsRequest?.isLoading}
                statistics={statisticData}
                trends={trends}
                catlogCreditRequest={catlogCreditRequest}
              />
            </QueryErrorBoundary>
            <AreaGraph
              title="Total Store Visits"
              error={storeAnalyticsRequest.error}
              onPressRetry={storeAnalyticsRequest.refetch}
              rawData={[graphRawData]}
              range={range}
              setRange={setRange}
              className="mt-20 mb-20"
              isLoadingData={storeAnalyticsRequest?.isLoading}
              labels={{
                topPrefix: 'Total Store Visits',
              }}
            />

            <QueryErrorBoundary
              error={getLatestOrdersReq.error || getTopProductReq.error}
              isLoading={getLatestOrdersReq.isLoading || getTopProductReq.isLoading}
              refetch={() => {
                getLatestOrdersReq.refetch();
                getTopProductReq.refetch();
              }}
              variant="section"
              errorTitle="Failed to load products & orders">
              <DashboardOrdersAndTopProducts
                getLatestOrdersReq={getLatestOrdersReq}
                getTopProductReq={getTopProductReq}
              />
            </QueryErrorBoundary>
          </Container>
        </View>
      </ScrollView>
      {hasInvite && (
        <AcceptInviteModal
          isVisible={modals.inviteModal}
          closeModal={() => toggleModal('inviteModal', false)}
          inviteDetail={param}
        />
      )}
    </DashboardLayout>
  );
};

export default Home;
