import { <PERSON><PERSON>View, StyleSheet, View } from 'react-native';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import Container from '@/components/ui/container';
import { useNavigation } from '@react-navigation/native';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import { BaseText, CircledIcon, Row, SelectionPill } from '@/components/ui';
import useAuthContext from 'src/contexts/auth/auth-context';
import { hp, timeToClock, wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import { Fragment, useEffect, useState } from 'react';
import Toast from 'react-native-toast-message';
import { <PERSON><PERSON><PERSON>, Cursor, useBlurOn<PERSON>ulfill, useClearByFocusCell } from 'react-native-confirmation-code-field';
import useRouteParams from 'src/hooks/use-route-params';
import { VERIFICATION_TYPE } from 'src/@types/utils';
import { useApi } from 'src/hooks/use-api';
import cx from 'classnames';
import Pressable from '@/components/ui/base/pressable';
import { ArrowUpRight } from '@/components/ui/icons';
import { SEND_VERIFICATION_TOKENS, VERIFY_TOKENS } from 'catlog-shared';

const CELL_COUNT = 6;
const RESEND_DELAY_MINS = 6;

const Verify = () => {
  const [value, setValue] = useState('');
  const [lastSendTime, setLastSendTime] = useState<number>(0);
  const { user, updateUser } = useAuthContext();
  const [nextResendTime, setNextResendTime] = useState<number>(0);
  const navigation = useNavigation();

  const params = useRouteParams<'Verify'>();
  const verificationType = params?.type;

  const sendVerificationRequest = useApi(
    {
      key: 'send-verification',
      apiFunction: SEND_VERIFICATION_TOKENS,
      method: 'POST',
    },
    {
      email: verificationType === VERIFICATION_TYPE.EMAIL ? true : false,
      phone: verificationType === VERIFICATION_TYPE.PHONE ? true : false,
    },
  );

  const verifyTokenRequest = useApi(
    {
      key: 'verify-token',
      apiFunction: VERIFY_TOKENS,
      method: 'POST',
    },
    {
      ...(verificationType === VERIFICATION_TYPE.PHONE && { phone: '' }),
      ...(verificationType === VERIFICATION_TYPE.EMAIL && { email: '' }),
    },
  );

  const ref = useBlurOnFulfill({ value, cellCount: CELL_COUNT });
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });

  const handleRequestOTP = async () => {
    const [response, error] = await sendVerificationRequest.makeRequest({
      email: verificationType === VERIFICATION_TYPE.EMAIL ? true : false,
      phone: verificationType === VERIFICATION_TYPE.PHONE ? true : false,
    });

    if (response) {
      setLastSendTime(Date.now());
    }
  };

  useEffect(() => {
    handleRequestOTP();
  }, []);

  useEffect(() => {
    if (!lastSendTime && lastSendTime !== 0) {
      return;
    }

    const interval = setInterval(() => {
      const nextResendTime = RESEND_DELAY_MINS * 60000 - (Date.now() - lastSendTime);
      setNextResendTime(nextResendTime);

      if (nextResendTime < 0) {
        clearInterval(interval);
      }
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, [lastSendTime]);

  const disableResend = sendVerificationRequest.isLoading || nextResendTime > 0;
  const handleVerifyOtp = async () => {
    const [response, error] = await verifyTokenRequest.makeRequest({
      ...(verificationType === VERIFICATION_TYPE.PHONE && { phone: value }),
      ...(verificationType === VERIFICATION_TYPE.EMAIL && { email: value }),
    });

    if (response) {
      const userData = {} as any;
      const name = verificationType === VERIFICATION_TYPE.PHONE ? 'phone' : 'email';

      userData[`${name}_verified`] = true;
      updateUser(userData);

      navigation.goBack();
    }

    if (error) {
      Toast.show({ type: 'error', text1: error?.body?.message });
    }
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentOrange-pastel',
        pageTitle: 'Verification',
        variant: HeaderVariants.SUB_LEVEL,
      }}
      isLoading={sendVerificationRequest.isLoading}>
      <AvoidKeyboard>
        <ScrollView>
          <ScreenInfoHeader
            iconElement={<CircledIcon />}
            colorPalette={ColorPaletteType.ORANGE}
            pageTitleTop={'Verify your'}
            pageTitleBottom={verificationType === VERIFICATION_TYPE.EMAIL ? 'Email' : 'Phone Number'}
          />
          <Container className={'mt-24'}>
            <View>
              <BaseText type={'heading'} fontSize={15}>
                Email Verification
              </BaseText>
              <BaseText fontSize={12} classes="text-black-muted mt-2">
                We’ve sent a 6 digit code to{' '}
                <BaseText fontSize={12} weight={'medium'}>
                  {verificationType === VERIFICATION_TYPE.EMAIL ? user?.email : user?.phone}
                </BaseText>
              </BaseText>
            </View>
            <View className={'mt-20'}>
              <CodeField
                ref={ref}
                value={value}
                onChangeText={setValue}
                cellCount={CELL_COUNT}
                autoFocus
                keyboardType="number-pad"
                textContentType="oneTimeCode"
                testID="my-code-input"
                renderCell={({ index, symbol, isFocused }) => (
                  <Fragment key={index}>
                    <View
                      className={cx(
                        'border w-[45px] h-[52px] rounded-[10px] items-center justify-center',
                        { 'border-primary-main': isFocused },
                        { 'border-grey-border': !isFocused },
                      )}>
                      <BaseText
                        fontSize={22}
                        lineHeight={32}
                        weight={'semiBold'}
                        classes={'text-secondary'}
                        onLayout={getCellOnLayoutHandler(index)}>
                        {symbol || (isFocused ? <Cursor /> : null)}
                      </BaseText>
                    </View>
                    {index === 2 && <View className="h-1 w-10 bg-grey-muted self-center" />}
                  </Fragment>
                )}
              />
              <BaseText fontSize={12} weight={'semiBold'} classes={'text-primary-main mt-12'}>
                Wrong email? Change Email
              </BaseText>
            </View>
          </Container>
        </ScrollView>
        {nextResendTime > 0 && (
          <BaseText classes="text-center my-15 text-grey-muted">Resend in {timeToClock(nextResendTime)}</BaseText>
        )}
        {nextResendTime < 0 && (
          <Pressable
            className={cx('self-center mb-12 mt-6 py-10 px-15 rounded-full bg-grey-bgOne', {
              'opacity-50': disableResend,
            })}
            onPress={handleRequestOTP}
            disabled={disableResend}>
            <Row classes="justify-start">
              <BaseText fontSize={12} weight={'semiBold'} classes={'text-primary-main'}>
                Resend Code
              </BaseText>
              <ArrowUpRight size={wp(14)} currentColor={colors.primary.main} />
            </Row>
          </Pressable>
        )}
      </AvoidKeyboard>
      <FixedBtnFooter
        buttons={[
          {
            text: 'Proceed',
            onPress: handleVerifyOtp,
            isLoading: verifyTokenRequest.isLoading,
          },
        ]}
      />
    </DashboardLayout>
  );
};

export default Verify;
