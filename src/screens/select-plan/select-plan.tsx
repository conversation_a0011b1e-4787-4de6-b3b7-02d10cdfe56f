import { Gift, Tag2 } from 'iconsax-react-native/src';
import React, { useState } from 'react';
import { ScrollView, View, Image } from 'react-native';
import { wp } from '@/assets/utils/js';
import ConfirmPlanModal from '@/components/select-plan/confirm-plan-modal';
import PlanCard from '@/components/select-plan/plan-card';
import PlanFeatureModal from '@/components/select-plan/plan-feature-modal';
import SelectPlanDurationModal from '@/components/select-plan/select-plan-duration-modal';
import { BaseText, CircledIcon } from '@/components/ui';
import Button from '@/components/ui/buttons/button';
import Container from '@/components/ui/container';
import AuthLayout from '@/components/ui/layouts/auth-layout';
import InfoModal from '@/components/ui/modals/info-modal';
import Row from '@/components/ui/row';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import useStatusbar from '@/hooks/use-statusbar';
import colors from '@/theme/colors';

const SelectPlan = () => {
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [showPlanFeatureModal, setShowPlanFeatureModal] = useState(false);
  const [showSelectDurationModal, setShowSelectDurationModal] = useState(false);
  const [showConfirmPlanModal, setShowConfirmPlanModal] = useState(false);
  const [showConfirmDurationModal, setShowConfirmDurationModal] = useState(false);
  const [selectedDuration, setSelectedDuration] = useState<string | null>(null);

  const { setStatusBar } = useStatusbar();
  setStatusBar('dark', 'transparent', true);

  const handleOpenSelectPlanDuration = () => {
    setShowConfirmPlanModal(false);
    setTimeout(() => setShowSelectDurationModal(true), 650);
  };

  const handleOpenConfirmPlanDuration = () => {
    setShowSelectDurationModal(false);
    setTimeout(() => setShowConfirmDurationModal(true), 650);
  };

  return (
    <AuthLayout headerProps={{ backgrounColor: 'bg-accentRed-pastel', pageTitle: 'Setup busiess' }}>
      <ScrollView>
        <ScreenInfoHeader
          iconElement={
            <Image
              source={require('@/assets/images/giftBox.png')}
              resizeMode={'contain'}
              className="w-[80px] h-[60px]"
            />
          }
          colorPalette={ColorPaletteType.RED}
          pageTitleTop={'Final Step'}
          pageTitleBottom={'Select a Plan'}
        />
        <Container className="pt-24 pb-24">
          <Row classes="items-start p-12 bg-grey-bgOne rounded-[10px]">
            <CircledIcon iconBg="bg-white">
              <Gift variant={'Bold'} color={colors.accentRed.main} size={wp(16)} />
            </CircledIcon>
            <View className="flex-1 mx-10">
              <BaseText fontSize={12}>
                If you select a paid plan today, you get the first 14 days free. No card required!
              </BaseText>
              <BaseText fontSize={12} classes="mt-5">
                You also have NGN 500 in referral credits
              </BaseText>
            </View>
          </Row>
          <View>
            {plans.map(item => (
              <PlanCard
                key={item?.planName!}
                onPress={() => setSelectedItem(item?.planName)}
                onPressPlansFeatures={() => setShowPlanFeatureModal(true)}
                selected={selectedItem === item?.planName}
                planName={item?.planName!}
                pricing={item?.pricing!}
                duration={item?.duration!}
                greatChoice={item.isRecommened!}
                className="mt-15"
                description={item?.description!}
              />
            ))}
          </View>
        </Container>
      </ScrollView>
      <View className="pt-10 px-20 border-t border-t-grey-border">
        <Button
          onPress={() => setShowConfirmPlanModal(true)}
          disabled={selectedItem! !== null ? false : true}
          text={'Select Plan'}
        />
      </View>
      <ConfirmPlanModal
        closeModal={() => setShowConfirmPlanModal(false)}
        isVisible={showConfirmPlanModal}
        planName={selectedItem!}
        onPressButton={handleOpenSelectPlanDuration}
      />
      <SelectPlanDurationModal
        closeModal={() => setShowSelectDurationModal(false)}
        isVisible={showSelectDurationModal}
        planName={selectedItem!}
        selectedDuration={selectedDuration!}
        onDurationSelection={value => setSelectedDuration(value)}
        onPressButton={handleOpenConfirmPlanDuration}
      />
      <InfoModal
        closeModal={() => setShowConfirmDurationModal(false)}
        isVisible={showConfirmDurationModal}
        buttonText={'Yes Subscribe'}
        modalImage={
          <CircledIcon iconBg={'bg-primary-main'} className="self-center mt-15 p-15">
            <Tag2 variant={'Bold'} color={colors.white} size={wp(30)} />
          </CircledIcon>
        }
        title={'Subscribe to Business +\nMonthly Plan'}
        description={'You can try this plan free for 14 days, after that you’ll be charged NGN 4,000.00 every 30 days.'}
      />
      <PlanFeatureModal isVisible={showPlanFeatureModal} closeModal={() => setShowPlanFeatureModal(false)} />
      {/* <InfoModal
        closeModal={() => setShowConfirmDurationModal(false)}
        isVisible={showConfirmDurationModal}
        buttonText={'Yes Subscribe'}
        modalImage={
          <CircledIcon iconBg={'bg-primary-main'} className="self-center mt-15 p-15">
            <Tag2 variant={'Bold'} color={colors.white} size={wp(30)} />
          </CircledIcon>
        }
        title={'Subscribe to Business +\nMonthly Plan'}
        description={'You can try this plan free for 14 days, after that you’ll be charged NGN 4,000.00 every 30 days.'}
      /> */}
    </AuthLayout>
  );
};

const plans = [
  {
    planName: 'Starter',
    pricing: 'Free',
    duration: 'forever',
    description: 'Upload up to 10 products',
  },
  {
    planName: 'Basic',
    pricing: 'NGN 1,500',
    duration: 'monthly',
    description: 'Upload up to 100 products',
    isRecommened: true,
  },
  {
    planName: 'Business +',
    pricing: 'NGN 4,000',
    duration: 'monthly',
    description: 'Upload multiple products',
  },
];

export default SelectPlan;
