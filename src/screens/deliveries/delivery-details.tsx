import { ActivityIndicator, Linking, Sc<PERSON>View, View } from 'react-native';
import colors from '@/theme/colors';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { ArrowUpRight, ChevronDown, ChevronUp } from '@/components/ui/icons';
import { hp, openLinkInBrowser, toCurrency, wp } from '@/assets/utils/js';
import React, { ReactNode } from 'react';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import {
  Box,
  Box1,
  Call,
  Copy,
  Link21,
  Location,
  Map1,
  Money,
  Profile,
  Receipt2,
  Receipt21,
  Whatsapp,
} from 'iconsax-react-native/src';
import Pressable from '@/components/ui/base/pressable';
import SectionContainer from '@/components/ui/section-container';
import { ButtonVariant, TextColor } from '@/components/ui/buttons/button';
import Separator from '@/components/ui/others/separator';
import InfoRow from '@/components/ui/others/info-row';
import Accordion from '@/components/ui/others/accordion';
import cx from 'classnames';
import useRouteParams from '@/hooks/use-route-params';
import { ResponseWithoutPagination, useApi } from '@/hooks/use-api';
import useModals from 'src/hooks/use-modals';
import CustomImage from '@/components/ui/others/custom-image';
import useAuthContext from 'src/contexts/auth/auth-context';
import ProductDetailSection from '@/components/deliveries/delivery-details/product-detail-section';
import PickUpAddressSection from '@/components/deliveries/delivery-details/pick-up-address-section';
import DropOffAddressSection from '@/components/deliveries/delivery-details/drop-off-address-section';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { useNavigation } from '@react-navigation/native';
import { OrderInfoSkeletonLoader } from '../orders/order-info';
import {
  OrderInterface,
  GET_DELIVERY,
  GetDeliveryParams,
  IDelivery,
  DELIVERY_STATUSES,
  PAYMENT_TYPES,
} from 'catlog-shared';
import PaymentsWidget, { PAYMENT_WIDGET_ADDONS } from 'src/components/payments/payments-widget';

const DeliveryDetails = () => {
  const { store } = useAuthContext();
  const params = useRouteParams<'DeliveryDetails'>();
  const { modals, toggleModal } = useModals(['customerInfo', 'payments']);

  const navigation = useNavigation();

  // const changeStatusRequest = useApi({ apiFunction: UPDATE_ORDER, method: 'PUT', key: 'update-delivery' });
  const getDeliveryRequest = useApi<GetDeliveryParams, ResponseWithoutPagination<IDelivery>>(
    {
      apiFunction: GET_DELIVERY,
      method: 'GET',
      key: 'get-delivery',
    },
    {
      id: params?.id!,
    },
  );

  const delivery = getDeliveryRequest?.response?.data ?? ({} as IDelivery);

  const routeToInitiateDelivery = async () => {
    if (getDeliveryRequest?.response?.data) {
      navigation.navigate('InitiateDelivery', { deliveryData: getDeliveryRequest?.response?.data });
    }
  };

  const handleNotifyCustomer = () => {};

  const isDraft = delivery?.status === DELIVERY_STATUSES.DRAFT;

  const buttons = [
    {
      text: 'Notify Customer',
      onPress: handleNotifyCustomer,
      variant: ButtonVariant.LIGHT,
      // isLoading: changeStatusRequest.isLoading,
    },
    ...(delivery?.status !== DELIVERY_STATUSES.CANCELLED
      ? [
          {
            text: isDraft ? 'Finalize Delivery' : 'Track Delivery',
            onPress: () => (isDraft ? routeToInitiateDelivery() : openLinkInBrowser(delivery?.tracking_url!)),
            variant: isDraft ? ButtonVariant.SUCCESS : ButtonVariant.PRIMARY,
          },
        ]
      : []),
    ...(delivery?.status === DELIVERY_STATUSES.CANCELLED
      ? [
          {
            text: 'Retry Delivery',
            onPress: () => {},
            // isLoading: changeStatusRequest.isLoading,
          },
        ]
      : []),
  ];

  const deliveryStatusReplacement = {
    [DELIVERY_STATUSES.PENDING]: StatusType.WARN,
    [DELIVERY_STATUSES.DRAFT]: StatusType.DEFAULT,
    [DELIVERY_STATUSES.COMPLETED]: StatusType.SUCCESS,
    [DELIVERY_STATUSES.CANCELLED]: StatusType.DANGER,
    [DELIVERY_STATUSES.IN_TRANSIT]: StatusType.DANGER,
    [DELIVERY_STATUSES.PICKED_UP]: StatusType.DANGER,
    [DELIVERY_STATUSES.CONFIRMED]: StatusType.DANGER,
  };

  const isLoading = getDeliveryRequest.isLoading;

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Delivery Details',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <ScrollView contentContainerStyle={{ paddingBottom: hp(20) }}>
        {getDeliveryRequest.isLoading && <OrderInfoSkeletonLoader />}
        {!getDeliveryRequest.isLoading && (
          <>
            <View className="px-20 py-30 items-center bg-accentYellow-pastel">
              <StatusPill
                title={delivery.status}
                className="bg-white"
                statusType={deliveryStatusReplacement[delivery.status]}
              />
              <BaseText fontSize={22} classes="mt-5 leading-[30px] text-center" type="heading">
                Delivery {delivery?.id}
              </BaseText>
            </View>
            {delivery.courier?.courier_name && (
              <>
                <Row className="mt-24 px-20 items-start">
                  {delivery.courier?.courier_image ? (
                    <CustomImage
                      imageProps={{ source: { uri: delivery.courier?.courier_image }, contentFit: 'cover' }}
                      className="h-40 w-40 rounded-full"
                    />
                  ) : (
                    <CircledIcon style={{ backgroundColor: colors.accentOrange.pastel }}>
                      <Box variant={'Bold'} size={wp(20)} color={colors.accentOrange.main} />
                    </CircledIcon>
                  )}
                  <View className={'flex-1 mx-12'}>
                    <BaseText fontSize={18} type={'heading'} classes="capitalize">
                      {delivery?.courier?.courier_name}
                    </BaseText>
                    <BaseText classes="text-black-placeholder mt-2">Tracking Code: {delivery?.tracking_code}</BaseText>
                  </View>
                </Row>
                <Separator />
              </>
            )}
            <Row className={cx('px-20 items-start', { 'mt-24': !delivery.courier?.courier_name })}>
              <View className="flex-1">
                <BaseText fontSize={15} type={'heading'} classes="capitalize">
                  {toCurrency(delivery?.delivery_amount ?? 0, store?.currencies?.default)}
                </BaseText>
                <BaseText fontSize={12} classes="text-black-placeholder mt-2">
                  Delivery Fee
                </BaseText>
              </View>
              <CircledIcon style={{ backgroundColor: colors.accentRed.main }}>
                <Money variant={'Bold'} size={wp(20)} color={colors.white} />
              </CircledIcon>
            </Row>
            <Separator />
            <ProductDetailSection delivery={delivery} />
            {delivery?.sender_address && <PickUpAddressSection delivery={delivery} />}
            {delivery?.receiver_address && <DropOffAddressSection delivery={delivery} />}
          </>
        )}
      </ScrollView>
      {delivery.status !== DELIVERY_STATUSES.COMPLETED && isLoading === false && <FixedBtnFooter buttons={buttons} />}
      <PaymentsWidget
        onComplete={d => console.log(d)}
        data={{
          delivery: delivery,
          addon: PAYMENT_WIDGET_ADDONS.DELIVERY,
          paymentType: PAYMENT_TYPES.DELIVERY,
          successMessage: 'Your payment for this delivery was successful. Click continue to get delivery info',
        }}
        show={modals.payments}
        toggle={() => toggleModal('payments')}
      />
    </DashboardLayout>
  );
};

export default DeliveryDetails;
