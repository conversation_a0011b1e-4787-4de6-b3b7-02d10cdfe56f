import { useMemo, useRef, useState } from 'react';
import colors from '@/theme/colors';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { cx, wp, Yup } from '@/assets/utils/js';
import { BaseText, CircledIcon, Container, Row, WhiteCardBtn } from '@/components/ui';
import { ArrowLeft, ArrowRight, Money, MoneyChange, Note1 } from 'iconsax-react-native/src';
import useRouteParams from '@/hooks/use-route-params';
import useModals from 'src/hooks/use-modals';
import ScreenInfoHeader, { ColorPaletteType } from 'src/components/ui/screen-info-header';
import useSteps from 'src/hooks/use-steps';
import Pressable from 'src/components/ui/base/pressable';
import Input from 'src/components/ui/inputs/input';
import { ScrollView, View } from 'react-native';
import { ChevronDown } from 'src/components/ui/icons';
import SelectProductStep from 'src/components/orders/bulk-update/select-product-step';
import { useApi } from 'src/hooks/use-api';
import { ProductsResponse } from '../storefront';
import useAuthContext from 'src/contexts/auth/auth-context';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import { useNavigation } from '@react-navigation/native';
import { AutoUpdateFormProps, BulkUpdateMethod, UPDATE_PRICE_STEPS } from '../types';
import UpdatePriceStep from 'src/components/orders/bulk-update/update-price-step';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import Toast from 'react-native-toast-message';
import { useFormik } from 'node_modules/formik/dist';
import { BULK_UPDATE_ITEMS, BulkUpdateFormItem, GET_ITEMS, GetItemsParams, ProductItemInterface } from 'catlog-shared';
import useBulkUpdate from 'src/hooks/use-bulk-update';

const BATCH_SIZE = 10;

const PriceUpdate = () => {
  const [method, setMethod] = useState<BulkUpdateMethod>(BulkUpdateMethod.MANUAL);

  const scrollRef = useRef<ScrollView>(null);

  const {
    getProductsRequest,
    bulkUpdateRequest,
    changeBatch,
    handleItemsSelect,
    store,
    handleFormUpdate,
    form,
    setForm,
    batchData,
    totalBatches,
    currentBatch,
    selected,
    completeUpdate,
  } = useBulkUpdate(scrollRef);

  const params = useRouteParams<'PriceUpdate'>();
  const formSteps = useSteps(Object.values(UPDATE_PRICE_STEPS), 0);
  const { step, isActive, stepIndex, next, previous, canNext, canPrevious, steps, changeStep } = formSteps;

  const autoUpdateForm = useFormik<AutoUpdateFormProps>({
    initialValues: {
      price_action: 'increase',
      update_method: 'percentage',
      percentage: 0,
      amount: 0,
    },
    onSubmit: async values => {
      const bulkUpdateCopy = { ...form };
      selected.forEach(i => {
        bulkUpdateCopy[i.id] = getUpdatedItem(i, values, true);
      });
      // setForm(bulkUpdateCopy);

      await completeUpdate(bulkUpdateCopy);
      // await completeUpdate();
    },
    validationSchema,
  });

  const calcNewPrice = (old_price: number, values: AutoUpdateFormProps) => {
    let deltaPrice = null;

    if (values.update_method === 'percentage') {
      deltaPrice = old_price * (autoUpdateForm.values.percentage / 100);
    } else if (values.update_method === 'fixed') {
      deltaPrice = values.amount;
    }
    const newPrice =
      values.price_action === 'decrease'
        ? Number(old_price) - Number(deltaPrice)
        : Number(old_price) + Number(deltaPrice);
    return newPrice > 0 ? newPrice : old_price;
  };

  function getUpdatedItem(item: BulkUpdateFormItem, values: AutoUpdateFormProps, computeVariants = false) {
    const itemCopy = { ...item };
    itemCopy.price = calcNewPrice(itemCopy.old_price, values);
    if (itemCopy.variants?.options?.length > 0 && computeVariants)
      itemCopy.variants.options = itemCopy.variants.options.map(o => ({
        ...o,
        price: calcNewPrice(o.price, values),
      }));

    return itemCopy;
  }

  const navigation = useNavigation();

  const onPressBack = () => {
    if (canPrevious) {
      previous();
    } else {
      navigation.goBack();
    }
  };

  const disableBtn = useMemo(() => {
    if (step === UPDATE_PRICE_STEPS.SELECT_PRODUCT) {
      if (selected.length < 1) return true;
    }
    return false;
  }, [step, selected]);

  const onPressContinue = () => {
    if (isActive(UPDATE_PRICE_STEPS.INCREASE_PRICE_FORM)) {
      method === BulkUpdateMethod.MANUAL ? completeUpdate() : autoUpdateForm.submitForm();
    } else {
      next();
    }
  };

  const buttons = [
    ...(method === BulkUpdateMethod.MANUAL &&
    isActive(UPDATE_PRICE_STEPS.INCREASE_PRICE_FORM) &&
    !bulkUpdateRequest.isLoading
      ? [
          {
            text: 'Go Back',
            onPress: () => changeBatch('-'),
            disabled: currentBatch === 1,
            variant: ButtonVariant.LIGHT,
          },
        ]
      : []),
    ...(method === BulkUpdateMethod.AUTO || totalBatches === currentBatch || isActive(UPDATE_PRICE_STEPS.SELECT_PRODUCT)
      ? [{ text: 'Continue', onPress: onPressContinue, disabled: disableBtn, isLoading: bulkUpdateRequest.isLoading }]
      : [
          {
            text: 'Next Batch',
            onPress: () => changeBatch('+'),
            disabled: currentBatch === totalBatches,
          },
        ]),
    ,
  ];

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Bulk Update Prices',
        variant: HeaderVariants.SUB_LEVEL,
        onBackPress: onPressBack,
      }}>
      <ScrollView keyboardShouldPersistTaps={'handled'} ref={scrollRef}>
        {!isActive(UPDATE_PRICE_STEPS.SUCCESS) && (
          <ScreenInfoHeader
            colorPalette={ColorPaletteType.YELLOW}
            isTextFollowPalette={false}
            iconElement={
              <CircledIcon className="bg-accentYellow-main p-15">
                <MoneyChange variant={'Bold'} color={colors.white} size={wp(30)} />
              </CircledIcon>
            }
            pageTitleTop={'Update Prices'}
            pageTitleBottom={'For Multiple Products'}
          />
        )}
        <Container className={'mt-20'}>
          <View style={{ display: isActive(UPDATE_PRICE_STEPS.SELECT_PRODUCT) ? 'flex' : 'none' }}>
            <SelectProductStep
              selectedItems={selected.map(i => i.id)}
              {...{ method, handleItemsSelect, getProductsRequest, setMethod }}
            />
          </View>
          <View style={{ display: isActive(UPDATE_PRICE_STEPS.INCREASE_PRICE_FORM) ? 'flex' : 'none' }}>
            <UpdatePriceStep
              {...{
                selected,
                method,
                handleFormUpdate,
                batchData,
                autoUpdateForm,
                calcNewPrice,
                getUpdatedItem,
              }}
              currency={store.currencies.default}
            />
          </View>
        </Container>
      </ScrollView>
      <FixedBtnFooter buttons={buttons} />
    </DashboardLayout>
  );
};

export default PriceUpdate;

const validationSchema = Yup.object().shape({
  percentage: Yup.number().test({
    name: 'percentage',
    test: (value: number | undefined, context) => {
      if (context.parent.update_method !== 'percentage') return true;
      return value !== undefined && value >= 1 && value <= 100;
    },
    message: 'Percentage must be between 1 and 100',
  }),
  amount: Yup.number().test({
    name: 'amount',
    test: (value: number | undefined, context) => {
      if (context.parent.update_method !== 'fixed') return true;
      return value !== undefined && value >= 1;
    },
    message: 'Amount must be at least 1',
  }),
  price_action: Yup.string().required('Price action is required'),
});
