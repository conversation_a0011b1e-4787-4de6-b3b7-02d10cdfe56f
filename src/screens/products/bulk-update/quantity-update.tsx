import { useMemo, useRef, useState } from 'react';
import colors from '@/theme/colors';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { cx, wp, Yup } from '@/assets/utils/js';
import { BaseText, CircledIcon, Container, Row, WhiteCardBtn } from '@/components/ui';
import { Add, ArrowLeft, ArrowRight, Money, MoneyChange, Note1 } from 'iconsax-react-native/src';
import useRouteParams from '@/hooks/use-route-params';
import useModals from 'src/hooks/use-modals';
import ScreenInfoHeader, { ColorPaletteType } from 'src/components/ui/screen-info-header';
import useSteps from 'src/hooks/use-steps';
import Pressable from 'src/components/ui/base/pressable';
import Input from 'src/components/ui/inputs/input';
import { ScrollView, View } from 'react-native';
import { ChevronDown } from 'src/components/ui/icons';
import SelectProductStep from 'src/components/orders/bulk-update/select-product-step';
import { useApi } from 'src/hooks/use-api';
import { ProductsResponse } from '../storefront';
import useAuthContext from 'src/contexts/auth/auth-context';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import { useNavigation } from '@react-navigation/native';
import { AutoUpdateFormProps, BulkUpdateMethod, UPDATE_QTY_STEPS } from '../types';
import UpdatePriceStep from 'src/components/orders/bulk-update/update-price-step';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import Toast from 'react-native-toast-message';
import { useFormik } from 'node_modules/formik/dist';
import { BULK_UPDATE_ITEMS, BulkUpdateFormItem, GET_ITEMS, GetItemsParams, ProductItemInterface } from 'catlog-shared';
import UpdateQtyStep from 'src/components/orders/bulk-update/update-qty-step';
import useBulkUpdate from 'src/hooks/use-bulk-update';

const BATCH_SIZE = 10;

const QuantityUpdate = () => {
  const params = useRouteParams<'QuantityUpdate'>();
  const { modals, toggleModal } = useModals(['customerInfo']);
  const [method, setMethod] = useState<BulkUpdateMethod>(BulkUpdateMethod.MANUAL);
  const scrollRef = useRef<ScrollView>(null);

  const {
    getProductsRequest,
    bulkUpdateRequest,
    changeBatch,
    handleItemsSelect,
    store,
    handleFormUpdate,
    form,
    setForm,
    batchData,
    totalBatches,
    currentBatch,
    selected,
    completeUpdate,
  } = useBulkUpdate(scrollRef);

  const formSteps = useSteps(Object.values(UPDATE_QTY_STEPS), 0);
  const { step, isActive, stepIndex, next, previous, canNext, canPrevious, steps, changeStep } = formSteps;

  const navigation = useNavigation();

  const onPressBack = () => {
    if (canPrevious) {
      previous();
    } else {
      navigation.goBack();
    }
  };

  const disableBtn = useMemo(() => {
    if (step === UPDATE_QTY_STEPS.SELECT_PRODUCT) {
      if (selected.length < 1) return true;
    }
    return false;
  }, [step, selected]);

  const onPressContinue = () => {
    if (isActive(UPDATE_QTY_STEPS.INCREASE_QTY_FORM)) {
      completeUpdate();
    } else {
      next();
    }
  };

  const buttons = [
    ...(method === BulkUpdateMethod.MANUAL &&
    isActive(UPDATE_QTY_STEPS.INCREASE_QTY_FORM) &&
    !bulkUpdateRequest.isLoading
      ? [
          {
            text: 'Go Back',
            onPress: () => changeBatch('-'),
            disabled: currentBatch === 1,
            variant: ButtonVariant.LIGHT,
          },
        ]
      : []),
    ...(method === BulkUpdateMethod.AUTO || totalBatches === currentBatch || isActive(UPDATE_QTY_STEPS.SELECT_PRODUCT)
      ? [{ text: 'Continue', onPress: onPressContinue, disabled: disableBtn, isLoading: bulkUpdateRequest.isLoading }]
      : [
          {
            text: 'Next Batch',
            onPress: () => changeBatch('+'),
            disabled: currentBatch === totalBatches,
          },
        ]),
    ,
  ];

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Bulk Update Qtys',
        variant: HeaderVariants.SUB_LEVEL,
        onBackPress: onPressBack,
      }}>
      <ScrollView keyboardShouldPersistTaps={'handled'} ref={scrollRef}>
        <ScreenInfoHeader
          colorPalette={ColorPaletteType.YELLOW}
          isTextFollowPalette={false}
          iconElement={
            <CircledIcon className="bg-accentYellow-main p-15">
              <Add variant={'Bold'} color={colors.white} size={wp(30)} />
            </CircledIcon>
          }
          pageTitleTop={'Update Quantities'}
          pageTitleBottom={'For Multiple Products'}
        />
        <Container className={'mt-20'}>
          <View style={{ display: isActive(UPDATE_QTY_STEPS.SELECT_PRODUCT) ? 'flex' : 'none' }}>
            <SelectProductStep
              selectedItems={selected.map(i => i.id)}
              isQuantity={true}
              {...{ method, handleItemsSelect, getProductsRequest, setMethod }}
            />
          </View>
          <View style={{ display: isActive(UPDATE_QTY_STEPS.INCREASE_QTY_FORM) ? 'flex' : 'none' }}>
            <UpdateQtyStep
              {...{
                selected,
                method,
                handleFormUpdate,
                batchData,
              }}
              currency={store.currencies.default}
            />
          </View>
        </Container>
      </ScrollView>
      <FixedBtnFooter buttons={buttons} />
    </DashboardLayout>
  );
};

export default QuantityUpdate;
