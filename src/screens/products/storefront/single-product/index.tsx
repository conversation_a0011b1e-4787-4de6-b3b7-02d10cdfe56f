import React, { useCallback, useMemo } from 'react';
import { ScrollView, View, Dimensions, RefreshControl } from 'react-native';
import colors from '@/theme/colors';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { ArrowUpRight, Plus } from '@/components/ui/icons';
import {
  copyToClipboard,
  delay,
  enumToHumanFriendly,
  getProductLink,
  hideLoader,
  hp,
  removeEmptyAndUndefined,
  showLoader,
  showSuccess,
  toCurrency,
  transformProductData,
  wp,
} from '@/assets/utils/js';
import { RouteProp, useFocusEffect, useNavigation, useRoute } from '@react-navigation/native';
import StatusPill from '@/components/ui/others/status-pill';
import { BaseText, CircledIcon, Row, ScrollableActionPills, WhiteCardBtn } from '@/components/ui';
import {
  Bag,
  Box1,
  Category,
  Danger,
  Edit2,
  Eye,
  Flash,
  HambergerMenu,
  Layer,
  Link21,
  Money,
  MoneyForbidden,
  Moneys,
  Notepad,
  Stacks,
  Stickynote,
  TransmitSquare,
} from 'iconsax-react-native/src';
import ProductInfoRow from '@/components/products/product-info-row';
import ProductImage from '@/components/products/storefront-products/product-image';
import Pressable from '@/components/ui/base/pressable';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { ButtonVariant, TextColor } from '@/components/ui/buttons/button';
import useStatusbar from '@/hooks/use-statusbar';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import { ResponseWithoutPagination, useApi } from '@/hooks/use-api';
import { RootStackParamList } from '@/@types/navigation';
import CustomImage from '@/components/ui/others/custom-image';
import useProductMutations from '@/hooks/use-product-mutations';
import Separator from '@/components/ui/others/separator';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import UploadImageBtn from '@/components/ui/buttons/upload-image-btn';
import { Fragment, useEffect, useState } from 'react';
import { Image } from 'src/@types/utils';
import ProductImagesSection from '@/components/products/product-images-section';
import useModals from 'src/hooks/use-modals';
import AccordionAnchor from '@/components/ui/others/accordion/accordion-anchor';
import InfoRow from '@/components/ui/others/info-row';
import ProductVariantCard from '@/components/products/product-variant-card';
import Shimmer from '@/components/ui/shimmer';
import Accordion from '@/components/ui/others/accordion';
import useAuthContext from 'src/contexts/auth/auth-context';
import dayjs from 'dayjs';
import { ProductItemInterface, GET_ITEM, GetItemParams, VariantForm, getItemThumbnail } from 'catlog-shared';
import VariantSectionCard from 'src/components/products/storefront-products/variant-section-card';
import useEventListener from 'src/hooks/use-event-emitter';
import { actionIsAllowed, SCOPES } from 'src/assets/utils/js/permissions';
import eventEmitter from 'src/assets/utils/js/event-emitter';
import AddProductOptionModal from 'src/components/products/create-products/add-product-option/add-product-option-modal';
import { Product } from 'src/components/products/create-products/types';

const { width } = Dimensions.get('window');

interface ProductResponse extends ResponseWithoutPagination<ProductItemInterface> {}

const ProductDetails = () => {
  const [variantValues, setVariantValues] = useState<{ name: string; value: string[] }[]>([]);
  const [product, setProduct] = useState<ProductItemInterface>({} as ProductItemInterface);
  const { params } = useRoute<RouteProp<RootStackParamList, 'ProductDetails'>>();
  const { modals, toggleModal } = useModals(['editProductOptions']);

  const { setStatusBar } = useStatusbar();

  const { userRole } = useAuthContext();

  setStatusBar('dark', 'transparent', true);

  const canEditProducts = actionIsAllowed({
    userRole,
    permission: SCOPES.PRODUCTS.EDIT_PRODUCTS,
  });

  useEventListener('productImage', p => {
    setProduct(p);
  });

  const navigation = useNavigation();

  const {
    handleOnPressDelete,
    handleOnPressFeature,
    handleProductShare,
    handleEditProduct,
    duplicateProduct,
    deleteLoading,
  } = useProductMutations();

  const getProductRequest = useApi<GetItemParams, ProductResponse>(
    {
      apiFunction: GET_ITEM,
      method: 'GET',
      key: 'get-product',
      onSuccess: response => {
        setProduct(response?.data);
      },
    },
    {
      slug: params?.id,
    },
  );

  useEventListener('productUpdate', product => {
    setProduct(product);
  });

  // const product = getProductRequest?.response?.data;

  useEffect(() => {
    const variants = product?.variants?.options!;
    const hasValues = variants?.[0]?.values !== undefined;
    // if (typeof(variants?.[0]?.values) !== 'object') return;
    if (variants?.length > 0) {
      let types: string[] = [];
      let values: { [key: string]: string[] } = {};

      if (hasValues) {
        types = [...types, ...Object.keys(variants?.[0]?.values!)];

        variants.forEach(v => {
          types.forEach(t => {
            if (values[t] !== undefined) {
              if (!values[t].includes(v?.values?.[t]!)) {
                values[t].push(v?.values?.[t]!);
              }
            } else {
              values[t] = [v?.values?.[t]!];
            }
          });
        });
      }

      if (product.variants?.type === 'images') {
        types = [...types, 'Images'];
        const images: string[] = [];
        variants.forEach(t => {
          if (t.image !== undefined && !images.includes(t.image)) {
            images.push(t.image);
          }
        });
        values = { ...values, images };
      }

      // console.log("types", types);
      // console.log("values", values);
      const variantsData = Object.entries(values).map(item => ({ name: item[0], value: item[1] }));
      setVariantValues(variantsData);
    }
  }, [product]);

  const handleDelete = async () => {
    const callBack = () => {
      showSuccess('Product deleted');
      eventEmitter.emit('removeProduct', { id: product.id });
      navigation.goBack();
    };
    await handleOnPressDelete(product!, callBack);
  };

  const handleToggleProductAvailable = async (value: boolean) => {
    await showLoader('Changing Product Availability');
    await handleEditProduct(
      product!,
      { is_always_available: product?.is_always_available, available: value },
      async () => {
        await delay(600);
        setProduct(prev => ({ ...prev, available: value }));
        showSuccess('Product updated successfully');
      },
    );
    hideLoader();
  };

  const handleEditProductOptions = async (value: VariantForm) => {
    // console.log(JSON.stringify(value));
    setStep('fillForm');
    const quantitySummation = value.options?.reduce((total, item) => total + (item.quantity || 0), 0);
    await delay(600);
    await showLoader('Updating product option');
    //@feranmi typing error here
    await handleEditProduct(product!, { variants: value, quantity: quantitySummation }, async () => {
      await delay(600);
      setProduct(prev => ({ ...prev, variants: value }));
      showSuccess('Product option updated successfully');
    });
    hideLoader();
  };

  const handleToggleFeature = async (value: boolean) => {
    handleOnPressFeature(product, () => setProduct(prev => ({ ...prev, is_featured: value })));
  };

  const actionPills = [
    ...(canEditProducts
      ? [
          {
            LeftIcon: () => <Edit2 color={colors?.black.muted} size={hp(15)} />,
            title: 'Edit Product',
            showArrow: true,
            onPress: () => navigation.navigate('EditProduct', { isDuplicate: false, product }),
          },
          {
            LeftIcon: () => <Notepad color={colors?.black.muted} size={hp(15)} />,
            title: 'Duplicate',
            onPress: () => duplicateProduct(product),
          },
        ]
      : []),
    {
      LeftIcon: () => <Link21 color={colors?.black.muted} size={hp(15)} />,
      title: 'Copy Link',
      onPress: () => copyToClipboard(getProductLink(product!)),
    },
    {
      LeftIcon: () => <TransmitSquare color={colors?.black.muted} size={hp(15)} />,
      title: 'Share ',
      showArrow: true,
      addon: <ArrowUpRight currentColor={colors.black.secondary} strokeWidth={1.5} size={18} />,
      onPress: () => handleProductShare(product!),
    },
  ];

  const buttons = canEditProducts
    ? [
        {
          text: 'Delete Product',
          onPress: handleDelete,
          disabled: getProductRequest.isLoading || getProductRequest.isReLoading || deleteLoading,
          variant: ButtonVariant.LIGHT,
          textColor: TextColor.NEGATIVE,
        },
        {
          text: 'Edit Product',
          disabled: getProductRequest.isLoading || getProductRequest.isReLoading || deleteLoading,
          onPress: () => navigation.navigate('EditProduct', { isDuplicate: false, product }),
        },
      ]
    : [];

  const extraProductDetails = removeEmptyAndUndefined({
    expiry_date: product?.expiry_date,
    cost_price: product?.cost_price,
    minimum_order_quantity: product?.minimum_order_quantity,
  });
  const validExtraProductDetails = Object.entries(extraProductDetails);

  const [step, setStep] = useState<'selectType' | 'fillForm'>('fillForm');

  const productTotalQty = useMemo(() => {
    if (product?.variants?.options?.length > 0) {
      const quantitySummation = product.variants?.options?.reduce((total, item) => total + (item.quantity || 0), 0);
      return quantitySummation;
    }

    return product?.quantity ?? '-';
  }, [product]);

  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = () => {
    setIsRetrying(true);
    getProductRequest.refetch();
    setTimeout(() => setIsRetrying(false), 2000);
  };

  const hasError = Boolean(getProductRequest.error);

  // const hasImages = product.images !== undefined && product?.images?.length > 0 && product?.images?.some(i => i !== '');

  const ErrorElement = () => {
    return (
      <View className="flex-1">
        <Row className="mx-20 mt-24 bg-grey-bgTwo rounded-12 p-12 items-start">
          <CircledIcon className="bg-accentYellow-pastel2 p-8 self-center">
            <Danger variant={'Bold'} color={colors.accentYellow.main} size={wp(16)} />
          </CircledIcon>
          <View className="flex-1 ml-10">
            <BaseText classes="text-black-muted">
              We couldn't load product details, please check your connection or refresh the page.
            </BaseText>
            <Pressable onPress={handleRetry}>
              <BaseText weight="medium" classes="text-primary-main mt-5">
                Reload
              </BaseText>
            </Pressable>
          </View>
        </Row>
        <ErrorSkeletonLoader />
      </View>
    );
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Product Details',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <QueryErrorBoundary
        error={getProductRequest.isLoading ? undefined : getProductRequest.error}
        isLoading={getProductRequest.isLoading}
        refetch={handleRetry}
        isRetrying={isRetrying}
        padAround
        variant="fullPage"
        errorTitle="Failed to load product details"
        customErrorElement={<ErrorElement />}
        customErrorMessage="We couldn't load the product details. Please check your connection and try again.">
        {getProductRequest?.isLoading && <SingleProductSkeletonLoader />}
        <ScrollView
          contentContainerStyle={{ paddingBottom: hp(20) }}
          refreshControl={
            <RefreshControl refreshing={getProductRequest?.isReLoading!} onRefresh={() => getProductRequest.reset()} />
          }>
          {product !== undefined && !getProductRequest?.isLoading && (
            <Fragment>
              <View className="p-20 items-center bg-accentYellow-pastel">
                <StatusPill title="IN STOCK" whiteBg />
                <BaseText fontSize={18} classes="mt-5 leading-[30px] text-center max-w-[200]" type="heading">
                  {product?.name}
                </BaseText>
              </View>
              <Pressable
                className="mx-20 mt-25 rounded-[15px] overflow-hidden justify-end"
                style={{ height: hp((width - 40) * 0.55) }}
                // disabled={!hasImages}
                onPress={() =>
                  navigation.navigate('ProductImages', {
                    images: product?.images ?? [],
                    thumbnail: product?.thumbnail,
                    activeIndex: product?.thumbnail,
                    itemId: product.id,
                  })
                }>
                {getItemThumbnail(product) ? (
                  <CustomImage imageProps={{ source: { uri: getItemThumbnail(product) } }} />
                ) : (
                  <View className="w-full h-full bg-accentYellow-pastel justify-center items-center">
                    <Box1 color={colors.accentYellow.main} variant="Bulk" size={wp(80)} />
                  </View>
                )}
                {product?.is_featured && (
                  <CircledIcon className="p-4 bg-accentGreen-main absolute bottom-10 left-10">
                    <Flash variant={'Bold'} size={wp(12)} color={colors.white} />
                  </CircledIcon>
                )}
              </Pressable>
              <ProductInfoRow
                className="px-20 pt-25 pb-10"
                leftItem={{
                  icon: (
                    <CircledIcon iconBg={'bg-accentRed-main'}>
                      <Moneys variant={'Bold'} size={wp(15)} color={colors.white} />
                    </CircledIcon>
                  ),
                  value: toCurrency(product?.price),
                  title: 'Product Price',
                }}
                rightItem={{
                  icon: (
                    <CircledIcon iconBg={'bg-accentYellow-main'}>
                      <MoneyForbidden variant={'Bold'} size={wp(15)} color={colors.white} />
                    </CircledIcon>
                  ),
                  value: product?.discount_price ? toCurrency(product?.discount_price!) : '-',
                  title: 'Discount Price',
                }}
              />
              <Separator />
              <ScrollableActionPills pills={actionPills} />
              <Separator />
              <View className={'mx-20'}>
                <BaseText fontSize={13} type="heading">
                  Product Description
                </BaseText>
                <BaseText fontSize={12} classes="text-black-muted mt-10" lineHeight={wp(16)}>
                  {product?.description}
                </BaseText>
              </View>
              <ProductImagesSection item={product} updateProduct={setProduct} />
              <Separator />
              <Row className="py-6 mx-20">
                <BaseText fontSize={12} type="heading">
                  Product is Active
                </BaseText>
                <CustomSwitch value={product?.available} onValueChange={handleToggleProductAvailable} />
              </Row>
              <Separator />
              <Row className="py-6 mx-20">
                <BaseText fontSize={12} type="heading">
                  Feature Product
                </BaseText>
                <CustomSwitch value={product?.is_featured} onValueChange={v => handleToggleFeature(v)} />
              </Row>
              <Separator />
              <ProductInfoRow
                className="px-20"
                leftItem={{
                  icon: (
                    <CircledIcon iconBg={'bg-accentRed-pastel'}>
                      <HambergerMenu variant={'Bold'} size={wp(15)} color={colors.accentRed.main} />
                    </CircledIcon>
                  ),
                  value: product?.variants?.options ? `${product?.variants?.options?.length!} Options` : '-',
                  title: 'Options',
                }}
                rightItem={{
                  icon: (
                    <CircledIcon iconBg={'bg-accentGreen-pastel'}>
                      <Bag variant={'Bold'} size={wp(15)} color={colors.accentGreen.main} />
                    </CircledIcon>
                  ),
                  value: productTotalQty ? `${productTotalQty} In stock` : '-',
                  title: 'Stock',
                }}
              />
              <Separator />
              <ProductInfoRow
                className="px-20"
                leftItem={{
                  icon: (
                    <CircledIcon iconBg={'bg-accentOrange-pastel'}>
                      <Category variant={'Bold'} size={wp(15)} color={colors.accentOrange.main} />
                    </CircledIcon>
                  ),
                  value: product?.category?.name ?? '-',
                  title: 'Category',
                }}
                rightItem={{
                  icon: (
                    <CircledIcon iconBg={'bg-accentYellow-pastel'}>
                      <Eye variant={'Bold'} size={wp(15)} color={colors.accentYellow.main} />
                    </CircledIcon>
                  ),
                  value: product?.discount_price ? toCurrency(product?.discount_price!) : '-',
                  title: 'Discount Price',
                }}
              />
              {validExtraProductDetails.length > 0 && (
                <>
                  <Separator className="mb-0" />
                  <View className="px-20">
                    <Accordion
                      anchorElement={status => (
                        <AccordionAnchor title={'Optional Product Settings'} isOpened={status} />
                      )}
                      title={'Optional Product Settings'}>
                      <View className="mt-10 bg-grey-bgOne px-10 pt-15 rounded-[15px]">
                        {validExtraProductDetails.map(([key, value]) => {
                          return (
                            <InfoRow
                              key={key}
                              icon={extraProductDetailsConfig[key].icon}
                              title={extraProductDetailsConfig[key].title}
                              value={value}
                              iconBg="white"
                            />
                          );
                        })}
                      </View>
                    </Accordion>
                  </View>
                </>
              )}
              <Separator />
              {product.variants?.options?.length! > 0 && (
                <View className="px-20">
                  <Row>
                    <BaseText fontSize={13} type="heading">
                      Product Options
                    </BaseText>
                    <WhiteCardBtn
                      className="bg-grey-bgOne rounded-full"
                      onPress={() => toggleModal('editProductOptions')}>
                      Edit Product Options
                    </WhiteCardBtn>
                  </Row>
                  {variantValues?.map((item, index) => (
                    <VariantSectionCard key={item?.name + index} name={item?.name} values={item?.value} />
                  ))}
                </View>
              )}
            </Fragment>
          )}
        </ScrollView>
        {!getProductRequest?.isLoading && <FixedBtnFooter buttons={buttons} />}
      </QueryErrorBoundary>
      {modals.editProductOptions && (
        <AddProductOptionModal
          isVisible={modals.editProductOptions}
          product={product?.name ? transformProductData(product) : ({} as Product)}
          initialView={step}
          // form={form}
          saveVariants={v => handleEditProductOptions(v)}
          isEdit
          headerAddon={
            step === 'fillForm' && (
              <Pressable onPress={() => setStep('selectType')}>
                <BaseText weight="medium" fontSize={14} classes="text-primary-main">
                  Edit Options
                </BaseText>
              </Pressable>
            )
          }
          closeModal={() => toggleModal('editProductOptions', false)}
          title={`${enumToHumanFriendly(product?.variants?.type)} product option`}
        />
      )}
    </DashboardLayout>
  );
};

export default ProductDetails;

const extraProductDetailsConfig = {
  expiry_date: {
    title: 'Expiry Date',
    icon: <Stickynote size={wp(16)} color={colors.black.placeholder} />,
  },
  cost_price: {
    title: 'Cost Price',
    icon: <Money size={wp(16)} color={colors.black.placeholder} />,
  },
  minimum_order_quantity: {
    title: 'Minimum Order Quantity',
    icon: <Layer size={wp(16)} color={colors.black.placeholder} />,
  },
};

const SingleProductSkeletonLoader = ({ showHeader = true }: { showHeader?: boolean }) => {
  return (
    <View className="flex-1">
      {showHeader && (
        <View className="py-35 bg-grey-bgTwo items-center">
          <Shimmer borderRadius={hp(40)} height={hp(12)} width={wp(70)} marginTop={hp(10)} />
          <Shimmer borderRadius={hp(40)} height={hp(15)} width={wp(160)} marginTop={hp(10)} />
        </View>
      )}
      <View className="px-20 mt-10">
        <Shimmer borderRadius={hp(12)} height={hp((width - 40) * 0.55)} width={width - 40} marginTop={hp(10)} />
        <View className={'flex-row justify-between mt-15'}>
          <View className="items-center flex-row flex-1">
            <Shimmer borderRadius={hp(40)} height={hp(40)} width={hp(40)} />
            <View className="ml-5">
              <BaseText fontSize={10} classes="text-black-placeholder">
                Product Price
              </BaseText>
              <Shimmer borderRadius={hp(40)} height={hp(12)} width={wp(90)} marginTop={hp(5)} />
            </View>
          </View>
          <View className="w-1 bg-grey-border" />
          <View className="items-center flex-row flex-1 pl-20">
            <Shimmer borderRadius={hp(40)} height={hp(40)} width={hp(40)} />
            <View className="ml-5">
              <BaseText fontSize={10} classes="text-black-placeholder">
                Discount Price
              </BaseText>
              <Shimmer borderRadius={hp(40)} height={hp(12)} width={wp(90)} marginTop={hp(5)} />
            </View>
          </View>
        </View>
        <View className="h-1 bg-grey-border my-10" />
        <View className="items-center flex-row py-10" style={{ columnGap: 10 }}>
          <Shimmer borderRadius={hp(40)} height={hp(30)} width={wp(90)} />
          <Shimmer borderRadius={hp(40)} height={hp(30)} width={wp(120)} />
          <Shimmer borderRadius={hp(40)} height={hp(30)} width={wp(100)} />
          <Shimmer borderRadius={hp(40)} height={hp(30)} width={wp(90)} />
        </View>
        <View className="h-1 bg-grey-border my-10" />
        <View>
          <BaseText fontSize={12} classes="text-black-main" type="heading">
            Product description
          </BaseText>
          <Shimmer borderRadius={hp(40)} height={hp(10)} width={wp(280)} marginTop={hp(10)} />
          <Shimmer borderRadius={hp(40)} height={hp(10)} width={wp(300)} marginTop={hp(8)} />
          <Shimmer borderRadius={hp(40)} height={hp(10)} width={wp(310)} marginTop={hp(8)} />
          <Shimmer borderRadius={hp(40)} height={hp(10)} width={wp(320)} marginTop={hp(8)} />
          <View className="items-center flex-row mt-25" style={{ columnGap: 10 }}>
            <Shimmer borderRadius={hp(12)} height={hp(55)} width={wp(55)} />
            <Shimmer borderRadius={hp(12)} height={hp(55)} width={wp(55)} />
            <Shimmer borderRadius={hp(12)} height={hp(55)} width={wp(55)} />
            <Shimmer borderRadius={hp(12)} height={hp(55)} width={wp(55)} />
            <Shimmer borderRadius={hp(12)} height={hp(55)} width={wp(55)} />
          </View>
        </View>
        <View className="h-1 bg-grey-border my-15" />
        <View className="flex-row justify-between">
          <Shimmer borderRadius={hp(12)} height={hp(15)} width={wp(65)} />
          <Shimmer borderRadius={hp(12)} height={hp(15)} width={wp(55)} />
        </View>
      </View>
    </View>
  );
};

const ErrorSkeletonLoader = () => {
  return (
    <View className="flex-1">
      <View className="px-20 mt-10">
        <Shimmer borderRadius={hp(12)} height={hp((width - 40) * 0.55)} width={width - 40} marginTop={hp(10)} />
        <View className={'flex-row justify-between mt-15'}>
          <Shimmer borderRadius={hp(12)} height={hp(50)} width={(width - 50) / 2} />
          <Shimmer borderRadius={hp(12)} height={hp(50)} width={(width - 50) / 2} />
        </View>
        <View className="h-1 bg-grey-border my-10" />
        <View className="items-center flex-row" style={{ columnGap: 10 }}>
          <Shimmer borderRadius={hp(40)} height={hp(36)} width={wp(90)} />
          <Shimmer borderRadius={hp(40)} height={hp(36)} width={wp(120)} />
          <Shimmer borderRadius={hp(40)} height={hp(36)} width={wp(100)} />
          <Shimmer borderRadius={hp(40)} height={hp(36)} width={wp(90)} />
        </View>
        <View className="h-1 bg-grey-border my-10" />
        <View>
          <Shimmer borderRadius={hp(12)} height={hp(84)} width={width - 40} />
          <View className="items-center flex-row mt-10" style={{ columnGap: 10 }}>
            <Shimmer borderRadius={hp(12)} height={hp(55)} width={wp(55)} />
            <Shimmer borderRadius={hp(12)} height={hp(55)} width={wp(55)} />
            <Shimmer borderRadius={hp(12)} height={hp(55)} width={wp(55)} />
            <Shimmer borderRadius={hp(12)} height={hp(55)} width={wp(55)} />
            <Shimmer borderRadius={hp(12)} height={hp(55)} width={wp(55)} />
          </View>
        </View>
        <View className="h-1 bg-grey-border my-15" />
        <View className="flex-row justify-between">
          <Shimmer borderRadius={hp(12)} height={hp(15)} width={wp(65)} />
          <Shimmer borderRadius={hp(12)} height={hp(15)} width={wp(55)} />
        </View>
        <View className="h-1 bg-grey-border my-15" />
        <View className="flex-row justify-between">
          <Shimmer borderRadius={hp(12)} height={hp(15)} width={wp(65)} />
          <Shimmer borderRadius={hp(12)} height={hp(15)} width={wp(55)} />
        </View>
        <View className="h-1 bg-grey-border my-15" />
        <View className={'flex-row justify-between'}>
          <Shimmer borderRadius={hp(12)} height={hp(50)} width={(width - 50) / 2} />
          <Shimmer borderRadius={hp(12)} height={hp(50)} width={(width - 50) / 2} />
        </View>
      </View>
    </View>
  );
};
