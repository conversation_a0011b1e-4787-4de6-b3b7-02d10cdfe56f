import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { useNavigation } from '@react-navigation/native';
import { useEffect, useState } from 'react';
import Toast from 'react-native-toast-message';
import { showError, showSuccess, transformProductData } from 'src/assets/utils/js';
import FillProductInfoController from '@/components/products/create-products/fill-product-info-controller';
import ProductsForm from '@/components/products/create-products/form';
import { Product, ProductForm } from '@/components/products/create-products/types';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import useProductMutations from 'src/hooks/use-product-mutations';
import useRouteParams from 'src/hooks/use-route-params';
import { CREATE_ITEMS, CreateItemsParams, EDIT_ITEM, EditItemParams, GET_VARIANT_TEMPLATES } from 'catlog-shared';
import eventEmitter from 'src/assets/utils/js/event-emitter';

const EditProduct = () => {
  const [form, setForm] = useState<ProductForm>({
    products: [],
  });
  const { store, categories } = useAuthContext();

  const navigation = useNavigation();

  const params = useRouteParams<'EditProduct'>();
  const allowVariants = true;
  const isDuplication = params?.isDuplicate;

  // console.log(JSON.stringify(params?.product));
  const updateProductMutation = useApi<EditItemParams>({ apiFunction: EDIT_ITEM, method: 'PUT', key: 'edit-item' });

  const getVariantTemplatesReq = useApi({
    method: 'GET',
    key: 'get-variant-templates',
    apiFunction: GET_VARIANT_TEMPLATES,
  });

  const createProductRequest = useApi<CreateItemsParams>({
    method: 'POST',
    key: 'create-products',
    apiFunction: CREATE_ITEMS,
  });

  const templates: Product['variants'][] = getVariantTemplatesReq?.response?.data?.templates || [];

  useEffect(() => {
    if (params?.product) {
      setForm({ products: [transformProductData(params?.product)] });
    }
  }, [params?.product]);

  const handleEditProduct = async () => {
    const product = {
      ...form.products[0],
      images: form.products[0].images.map(i => i.url!),
      price: Number(form.products[0].price),
      discount_price: form.products[0].discount_price ? Number(form.products[0].discount_price) : undefined,
    };

    const reqData = { id: params?.product.id!, item: product };

    const [response, error] = await updateProductMutation.makeRequest(reqData);

    if (response) {
      Toast.show({ text1: 'Product updated Successfully' });
      eventEmitter.emit('productUpdate', response?.data);
      navigation.goBack();
    }

    if (error) {
      Toast.show({ type: 'error', text1: error.body.body.message });
      // console.log(error);
    }
  };

  const handleDuplicateProduct = async () => {
    const product = {
      ...form.products[0],
      images: form.products[0].images.map(i => i.url!),
      price: Number(form.products[0].price),
      quantity: form.products[0].is_always_available ? null : form.products[0].quantity,
      ...(form.products[0].discount_price && { discount_price: Number(form.products[0].discount_price) }),
    };

    const reqData = { store: store?.id, items: [product] };

    const [response, error] = await createProductRequest.makeRequest(reqData);

    if (response) {
      showSuccess('Product Duplicated Successfully');
      navigation.goBack();
    }

    if (error) {
      showError(error);
    }
  };

  const actionTypeData = {
    title: isDuplication ? 'Duplicate Product' : 'Edit Product',
    btnText: isDuplication ? 'Duplicate Product' : 'Edit Product',
    submitFunc: isDuplication ? handleDuplicateProduct : handleEditProduct,
    isLoading: isDuplication ? createProductRequest.isLoading : updateProductMutation.isLoading,
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: actionTypeData?.title,
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      {form?.products[0] && (
        <ProductsForm
          product={form?.products[0]}
          index={0}
          setForm={setForm}
          form={form}
          changeView={() => {}}
          submitForm={actionTypeData?.submitFunc}
          isLoading={actionTypeData?.isLoading}
          categories={categories ?? []}
          allowVariants={allowVariants}
          isSetup={false}
          isDuplication={isDuplication}
          currentIndex={0}
          variantTemplates={templates}
          store={store!}
          isEditing
        />
      )}
    </DashboardLayout>
  );
};

export default EditProduct;
