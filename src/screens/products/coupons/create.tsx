import { Image, KeyboardAvoidingView, ScrollView, View } from 'react-native';
import colors from '@/theme/colors';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import Container from '@/components/ui/container';
import { useNavigation } from '@react-navigation/native';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import CouponForm, {
  CouponFormScreen,
  couponValidationSchema,
} from '@/components/products/discounts-and-coupons/coupons/coupon-form';
import { useApi } from '@/hooks/use-api';
import { useFormik } from 'formik';
import Toast from 'react-native-toast-message';
import { CREATE_COUPON, CreateCouponParams } from 'catlog-shared';
import { useMemo } from 'react';
import { testYupValidation } from 'src/assets/utils/js';

const CreateCoupon = () => {
  const navigation = useNavigation();
  const createCouponRequest = useApi({ apiFunction: CREATE_COUPON, method: 'POST', key: 'create-coupon' });

  const form = useFormik<CreateCouponParams>({
    initialValues: {
      coupon_code: '',
      type: '',
      active: true,
      end_date: null,
      percentage: 1,
      quantity: 0, //todo: @silas
      discount_amount: 0,
      discount_cap: null,
      minimum_order_amount: 0,
    },
    validationSchema: couponValidationSchema,
    onSubmit: async values => {
      // setErrorText("");
      // console.log(values);

      const requestData = {
        ...values,
        percentage: Number(values.percentage),
        quantity: Number(values.quantity),
        ...(values?.discount_cap && { discount_cap: Number(values?.discount_cap) }),
        ...(values?.discount_amount && { discount_amount: Number(values?.discount_amount) }),
        ...(values?.minimum_order_amount && { minimum_order_amount: Number(values?.minimum_order_amount) }),
      };
      // const [response, error] = await login(values);
      const [response, error] = await createCouponRequest.makeRequest(requestData);
      if (response) {
        navigation.navigate('Feedback', {
          headerTitle: 'Coupon Created',
          headerBg: 'bg-accentRed-pastel',
          feedbackText: "You've successfully created a new discount",
          btnText: 'Bact to coupons',
          onPressBtn: () => navigation.navigate('HomeTab', { screen: 'Products' } as any),
        });
      }

      if (error) {
        // setErrorText(error.message);
        Toast.show({ type: 'error', text1: error.body.message });
      }
    },
  });
  
    const isFormValid = (form: { values: unknown }, schema: typeof couponValidationSchema): boolean => {
      const { isValid } = testYupValidation<any>(schema, form.values);
      return isValid;
    };
  
    const disableSubmission = useMemo(() => {
      return !isFormValid(form, couponValidationSchema);
    }, [form.values]);

  // const disableSubmission = useMemo(() => {
  //   const { isValid } = testYupValidation<any>(couponValidationSchema, form.values);
  //   return !isValid;
  // }, [form.values]);

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: 'Create Coupon',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <AvoidKeyboard>
        <ScrollView keyboardDismissMode={'interactive'}>
          <ScreenInfoHeader
            iconElement={
              <Image
                className="w-80 h-80"
                source={require('@/assets/images/coupon.png')}
                resizeMode={'contain'}
              />
            }
            colorPalette={ColorPaletteType.RED}
            pageTitleTop={'Create Coupon'}
            pageTitleBottom={'For Use at Checkout'}
          />
          <Container className={'mt-20'}>
            <CouponForm screen={CouponFormScreen.CREATE_SCREEN} form={form} />
          </Container>
        </ScrollView>
      </AvoidKeyboard>
      <FixedBtnFooter
        buttons={[
          {
            text: 'Create Coupon',
            onPress: () => form.handleSubmit(),
            isLoading: createCouponRequest.isLoading,
            disabled: disableSubmission || createCouponRequest.isLoading,
          },
        ]}
      />
    </DashboardLayout>
  );
};

export default CreateCoupon;
