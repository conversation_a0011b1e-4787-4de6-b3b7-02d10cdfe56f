import { useNavigation } from '@react-navigation/native';
import React, { useState } from 'react';
import { Alert, RefreshControl, StyleSheet, View } from 'react-native';
import Animated, { ScrollHandlerProcessed } from 'react-native-reanimated';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import FAB from '@/components/ui/buttons/fab';
import EmptyState from '@/components/ui/empty-states/empty-state';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import { mockProducts } from '@/constant/mock-data';
import useAuthContext from '@/contexts/auth/auth-context';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import usePagination from '@/hooks/use-pagination';
import {
  ensureUniqueItems,
  getColorAlternates,
  showError,
  showLoader,
  toCurrency,
  updateOrDeleteItemFromList,
  wp,
} from 'src/assets/utils/js';
import ListItemCard from '@/components/ui/cards/list-item-card';
import Shimmer from '@/components/ui/shimmer';
import { Bag, Edit2, Trash } from 'iconsax-react-native/src';
import MoreOptions, { MoreOptionElementProps, OptionWithIcon } from '@/components/ui/more-options';
import colors from 'src/theme/colors';
import Toast from 'react-native-toast-message';
import AddOtherProductModal from '@/components/products/add-other-product-modal';
import useModals from 'src/hooks/use-modals';
import {
  StoreInterface,
  DELETE_CUSTOM_ITEM,
  DeleteItemParams,
  GET_CUSTOM_ITEMS,
  GET_ITEMS,
  GET_STORE_ITEMS,
  GetCustomItemsParams,
  GetItemsParams,
  CustomItemInterface,
  ProductItemInterface,
} from 'catlog-shared';

interface OtherProductsProps {
  scrollHandler: ScrollHandlerProcessed<Record<string, unknown>>;
}

export interface ProductsResponse
  extends ResponseWithPagination<{ store: StoreInterface; items: CustomItemInterface[] }> {}
const PER_PAGE = 10;

const OtherProducts: React.FC<OtherProductsProps> = ({ scrollHandler }) => {
  const [otherProducts, setOtherProducts] = useState<CustomItemInterface[]>([]);
  const [activeItem, setActiveItem] = useState<CustomItemInterface>({} as CustomItemInterface);
  const [isEditing, setIsEditing] = useState<boolean>(false);

  const { modals, toggleModal } = useModals(['addItems']);

  const { store } = useAuthContext();
  const { currentPage, goNext, setPage } = usePagination();

  const getOtherProductsRequest = useApi<GetCustomItemsParams, ProductsResponse>(
    {
      key: 'custom-products',
      apiFunction: GET_CUSTOM_ITEMS,
      method: 'GET',
      onSuccess: response => {
        setOtherProducts(prev => ensureUniqueItems([...prev, ...response?.data?.items]));
      },
    },
    {
      filter: { store: store?.id, search: '' },
      page: currentPage,
      per_page: PER_PAGE,
      sort: 'dsc',
    },
  );

  const deleteOtherProductsRequest = useApi<DeleteItemParams>({
    key: 'custom-products',
    apiFunction: DELETE_CUSTOM_ITEM,
    method: 'DELETE',
  });

  const handlePullToRefresh = () => {
    setOtherProducts([]);
    if (currentPage === 1) {
      getOtherProductsRequest.reset();
      return;
    }
    setPage(1);
  };
  const deleteDiscount = async (item: CustomItemInterface) => {
    Alert.alert('Delete Item', `Are you sure you want to delete ${item.name}?`, [
      { text: 'Delete', onPress: () => deleteFunction(), style: 'destructive' },
      { text: 'Cancel', onPress: () => {} },
    ]);

    const deleteFunction = async () => {
      showLoader('Deleting item...');
      const [response, error] = await deleteOtherProductsRequest.makeRequest({
        id: item.id,
      });

      if (response) {
        setOtherProducts(updateOrDeleteItemFromList(otherProducts, 'id', item.id, null));
        Toast.show({ type: 'success', text1: 'Item deleted' });
      }

      if (error) {
        showError(error);
      }
    };
  };

  const openEditModal = (item: CustomItemInterface) => {
    setIsEditing(true);
    setActiveItem(item);
    setTimeout(() => {
      toggleModal('addItems');
    }, 600);
  };

  const openAddItemModal = () => {
    setIsEditing(false);
    setActiveItem({} as CustomItemInterface);
    setTimeout(() => {
      toggleModal('addItems');
    }, 600);
  };

  const moreOptionFunc = (item: CustomItemInterface) => [
    {
      optionElement: (
        <OptionWithIcon icon={<Edit2 size={wp(15)} color={colors.black.placeholder} />} label={'Edit Item'} />
      ),
      title: 'Edit Item',
      onPress: () => openEditModal(item),
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Trash size={wp(15)} color={colors.black.placeholder} />} label={'Delete Item'} />
      ),
      title: 'Delete Item',
      onPress: () => deleteDiscount(item),
    },
  ];

  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = () => {
    setIsRetrying(true);
    handlePullToRefresh();
    setTimeout(() => setIsRetrying(false), 2000);
  };

  return (
    <View style={{ flex: 1 }}>
      <View style={{ flex: 1 }}>
        <QueryErrorBoundary
          error={getOtherProductsRequest.error}
          isLoading={getOtherProductsRequest.isLoading}
          refetch={handleRetry}
          isRetrying={isRetrying}
          padAround
          variant='fullPage'
          errorTitle="Failed to load other products"
          customErrorMessage="We couldn't load your other products. Please check your connection and try again."
        >
          <Animated.FlatList
          data={otherProducts}
          keyExtractor={(item, index) => item.id + index}
          onScroll={scrollHandler}
          refreshControl={<RefreshControl refreshing={false} onRefresh={handlePullToRefresh} />}
          onEndReachedThreshold={0.3}
          ListEmptyComponent={() =>
            getOtherProductsRequest?.isLoading ? (
              <OtherProductSkeletonLoader />
            ) : (
              <EmptyState btnText={'Add Other Product'} text={'No other products added yet'} icon={<Bag variant={'Bold'} size={wp(40)} color={colors.grey.muted} />} />
            )
          }
          className="flex-1 px-20 pb-40"
          contentContainerStyle={{ flexGrow: 1 }}
          onEndReached={() => {
            if (!getOtherProductsRequest?.isLoading) {
              goNext(getOtherProductsRequest?.response?.total_pages);
            }
          }}
          renderItem={value => <OtherProductCard {...value} options={moreOptionFunc(value.item)} />}
          ListFooterComponent={
            <View style={{ marginBottom: 80 }}>
              {getOtherProductsRequest?.isLoading && (
                <View className="mt-20">
                  <OtherProductSkeletonLoader />
                </View>
              )}
            </View>
          }
        />
        </QueryErrorBoundary>
      </View>
      <FAB onPress={openAddItemModal} />
      <AddOtherProductModal
        isVisible={modals.addItems}
        closeModal={() => toggleModal('addItems', false)}
        setOtherProducts={setOtherProducts}
        activeItem={activeItem}
        isEdit={isEditing}
      />
    </View>
  );
};

interface SkeletonProps {
  showBorder?: boolean;
}

const OtherProductCard = ({
  item,
  index,
  options,
}: {
  item: CustomItemInterface;
  index: number;
  options: MoreOptionElementProps[];
}) => {
  const { store } = useAuthContext();

  const colorAlternate = getColorAlternates(index);

  return (
    <ListItemCard
      title={item.name}
      leftElement={
        <CircledIcon style={{ backgroundColor: colorAlternate.bgColor }}>
          <Bag variant={'Bold'} size={wp(20)} color={colorAlternate.iconColor} />
        </CircledIcon>
      }
      titleProps={{ type: 'heading', classes: 'text-black-muted' }}
      descriptionClasses="text-black-main"
      description={toCurrency(Number(item.price), store?.currencies?.default)}
      rightElement={<MoreOptions options={options ?? []} />}
    />
  );
};

export const OtherProductSkeleton = ({ showBorder = true }: SkeletonProps) => {
  return (
    <View className={`flex-row items-center ${showBorder && 'border-b border-b-grey-border py-15'}`}>
      <Shimmer {...{ height: 50, width: 50, borderRadius: 100 }} />
      <View className={'mx-12 flex-1'}>
        <Row className="mr-12 mb-10">
          <Shimmer {...{ height: 15, width: 150, borderRadius: 50 }} className="mr-2" />
          <Shimmer {...{ height: 5, width: 15, borderRadius: 50 }} />
        </Row>
        <Shimmer {...{ height: 10, width: 70, borderRadius: 50 }} />
      </View>
    </View>
  );
};

export const OtherProductSkeletonLoader = () => {
  return (
    <View>
      {Array.from({ length: 10 }, (_, i) => i).map(i => (
        <OtherProductSkeleton key={i} />
      ))}
    </View>
  );
};

export default React.memo(OtherProducts);
