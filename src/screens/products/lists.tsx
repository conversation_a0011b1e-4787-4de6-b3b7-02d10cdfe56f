import { ActivityIndicator, FlatList, ScrollView, View } from 'react-native';
import colors from '@/theme/colors';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { Search } from '@/components/ui/icons';
import Input from '@/components/ui/inputs/input';
import { useState } from 'react';
import EmptyState from '@/components/ui/empty-states/empty-state';
import { useNavigation } from '@react-navigation/native';
import TopTabs from '@/components/ui/others/top-tabs';
import Animated, {
  Easing,
  interpolate,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import Pressable from '@/components/ui/base/pressable';
import StorefrontProducts from './storefront';
import DiscountsList from './discounts';
import CouponsList from './coupons';
import CategoriesList from './categories';
import { wp } from 'src/assets/utils/js';
import OtherProducts from './other-products';
import useScrollHandler from 'src/hooks/use-scroll-handler';
import SearchContainer, { SEARCH_BG_VARIANT } from 'src/components/ui/others/search-container';
import { CircledIcon } from 'src/components/ui';
import { Add, ArrowRight, Edit2, Element2, ElementEqual, ExportSquare, Layer } from 'iconsax-react-native/src';
import { DropDownItem } from 'src/components/ui/inputs/select-dropdown';
import useInteractionWait from 'src/hooks/use-interaction-wait';
import InfoBlocksList from './info-blocks/info-blocks-list';
import ProductHighlightList from './product-highlights/product-highlight-list';

enum PRODUCT_PAGES {
  STOREFRONT_PRODUCTS = 'STOREFRONT_PRODUCTS',
  OTHER_PRODUCTS = 'OTHER_PRODUCTS',
  DISCOUNTS = 'DISCOUNTS',
  COUPONS = 'COUPONS',
  CATEGORIES = 'CATEGORIES',
  PRODUCT_HIGHLIGHTS = 'PRODUCT_HIGHLIGHTS',
  INFO_BLOCKS = 'INFO_BLOCKS',
}

const pages = [
  PRODUCT_PAGES.STOREFRONT_PRODUCTS,
  PRODUCT_PAGES.OTHER_PRODUCTS,
  PRODUCT_PAGES.DISCOUNTS,
  PRODUCT_PAGES.COUPONS,
  PRODUCT_PAGES.CATEGORIES,
  PRODUCT_PAGES.PRODUCT_HIGHLIGHTS,
  PRODUCT_PAGES.INFO_BLOCKS,
];

const ProductPagesLists = () => {
  const navigation = useNavigation();
  const [tabIndex, setTabIndex] = useState(0);
  const activePage: PRODUCT_PAGES = pages[tabIndex];
  const { scrollHandler, headerStyle, inputStyle } = useScrollHandler(100);

  const actionItems: { [key: string]: DropDownItem[] } = {
    [PRODUCT_PAGES.STOREFRONT_PRODUCTS]: [
      {
        label: 'Add Product',
        value: 'Add Product',
        onPress: () => navigation.navigate('CreateProducts'),
        leftElement: (
          <CircledIcon className="bg-white">
            <Add size={wp(15)} color={colors.primary.main} strokeWidth={2} />
          </CircledIcon>
        ),
      },
      {
        label: 'Bulk Update Qtys',
        value: 'Bulk Update Qtys',
        onPress: () => navigation.navigate('QuantityUpdate'),
        actionDelay: 300,
        leftElement: (
          <CircledIcon className="bg-white">
            <ElementEqual size={wp(14)} color={colors.accentOrange.main} strokeWidth={1.5} />
          </CircledIcon>
        ),
      },
      {
        label: 'Bulk Update Prices',
        value: 'Bulk Update Prices',
        onPress: () => navigation.navigate('PriceUpdate'),
        actionDelay: 300,
        leftElement: (
          <CircledIcon className="bg-white">
            <Element2 size={wp(14)} color={colors.accentRed.main} strokeWidth={1.5} />
          </CircledIcon>
        ),
      },
      {
        label: 'Sort Products',
        value: 'Sort Products',
        onPress: () => navigation.navigate('SortProducts'),
        actionDelay: 300,
        leftElement: (
          <CircledIcon className="bg-white">
            <Layer size={wp(14)} color={colors.accentYellow.main} strokeWidth={1.5} />
          </CircledIcon>
        ),
      },
    ],
  };

  const pagesConfig = {
    [PRODUCT_PAGES.STOREFRONT_PRODUCTS]: {
      title: 'Storefront Products',
      searchLabel: 'Search Products',
      onPressSearch: () => navigation.navigate('SearchProducts'),
    },
    [PRODUCT_PAGES.DISCOUNTS]: {
      title: 'Discounts',
      searchLabel: 'Search Discounts',
      onPressSearch: () => navigation.navigate('SearchDiscounts'),
    },
    [PRODUCT_PAGES.COUPONS]: {
      title: 'Coupons',
      searchLabel: 'Search Coupons',
      onPressSearch: () => navigation.navigate('SearchCoupons'),
    },
    [PRODUCT_PAGES.CATEGORIES]: {
      title: 'Categories',
      searchLabel: 'Search Categories',
      onPressSearch: () => navigation.navigate('SearchProducts'),
    },
    [PRODUCT_PAGES.OTHER_PRODUCTS]: {
      title: 'Other Products',
      searchLabel: 'Search Other Products',
      onPressSearch: () => navigation.navigate('SearchProducts'),
    },
    [PRODUCT_PAGES.PRODUCT_HIGHLIGHTS]: {
      title: 'Product Highlights',
      searchLabel: 'Search Product Highlights',
      onPressSearch: () => {},
    },
    [PRODUCT_PAGES.INFO_BLOCKS]: {
      title: 'Info Blocks',
      searchLabel: 'Search Info Blocks',
      onPressSearch: () => {},
    },
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: pagesConfig[activePage].title,
        variant: HeaderVariants.ROOT_LEVEL,
      }}>
      <SearchContainer
        {...{ headerStyle, inputStyle }}
        color={SEARCH_BG_VARIANT.YELLOW}
        onPressSearch={pagesConfig[activePage].onPressSearch}
        placeholder={pagesConfig[activePage].searchLabel}
        actions={actionItems[activePage]}
      />
      <TopTabs
        setIndex={setTabIndex}
        currentIndex={tabIndex}
        tabItems={[
          {
            key: PRODUCT_PAGES.STOREFRONT_PRODUCTS,
            title: pagesConfig[PRODUCT_PAGES.STOREFRONT_PRODUCTS].title,
            component: <StorefrontProducts scrollHandler={scrollHandler} />,
            func: () => {},
          },
          {
            key: PRODUCT_PAGES.OTHER_PRODUCTS,
            title: pagesConfig[PRODUCT_PAGES.OTHER_PRODUCTS].title,
            component: <OtherProducts scrollHandler={scrollHandler} />,
            func: () => {},
          },
          {
            key: PRODUCT_PAGES.DISCOUNTS,
            title: pagesConfig[PRODUCT_PAGES.DISCOUNTS].title,
            component: <DiscountsList scrollHandler={scrollHandler} />,
            func: () => {},
          },
          {
            key: PRODUCT_PAGES.COUPONS,
            title: pagesConfig[PRODUCT_PAGES.COUPONS].title,
            component: <CouponsList scrollHandler={scrollHandler} />,
            func: () => {},
          },
          {
            key: PRODUCT_PAGES.CATEGORIES,
            title: pagesConfig[PRODUCT_PAGES.CATEGORIES].title,
            component: <CategoriesList scrollHandler={scrollHandler} />,
            func: () => {},
          },
          {
            key: PRODUCT_PAGES.PRODUCT_HIGHLIGHTS,
            title: pagesConfig[PRODUCT_PAGES.PRODUCT_HIGHLIGHTS].title,
            component: <ProductHighlightList scrollHandler={scrollHandler} />,
            func: () => {},
          },
          {
            key: PRODUCT_PAGES.INFO_BLOCKS,
            title: pagesConfig[PRODUCT_PAGES.INFO_BLOCKS].title,
            component: <InfoBlocksList scrollHandler={scrollHandler} />,
            func: () => {},
          },
        ]}
      />
    </DashboardLayout>
  );
};

export default ProductPagesLists;
