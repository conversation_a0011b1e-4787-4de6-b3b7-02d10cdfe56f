import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MobileAppConfig } from 'catlog-shared';

interface FeatureFlagsState {
  flags: MobileAppConfig['feature_flags'];
  isInitialized: boolean;
}

interface FeatureFlagsActions {
  setFlags: (flags: Partial<MobileAppConfig['feature_flags']>) => void;
  setIsInitialized: (isInitialized: boolean) => void;
  initialize: () => Promise<void>;
}

const defaultFlags: MobileAppConfig['feature_flags'] = {
  subscriptions: false,
  deliveries: false,
};

const useFeatureFlagsStore = create(
  persist<FeatureFlagsState & FeatureFlagsActions>(
    (set, get) => ({
      flags: defaultFlags,
      isInitialized: false,

      setFlags: (flags) => {
        set((state) => ({
          flags: {
            ...state.flags,
            ...flags,
          },
        }));
      },

      setIsInitialized: (isInitialized) => {
        set({ isInitialized });
      },

      initialize: async () => {
        try {
          // You can fetch remote flags here
          // const remoteFlags = await fetch('YOUR_REMOTE_CONFIG_URL');
          // const flags = await remoteFlags.json();
          
          set({ 
            flags: {
              ...defaultFlags,
              // ...flags,
            },
            isInitialized: true,
          });
        } catch (error) {
          console.error('Failed to fetch feature flags:', error);
          set({ 
            flags: defaultFlags,
            isInitialized: true,
          });
        }
      },
    }),
    {
      name: 'feature-flags-storage',
      storage: createJSONStorage(() => AsyncStorage),
    },
  ),
);

export default useFeatureFlagsStore;