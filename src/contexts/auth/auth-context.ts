// useAuthContext.js
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useNavigation } from '@react-navigation/native';
import useAuthStore from './store'; // Import the Zustand store
import { delay, getActiveRouteName, subdomainStoreLink } from '@/assets/utils/js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ResponseWithoutPagination, useApi } from '@/hooks/use-api';
import {
  supportedCountries,
  StoreInterface,
  StoreRoles,
  GET_CURRENT_SESSION,
  LOGIN,
  LoginParams,
  REFRESH_TOKEN,
  RefreshTokenParams,
  GET_ALL_CONVERSION_RATES,
  ConversionRates,
  CurrencyRates,
  GET_REFERRAL_REWARDS,
  UPDATE_LAST_LOGIN,
  GET_MOBILE_APP_CONFIG,
  countryCodes,
  COUNTRIES,
  PLAN_TYPE,
} from 'catlog-shared';
import { set } from 'node_modules/date-fns';
import { RootStackParamList } from 'src/@types/navigation';
import { useFeatureFlags } from '../feature-flags/use-feature-flags';
import useFeatureFlagsStore from '../feature-flags/store';
import useWalletContext from '../wallet/wallet-context';
import useWalletStore from '../wallet/store';
import * as Updates from 'expo-updates';

function useAuthContext() {
  const navigation = useNavigation();
  const navigationState = navigation.getState();

  // Zustand store actions and state
  const {
    token,
    rewards,
    isNewUser,
    appIsSetup,
    fetchError,
    userHasSetup,
    userAccountDeactivated,
    isInitialized,
    isAuthenticated,
    isAuthFromCache,
    pageIsReady,
    redirectTo,
    isSwitchingStore,
    isCreatingStore,
    user,
    storeId,
    history,
    currentRates,
    setToken,
    setStoreId,
    setIsAuthenticated,
    setFetchError,
    setAppIsSetup,
    setUserHasSetup,
    setPageIsReady,
    setRedirectTo,
    setIsSwitchingStore,
    setIsCreatingStore,
    setUser,
    setHistory,
    verifyAndSetStoreId,
    handleAuthSuccess,
    getStore,
    clearUserData,
    getToken,
    setCurrencyRates,
    setRewards,
    setUserAccountDeactivated,
  } = useAuthStore();

  const stores = useMemo(() => (user?.stores ?? []) as StoreInterface[], [user]);
  const store: StoreInterface | null = useMemo(() => getStore(), [user, storeId]);

  useEffect(() => {
    console.log('<~~~~~~~~~~~~~~~~~~~~~ re-mounted Auth context ~~~~~~~~~~~~~~~~~~~~~>');
  }, []);

  const getUserDataRequest = useApi({
    key: 'get-user-data',
    apiFunction: GET_CURRENT_SESSION,
    method: 'GET',
    autoRequest: false,
  });

  const getNewTokenRequest = useApi<RefreshTokenParams>({
    key: 'get-new-token',
    apiFunction: REFRESH_TOKEN,
    method: 'GET',
    autoRequest: false,
  });

  const getCurrentRates = useApi<void, ResponseWithoutPagination<ConversionRates>>({
    key: 'get-conversion-rates-data',
    apiFunction: GET_ALL_CONVERSION_RATES,
    method: 'GET',
    autoRequest: false,
  });

  const getReferralRewardsReq = useApi<any, any>({
    apiFunction: GET_REFERRAL_REWARDS,
    key: GET_REFERRAL_REWARDS.name,
    method: 'GET',
    autoRequest: false,
  });

  const getRemoteConfigs = useApi<any, any>({
    apiFunction: GET_MOBILE_APP_CONFIG,
    key: GET_MOBILE_APP_CONFIG.name,
    method: 'GET',
    autoRequest: false,
    onSuccess: response => {
      useFeatureFlagsStore.setState({ flags: response?.data?.feature_flags ?? {} });
    },
  });

  const updateLastLogin = useApi<void, ResponseWithoutPagination<ConversionRates>>({
    apiFunction: UPDATE_LAST_LOGIN,
    key: UPDATE_LAST_LOGIN.name,
    method: 'GET',
    autoRequest: false,
  });

  // useEffect()

  useEffect(() => {
    const fn = async () => {
      if (token && storeId) {
        await refreshTokenIfNeeded(storeId);
      }
    };

    fn();
  }, [token, storeId]);

  useEffect(() => {
    if (isAuthenticated && !appIsSetup) {
      initialize();
    }
  }, [isAuthenticated, appIsSetup]);

  const initialize: VoidFunction = async () => {
    if (isAuthenticated) {
      if (isAuthFromCache) await updateUserData();

      let { userHasCompletedSetup, nextRoute } = decideNextRoute();

      setRedirectTo({ path: nextRoute ?? redirectTo?.path ?? 'HomeTab', props: null });

      if (userHasCompletedSetup) {
        getReferralRewardsReq.makeRequest({});
        getRemoteConfigs.makeRequest({});

        getRates();
        refreshRates();
      }
    }

    setAppIsSetup(true);
  };

  async function refreshTokenIfNeeded(storeId?: string) {
    const lastRefresh = await AsyncStorage.getItem('last-refresh');
    const now = new Date().getTime();

    // Check if the last refresh was more than a day ago
    if (!lastRefresh || now - parseInt(lastRefresh, 10) > 24 * 60 * 60 * 1000) {
      await getNewToken(storeId);
    }
  }

  const decideNextRoute = (
    pathToVisit?: keyof RootStackParamList,
  ): {
    userHasCompletedSetup: boolean;
    nextRoute: keyof RootStackParamList | null;
  } => {
    if (!isAuthenticated) return { userHasCompletedSetup: false, nextRoute: null };

    const goingTo: keyof RootStackParamList = pathToVisit ?? getActiveRouteName(navigationState);

    const { flags } = useFeatureFlagsStore.getState();

    const hasWatchedTutorial = user?.meta?.has_watched_onboarding_video;
    const stores = (user?.stores as StoreInterface[]) ?? [];
    const userHasStores = stores?.length > 0;
    const store = userHasStores ? getStore() : null;
    const userHasUploadedProducts = (store?.item_count ?? 0) >= 1;
    // const userHasUploadedProducts = store?.onboarding_steps?.products_added;
    const userHasSubscribed = store?.subscription ?? false;
    // const userAccountDeactivated =
    //   store?.subscription && store?.subscription?.plan.type === PLAN_TYPE.STARTER ? true : false;
    const userHasAdditionalStoreDetails =
      Boolean(store?.business_category?.name) && Boolean(store?.business_category?.type);
    const userHasCompletedSetup = Boolean(
      userHasStores &&
        userHasUploadedProducts &&
        userHasAdditionalStoreDetails &&
        (!flags.subscriptions || userHasSubscribed),
    );

    setUserHasSetup(userHasCompletedSetup);

    const userHAsStarterPlan =
      store?.subscription && store?.subscription?.plan?.type === PLAN_TYPE.STARTER ? true : false;

    setUserAccountDeactivated(userHAsStarterPlan);
    //todo: @silas add goingTo checks
    if (!userHasCompletedSetup) {
      const nextRoute: keyof RootStackParamList = (() => {
        if (!isNewUser) {
          return 'SetupProgress';
        }

        if (!userHasStores) return 'CreateStore';

        if (userHasStores && !userHasUploadedProducts) return 'SetupAddProducts';

        if (userHasStores && userHasUploadedProducts && !userHasAdditionalStoreDetails) return 'SetupBusiness';

        if (userHasStores && userHasUploadedProducts && userHasAdditionalStoreDetails && !hasWatchedTutorial)
          return 'WatchTutorial';

        // if (userHasStores && userHasAdditionalStoreDetails && !userHasUploadedProducts) return 'SetupAddProducts';

        if (userHasStores && userHasUploadedProducts && userHasAdditionalStoreDetails && !userHasSubscribed)
          return 'PickPlan';

        return null;
      })();

      return { userHasCompletedSetup, nextRoute };
    }

    return { userHasCompletedSetup, nextRoute: null };
  };

  const updateUserData = async () => {
    const validUser = await fetchUserSession();

    if (validUser) {
      //update last login
      updateLastLogin.makeRequest();
      await AsyncStorage.setItem('last-login', String(Date.now()));
    } else {
      // possibly logout user
    }
  };

  const fetchUserSession = async () => {
    const [res, err] = await getUserDataRequest.makeRequest(null);

    if (!err) {
      const loggedInUser = res;
      setUser(loggedInUser);
      verifyAndSetStoreId();

      return true;
    } else {
      //do something with this, show an error screen to the user
      setFetchError(true);
    }
    return false;
  };

  async function getNewToken(storeId?: string) {
    const [res, err] = await getNewTokenRequest.makeRequest({ store: storeId });
    if (res) {
      setToken(res.data.token);
      storeId && setStoreId(storeId);
      await AsyncStorage.setItem('last-refresh', new Date().getTime().toString());
    }
  }

  //Actions
  const logout = (redirect = true) => {
    clearUserData();
    // if (redirect) navigation.navigate('Login');
  };

  async function getReferralRewards() {
    const [res, err] = await getReferralRewardsReq.makeRequest({});

    if (res) {
      setRewards(res?.data);
    }
  }

  const switchStore = async (store: string, getUserInformation = false) => {
    if (store) {
      setIsSwitchingStore(true);
      await getNewToken(store);
      await delay(1000);
      await Updates.reloadAsync();

      // if (getUserInformation) {
      //   await fetchUserSession();
      //   await delay(5000);
      //   useWalletStore.getState().clearWallet();
      //   useWalletContext().initialize();
      // }
      setIsSwitchingStore(false);
      // const { userHasCompletedSetup, nextRoute } = decideNextRoute();
      // if (userHasCompletedSetup) {
      //   navigation.reset({
      //     index: 0,
      //     // routes: [{ name: nextRoute ?? redirectTo?.path ?? 'HomeTab' }],
      //     routes: [{ name: 'HomeTab'}],
      //   });
      // }
    }
  };

  //Save & Update State
  const saveUser = (user: any) => {
    setUser(user);
    AsyncStorage.setItem('user', JSON.stringify(user));
  };

  const updateStore = (data: Partial<StoreInterface>) => {
    const storeIndex = stores.findIndex(s => s.id === store?.id);
    const storeData = { ...store, ...data };
    const userStores = [...(user?.stores ?? [])];
    userStores[storeIndex] = storeData as any;
    updateUser({ stores: userStores });
  };

  const updateUser = async (newUserData: Partial<any>) => {
    if (user) {
      const updatedUser: any = { ...user, ...newUserData };
      saveUser(updatedUser);
    }
  };

  const refreshRates = () => {
    setInterval(
      () => {
        getRates();
      },
      1000 * 60 * 60,
    );
  };

  const getRates = async () => {
    const [res, err] = await getCurrentRates.makeRequest();

    if (res) {
      setCurrencyRates(res?.data?.rates as CurrencyRates);
    }
  };

  const getUserCountry = () => {
    return (
      store?.country ?? {
        name: 'Nigeria',
        currency: 'NGN',
        code: 'NG',
        dial_code: '+234',
        emoji: '🇳🇬',
      }
    );
  };

  const getRewards = () => {
    const sortedStores = stores.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
    const country = sortedStores[0]?.country;

    const countrySignupReward = rewards ? rewards.SIGNUP[country?.code ?? 'NG'] : 0;
    const countrySubscriptionReward = rewards ? rewards.SUBSCRIPTION[country?.code ?? 'NG'] : 0;
    const minCashoutAmount = rewards ? rewards.CASHOUT[country?.code ?? 'NG'] : 5000_00;

    return rewards
      ? {
          signup: countrySignupReward,
          subscription: countrySubscriptionReward,
          currency: country?.currency || 'NGN',
          min_cash_out: minCashoutAmount,
        }
      : null;
  };

  return {
    isInitialized,
    appIsSetup,
    userHasSetup,
    userAccountDeactivated,
    isSwitchingStore,
    isCreatingStore,
    setIsCreatingStore,
    isAuthenticated,

    storeId,
    stores,
    store: getStore(),
    currentRates,
    user,
    fetchError,

    initialize,
    logout,
    getNewToken,
    switchStore,
    getToken,

    updateUser,
    setStoreId,
    updateStore,
    setIsAuthenticated,
    decideNextRoute,
    getRates,
    getRewards,
    getUserCountry,

    categories: getStore()?.categories,
    pageNotReady: !isAuthenticated || !pageIsReady || !user || ((user?.stores?.length ?? 0) > 0 && !getStore()),
    storeLink: getStore() ? subdomainStoreLink(getStore()?.slug ?? '', true) : '',
    subscription: getStore()?.subscription ?? user?.subscription,
    userRole: getUserRole(getStore(), user),
    redirectTo,
    history,
    refetchSession: fetchUserSession,
    userLoading: getUserDataRequest.isLoading,
    // referrals,
  };
}

const defaultCountry = {
  name: 'Nigeria',
  currency: 'NGN',
  code: 'NG',
  dial_code: '+234',
  emoji: '🇳🇬',
};

export const getCountryFromCountries = (c: any) => {
  if (!c) return defaultCountry;
  return supportedCountries.find(cx => cx.code === c);
};

export const getUserRole = (store: any, user: any) => {
  if (store && user && store?.owner === user.id) {
    return StoreRoles.OWNER;
  }
  return store?.owners?.find((owner: any) => owner.user === user.id)?.role ?? StoreRoles.OPERATOR;
};

export default useAuthContext;
