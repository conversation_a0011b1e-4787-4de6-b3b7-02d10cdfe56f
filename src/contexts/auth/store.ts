import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { StoreInterface, CurrencyRates, Rewards, User, COUNTRIES } from 'catlog-shared';
import { delay } from 'src/assets/utils/js';
import { RootStackParamList } from 'src/@types/navigation';

export interface AuthState {
  userHasSetup: boolean;
  userAccountDeactivated: boolean; //account status of the user true when subscription === 'STARTER'
  appIsSetup: boolean;
  isNewUser: boolean;
  showBottomTab: boolean;
  isInitialized: boolean;
  isAuthenticated: boolean;
  isAuthFromCache: boolean;
  fetchError: boolean;
  pageIsReady: boolean;
  redirectTo: {
    path: keyof RootStackParamList;
    props: any;
  } | null;
  isSwitchingStore: boolean;
  isCreatingStore: boolean;
  token: string | null;
  user: User | null;
  storeId: string | null;
  history: string[];
  currentRates: CurrencyRates;
  rewards: Rewards;
  visitorCountry: { code: COUNTRIES; dial_code: string } | null;
  // rewards: any; // Define the type if known
  // referrals: any; // Define the type if known
}
export interface AuthActions {
  initialize: () => Promise<void>;
  verifyAndSetStoreId: () => Promise<void>;
  handleAuthSuccess: (user: any, token: string) => Promise<void>;
  clearUserData: () => void;
  getStore: () => StoreInterface | null;
  setIsAuthenticated: (isAuthenticated: boolean) => void;
  setFetchError: (fetchError: boolean) => void;
  setPageIsReady: (pageIsReady: boolean) => void;
  setRedirectTo: (redirectTo: { path: keyof RootStackParamList; props: any }) => void;
  setIsSwitchingStore: (isSwitchingStore: boolean) => void;
  setIsCreatingStore: (isCreatingStore: boolean) => void;
  setUser: (user: any) => void;
  setAppIsSetup: (status: boolean) => void;
  setUserHasSetup: (status: boolean) => void;
  setToken: (token: string) => void;
  setStoreId: (storeId: string) => void;
  setHistory: (history: string[]) => void;
  setShowBottomTab: (show: boolean) => void;
  reset: () => void;
  getToken: () => string | null;
  setCurrencyRates: (rates: CurrencyRates) => void;
  setRewards: (rates: Rewards) => void;
  setIsNewUser: (isNewUser: boolean) => void;
  setUserAccountDeactivated: (userAccountDeactivated: boolean) => void;
  setVisitorCountry: (visitorCountry: { code: COUNTRIES; dial_code: string }) => void;
  // setRewards: (rewards: any) => void;
  // setReferrals: (referrals: any) => void;
}
const initialState: AuthState = {
  showBottomTab: true,
  isInitialized: false,
  isNewUser: false,
  isAuthenticated: false,
  isAuthFromCache: false,
  fetchError: false,
  pageIsReady: false,
  redirectTo: null,
  isSwitchingStore: false,
  isCreatingStore: false,
  userAccountDeactivated: false, //account status of the user true when subscription === 'STARTER
  userHasSetup: false, //setup status of the user
  appIsSetup: false, //when necessary checks has been done to allow user access app
  // State -> user, store, stores
  token: null,
  user: null,
  storeId: null,
  history: [],
  currentRates: {},
  rewards: null,
  visitorCountry: null,
  // referrals: null,
};
const useAuthStore = create(
  persist<AuthState & AuthActions>(
    (set, get) => ({
      ...initialState,
      // Actions
      initialize: async () => {
        const token = get().token;
        const user = get().user;

        if (token && user) {
          await get().verifyAndSetStoreId();
          set({ isAuthenticated: true, isInitialized: true, isAuthFromCache: true });
        } else {
          set({ isInitialized: true });
        }
      },
      verifyAndSetStoreId: async () => {
        const user = get().user;
        const storeId = get().storeId;

        let store: StoreInterface | undefined;
        const stores = user?.stores as StoreInterface[];

        if (user && storeId && stores && stores?.length > 0) {
          store = stores.find(store => store.id === storeId);
        }

        if (!store && stores && stores?.length > 0) {
          set({ storeId: stores[0].id });
        }
      },
      handleAuthSuccess: async (user: any, token: string) => {
        set({ user, token });
        let storeId = null;

        if (user?.stores && user?.stores?.length > 0) {
          storeId = user.stores[0].id;
        }
        // await delay(200);

        console.log('HANDLING AUTH SUCCESS');
        set({ isAuthenticated: true, storeId });
      },
      clearUserData: () => {
        set({ isAuthenticated: false, user: null, token: null, storeId: null, redirectTo: null, appIsSetup: false });
        AsyncStorage.removeItem('auth-storage');
        AsyncStorage.removeItem('wallet-storage');
      },
      getStore: () => {
        const user = get().user;
        const storeId = get().storeId;
        const stores = user?.stores as StoreInterface[];
        if (user && storeId && stores) {
          return stores.find(store => store.id === storeId) || null;
        }
        return null;
      },
      getToken: () => get().token,
      setToken: (token: string) => set({ token }),
      setStoreId: (storeId: string) => set({ storeId }),
      setIsAuthenticated: (isAuthenticated: boolean) => set({ isAuthenticated }),
      setFetchError: (fetchError: any) => set({ fetchError }),
      setPageIsReady: (pageIsReady: boolean) => set({ pageIsReady }),
      setRedirectTo: (redirectTo: { path: keyof RootStackParamList; props: any }) => set({ redirectTo }),
      setIsSwitchingStore: (isSwitchingStore: boolean) => set({ isSwitchingStore }),
      setIsCreatingStore: (isCreatingStore: boolean) => set({ isCreatingStore }),
      setUser: (user: any) => set({ user }),
      setAppIsSetup: (appIsSetup: boolean) => set({ appIsSetup }),
      setUserHasSetup: (userHasSetup: boolean) => set({ userHasSetup }),
      setHistory: (history: any) => set({ history }),
      setCurrencyRates: (rates: CurrencyRates) => set({ currentRates: rates }),
      setShowBottomTab: (showBottomTab: boolean) => set({ showBottomTab }),
      setRewards: (rewards: any) => set({ rewards }),
      setIsNewUser: (isNewUser: boolean) => set({ isNewUser }),
      setUserAccountDeactivated: (userAccountDeactivated: boolean) => set({ userAccountDeactivated }),
      setVisitorCountry: (visitorCountry: any) => set({ visitorCountry }),
      // setReferrals: (referrals: any) => set({ referrals }),
      reset: () => {
        set(initialState);
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => {
        return {
          ...state,
          //Values to overwrite when persisting to storage
          isAuthenticated: false,
          isNewUser: false,
          isInitialized: false,
          fetchError: false,
          pageIsReady: false,
          redirectTo: null,
          isSwitchingStore: false,
          isCreatingStore: false,
          isAuthFromCache: false,
          appIsSetup: false,
          history: [],
          currentRates: {},
          rewards: null,
          referrals: null,
          showBottomTab: true,
        };
      },
      onRehydrateStorage: () => state => {
        console.log('<====== ON REHYDRATE STORAGE ====>');
        console.log({ state });
        state?.initialize();
      },
    },
  ),
);
export default useAuthStore;
