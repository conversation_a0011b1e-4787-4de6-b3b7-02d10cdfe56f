import { useNavigation } from '@react-navigation/native';
import useAuthStore from './store';
import { useApi } from '@/hooks/use-api';
import { COUNTRIES, countryCodes, LOGIN, LoginParams, SIGNUP, SIGNUP_V2, SignUpParams, SignUpParamsV2 } from 'catlog-shared';
import * as Sentry from '@sentry/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const useGuestContext = () => {
  const navigation = useNavigation();
  const navigationState = navigation.getState();

  // Zustand store actions and state
  const {
    isInitialized,
    token,
    isAuthenticated,
    isAuthFromCache,
    fetchError,
    pageIsReady,
    redirectTo,
    isSwitchingStore,
    isCreatingStore,
    user,
    storeId,
    history,
    visitorCountry,
    setIsAuthenticated,
    setFetchError,
    setPageIsReady,
    setRedirectTo,
    setIsSwitchingStore,
    setIsCreatingStore,
    setUser,
    setStoreId,
    setHistory,
    verifyAndSetStoreId,
    handleAuthSuccess,
    getStore,
    clearUserData,
    setVisitorCountry,
  } = useAuthStore();

  const loginRequest = useApi<LoginParams>({ key: 'login', apiFunction: LOGIN, method: 'POST' });
  const signupRequest = useApi<SignUpParams>({ key: 'signup', apiFunction: SIGNUP, method: 'POST' });

  const signupRequest2 = useApi({
    key: SIGNUP_V2.name,
    apiFunction: SIGNUP_V2,
    method: 'POST',
  });

  const login = async (data: LoginParams, redirect = true) => {
    const [response, error] = await loginRequest.makeRequest(data);
    if (error) return [response, error];

    Sentry.setUser({
      id: response.user.id, // Required unique identifier
      email: response.user.email,
    });
    return handleSuccess(response, redirect);
  };

  async function register(data: SignUpParams, redirect?: boolean) {
    const [response, error] = await signupRequest.makeRequest(data);
    if (error) return [response, error];

    return handleSuccess(response, redirect);
  }

  async function register2(data: SignUpParamsV2, redirect?: boolean) {
    const [response, error] = await signupRequest2.makeRequest(data);
    if (error) return [response, error];

    await AsyncStorage.setItem('is-new-user', 'true');
    return handleSuccess(response, redirect);
  }

  const handleSuccess = async (response: any, redirect = true) => {
    await handleAuthSuccess(response.user, response.token);
    // if (redirect) handleRedirects();
    return [response, null];
  };

  function getVisitorCountry() {
    const localVisitorCountry = localStorage.getItem("visitor-country");
    const fn = async () => {
      try {
        const payload = await (await fetch("https://api.iplocation.net/?cmd=get-ip")).json();
        if (payload) {
          const location = await (await fetch("https://api.iplocation.net/?ip=" + payload.ip)).json();
          if (location) {
            const code = countryCodes.find((c) => c.code === location.country_code2);
            if (code) {
              const visitorCountry = {
                code: code.code as COUNTRIES,
                dial_code: code.dial_code,
              };

              localStorage.setItem("visitor-country", JSON.stringify(visitorCountry));
              setVisitorCountry(visitorCountry);
            }
          }
        }
      } catch (e) {
        // console.log(e);
      }
    };

    if (!localVisitorCountry) {
      fn();
    } else {
      const localCountryCode = JSON.parse(localVisitorCountry);

      if (!localCountryCode.code || !localCountryCode.dial_code) {
        fn();
        return;
      }

      setVisitorCountry(localCountryCode);
    }
  }

  return {
    login,
    handleSuccess,
    register,
    register2,
    getVisitorCountry,
    visitorCountry,
    loginRequest,
    signupRequest,
    signupRequest2,
  };
};

export default useGuestContext;
