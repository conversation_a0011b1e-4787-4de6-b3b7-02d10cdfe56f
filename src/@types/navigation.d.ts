import { FillProductsInfoActionType } from '../screens/products/storefront/create-products/fill-products-info';
import { VERIFICATION_TYPE } from './utils';
import { ProductItemInterface, InvoiceInterface, IDelivery, StoreInviteDetails } from 'catlog-shared';
import { StackNavigationProp } from '@react-navigation/stack';
import { PaymentSettingsTabs, StoreConfigurationTabs, StoreInformationTabs } from 'src/components/store-settings/types';

export type RootStackParamList = {
  CatlogCredits: undefined;
  GetStarted: undefined;
  CreateStore: undefined;
  SetupAddProducts: undefined;
  SelectAddingMethod: undefined;
  SelectProductsImage: undefined;
  FillProductsInfo: {
    action: FillProductsInfoActionType;
  };
  SelectPlan: undefined;
  HomeTab: undefined;
  CatlogCredits: undefined;
  ProductDetails: { id: string };
  GetStarted: undefined;
  SelectCategorization: undefined;
  Verify: { type: VERIFICATION_TYPE | null };
  Storefront: undefined;
  ProductImages: {
    images: string[];
    thumbnail: number;
    activeIndex: number;
    itemId: string;
  };
  CreateDiscount: undefined;
  CreateCoupon: undefined;
  Feedback: {
    headerTitle: string;
    headerBg: string;
    feedbackText: string;
    btnText: string;
    onPressBtn;
  };
  LatestOrder: undefined;
  OrderInfo: { id: string };
  DeliveryDetails: { id?: string };
  TrackDelivery: { id?: string };
  InitiateDelivery: { deliveryData?: IDelivery };
  RequestDelivery: { deliveryData?: IDelivery };
  RecordOrder: undefined;
  Customer: undefined;
  CustomersAnalytics: undefined;
  SearchCustomers: undefined;
  StoreSettings: undefined;
  StoreInformation: { tab?: StoreInformationTabs };
  StoreConfigurations: { tab?: StoreConfigurationTabs };
  CheckoutOptions: undefined;
  DeliveryAreas: undefined;
  CreateInvoice: undefined;
  EditInvoice: { invoice: InvoiceInterface };
  DeliveriesAnalytics: undefined;
  Login: undefined;
  Signup: undefined;
  SetupBusiness: undefined;
  SetupProgress: undefined;
  SetupComplete: undefined;
  EnterEmail: undefined;
  ResetPassword: undefined;
  Search: undefined;
  OrderAnalytics: undefined;
  PaymentSettings: { tab?: PaymentSettingsTabs };
  StoreSettingsStack: any;
  Payments: undefined;
  CreateProducts: undefined;
  AccountDeactivated: undefined;
  DeactivatedPayments: undefined;
  //
  Payments: undefined;
  PaymentInfo: { id: string };
  KYC: undefined;
  PaymentAnalytics: undefined;
  PaymentLinksList: undefined;
  CreatePaymentLink: undefined;
  ManageInvite: { invite: string };
  InvoicesStack: { navigateToCreate?: boolean };
  NotificationList: undefined;
  SearchProducts: undefined;
  SearchOrders: undefined;
  SearchInvoices: undefined;
  SearchTransactions: undefined;
  SearchDiscounts: undefined;
  SearchCoupons: undefined;
  EditProduct: { product: ProductItemInterface; isDuplicate: boolean };
  Carts: undefined;
  PriceUpdate: undefined;
  QuantityUpdate: undefined;

  // AppNavigation: undefined;
  MainNavigation: undefined;
  HomeTab: undefined;
  Home: StoreInviteDetails;
  AppNavigation: any;
  AuthNavigation: undefined;
  Orders: undefined;
  Products: undefined;

  SetupComplete: undefined;
  PickPlan: undefined;
  ChangePlan: undefined;
  WatchTutorial: undefined;
  SetupBusinessStack: undefined;
  ForceUpdate: undefined;
  ManageSubscription: undefined;
  SortProducts: undefined;
};

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}

    // Add this type definition
    interface NavigationProp<ParamList = RootStackParamList, RouteName extends keyof ParamList = keyof ParamList>
      extends StackNavigationProp<ParamList, RouteName> {}
  }
}
