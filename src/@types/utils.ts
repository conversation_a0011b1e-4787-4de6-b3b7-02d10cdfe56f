import { Transaction } from 'catlog-shared';

export interface Image {
  src: string;
  name: string;
  file: string | null; // base64 string
  isUploading: boolean;
  uploadProgress: number;
  url?: string;
  error?: boolean;
  newFile?: boolean;
  key: string;
  meta?: any
  imageHash?: string;
  isPicker?: boolean;
  lastModified?: number;
}

export interface BaseTransaction {
  created_at: string;
}

export type GroupedTransactions<T extends BaseTransaction> = {
  date: Date;
  transactions: T[];
};

export type WithKey<T, K extends string> = T & { [key in K]: any };


export enum VERIFICATION_TYPE {
  'EMAIL',
  'PHONE'
}

export enum TimeRange {
  TODAY = 'Today',
  THIS_WEEK = 'This Week',
  LAST_WEEK = 'Last Week',
  LAST_30_DAYS = 'Last 30 Days',
  THIS_YEAR = 'This Year',
  ALL_TIME = 'All Time',
  CUSTOM = 'Custom',
}

export enum SETUP_TYPE {
  BOOK_ONBOARDING_CALL,
  JOIN_COMMUNITY,
  FOLLOW_OUR_SOCIALS,

  VERIFY_EMAIL_PHONE,
  UPLOAD_TEN_PRODUCTS,
  ADD_STORE_LINK_TO_SOCIAL,
  TAKE_ORDER_WITH_PAYMENT,

  ADD_LOGO_AND_COVER_IMAGE,
  ADD_LINK_TO_SOCIAL,
  UPLOAD_10_PRODUCT,
  VERIFY_IDENTITY,
  ADD_SECURITY_PIN,
  ADD_WITHDRAWAL_ACCOUNT,
  GET_VERIFIED,

  ADD_STORE_LOCATION,
  CUSTOMIZE_COLOR,
  ADD_DELIVERY_AREA,
  ADD_DELIVERY_TIMELINE,
}