{"name": "catlog", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "APP_ENV=development expo start --clear", "postinstall": "patch-package", "android": "APP_ENV=development expo run:android", "ios": "APP_ENV=development expo run:ios", "web": "APP_ENV=development expo start --web", "update-shared": "yarn add git+https://*********************************************************************************************:<EMAIL>/catlog-shop/app-commons.git", "create:component": "node create-component.js", "increment:build": "ts-node scripts/incrementBuildNumber.ts", "increment:build:version": "ts-node scripts/incrementBuildNumber.ts --increment-version", "build:staging:android": "eas build --profile staging -p android", "build:production:android": "eas build --profile production -p android", "build:staging:ios": "eas build --profile staging -p ios", "build:production:ios": "eas build --profile production -p ios", "eas-update:staging": "eas update --environment preview --channel staging", "eas-update:production": "eas update --environment production --channel production", "upload-source-map-sentry": "npx sentry-expo-upload-sourcemaps dist"}, "dependencies": {"@expo/config-plugins": "~9.0.0", "@gorhom/bottom-sheet": "^5", "@marceloterreiro/flash-calendar": "^1.3.0", "@microsoft/react-native-clarity": "^4.2.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-firebase/analytics": "^21.13.0", "@react-native-firebase/app": "^21.13.0", "@react-native-firebase/messaging": "^21.13.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@sentry/react-native": "~6.3.0", "@shopify/flash-list": "1.7.3", "@tanstack/react-query": "^5.51.1", "@tanstack/react-query-devtools": "^5.51.23", "@types/crypto-js": "^4.2.2", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "add": "^2.0.6", "catlog-shared": "git+https://*********************************************************************************************:<EMAIL>/catlog-shop/app-commons.git", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "eslint": "^9.18.0", "eslint-plugin-import": "^2.31.0", "expo": "^52.0.28", "expo-blur": "~14.0.1", "expo-build-properties": "~0.13.1", "expo-camera": "~16.0.10", "expo-clipboard": "~7.0.0", "expo-dev-client": "~5.0.10", "expo-device": "~7.0.2", "expo-file-system": "~18.0.12", "expo-haptics": "~14.0.0", "expo-image": "~2.0.3", "expo-image-manipulator": "~13.0.5", "expo-image-picker": "~16.0.3", "expo-linear-gradient": "~14.0.1", "expo-linking": "~7.0.3", "expo-localization": "~16.0.1", "expo-navigation-bar": "~4.0.6", "expo-network": "^7.0.5", "expo-notifications": "~0.29.11", "expo-quick-actions": "^4.0.2", "expo-sharing": "~13.0.0", "expo-splash-screen": "~0.29.18", "expo-status-bar": "~2.0.0", "expo-updates": "~0.26.13", "expo-video": "~2.0.6", "expo-video-thumbnails": "~9.0.3", "expo-web-browser": "^14.0.2", "ffmpeg-kit-react-native": "^6.0.2", "formik": "^2.4.6", "iconsax-react-native": "git+https://github.com/faarda/iconsax-react-native", "mime": "^4.0.4", "moment": "^2.30.1", "nativewind": "^2.0.11", "react": "18.3.1", "react-native": "0.76.5", "react-native-animatable": "^1.4.0", "react-native-calendar-picker": "^8.0.5", "react-native-calendar-range-picker": "^1.5.8", "react-native-circular-progress-indicator": "^4.4.2", "react-native-compressor": "^1.11.0", "react-native-confetti-cannon": "^1.5.2", "react-native-confirmation-code-field": "^7.4.0", "react-native-currency-input": "^1.1.1", "react-native-dotenv": "^3.4.11", "react-native-draggable-flatlist": "^4.0.3", "react-native-gesture-handler": "~2.20.2", "react-native-gifted-charts": "^1.4.7", "react-native-google-places-autocomplete": "2.5.6", "react-native-material-menu": "^2.0.0", "react-native-modal": "^13.0.1", "react-native-pager-view": "6.5.1", "react-native-react-query-devtools": "^1.3.0", "react-native-reanimated": "~3.16.1", "react-native-redash": "^18.1.3", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.1.0", "react-native-svg": "15.8.0", "react-native-svg-transformer": "^1.3.0", "react-native-switch": "^1.5.1", "react-native-tab-view": "^3.5.2", "react-native-toast-message": "^2.2.0", "react-native-webview": "13.12.5", "react-native-youtube-iframe": "^2.3.0", "react-number-format": "^5.4.4", "reanimated-color-picker": "^3.0.4", "rn-emoji-keyboard": "^1.7.0", "yup": "^1.4.0", "zustand": "^4.5.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^15.1.3", "@types/node": "^22.13.2", "@types/react": "~18.3.12", "@types/react-native": "^0.73.0", "@types/react-native-calendar-picker": "^8.0.0", "babel-plugin-inline-import": "^3.0.0", "babel-plugin-module-resolver": "^5.0.0", "chokidar": "^3.5.3", "eslint-config-expo": "^8.0.1", "eslint-config-prettier": "^10.0.1", "eslint-config-universe": "^12.0.0", "eslint-plugin-prettier": "^5.2.2", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "^3.2.5", "tailwindcss": "3.3.2", "ts-node": "^10.9.2", "typescript": "5.3.2"}, "private": true, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}