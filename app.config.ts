import { ExpoConfig } from 'expo/config';

const IS_DEVELOPMENT = process.env.APP_ENV === 'development';
const IS_STAGING = process.env.APP_ENV === 'staging';
const IS_PRODUCTION = process.env.APP_ENV === 'production';

const envConfig = () => {
  if (IS_DEVELOPMENT) {
    return {
      appName: 'Catlog (Dev)',
      scheme: 'shop.catlog.stx.app',
      uniqueIdentifier: 'shop.catlog.app.dev',
      icon: './assets/staging-icon.png',
      backgroundColor: '#ffffff',
      splash: './assets/staging-splash.png',
      adaptiveIcon: './assets/staging-adaptive-icon.png',
      adaptiveIconBackgroundColor: '#332089',
      iosFirebasePlist: './src/configs-files/staging/GoogleService-Info.plist',
    };
  }

  if (IS_STAGING) {
    return {
      appName: 'Catlog (Staging)',
      scheme: 'catlog',
      uniqueIdentifier: 'shop.catlog.stx.app',
      icon: './assets/staging-icon.png',
      backgroundColor: '#ffffff',
      splash: './assets/staging-splash.png',
      adaptiveIcon: './assets/staging-adaptive-icon.png',
      adaptiveIconBackgroundColor: '#332089',
      iosFirebasePlist: './src/configs-files/staging/GoogleService-Info.plist',
    };
  }

  return {
    appName: 'Catlog',
    scheme: 'catlog',
    uniqueIdentifier: 'shop.catlog.app',
    icon: './assets/icon.png',
    backgroundColor: '#332089',
    splash: './assets/splash.png',
    adaptiveIcon: './assets/adaptive-icon.png',
    adaptiveIconBackgroundColor: '#ffffff',
    iosFirebasePlist: './GoogleService-Info.plist',
  };
};

export default {
  name: envConfig().appName,
  slug: 'Catlog',
  version: '1.0.0',
  orientation: 'portrait',
  icon: envConfig().icon,
  userInterfaceStyle: 'light',
  scheme: envConfig().scheme,
  newArchEnabled: true,
  splash: {
    image: envConfig().splash,
    resizeMode: 'cover',
    backgroundColor: '#332089',
  },
  plugins: [
    'expo-video',
    '@react-native-firebase/app',
    '@react-native-firebase/messaging',
    "expo-localization",
    "expo-quick-actions",
    [
      '@sentry/react-native/expo',
      {
        organization: 'catlog-test',
        project: 'catlog',
        url: 'https://sentry.io/',
        warning: 'DO NOT COMMIT YOUR AUTH TOKEN',
        authToken:
          '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',
      },
    ],
    [
      'expo-image-picker',
      {
        photosPermission: 'The app accesses your photos to allow you upload products.',
      },
    ],
    [
      'expo-camera',
      {
        cameraPermission: 'Allow $(PRODUCT_NAME) to access your camera',
        microphonePermission: 'Allow $(PRODUCT_NAME) to access your microphone',
        recordAudioAndroid: true,
      },
    ],
    [
      'expo-build-properties',
      {
        ios: {
          useFrameworks: 'static',
        },
      },
    ],
    [
      'expo-notifications',
      {
        icon: './assets/notification-icon.png',
        enableBackgroundRemoteNotifications: false,
      },
    ],
    'expo-splash-screen',
  ],
  assetBundlePatterns: ['**/*'],
  ios: {
    supportsTablet: true,
    bundleIdentifier: envConfig().uniqueIdentifier,
    googleServicesFile: envConfig().iosFirebasePlist,
    associatedDomains: ['applinks:app.catlog.shop'],
    config: {
      usesNonExemptEncryption: false,
    },
    entitlements: {
      'aps-environment': 'production',
    },
    infoPlist: {
      NSCameraUsageDescription: 'This app needs access to your camera to take photos.',
      NSMicrophoneUsageDescription: 'This app needs access to your microphone for videos.',
      NSPhotoLibraryUsageDescription: 'This app needs access to your photos to save images.',
    },
  },
  android: {
    package: envConfig().uniqueIdentifier,
    googleServicesFile: './google-services.json',
    adaptiveIcon: {
      foregroundImage: envConfig().adaptiveIcon,
      backgroundColor: envConfig().adaptiveIconBackgroundColor,
    },
    permissions: ['android.permission.RECORD_AUDIO', 'android.permission.CAMERA', 'NOTIFICATIONS'],
    intentFilters: [
      {
        action: 'VIEW',
        autoVerify: true,
        data: [
          {
            scheme: 'https',
            host: 'app.catlog.shop',
            pathPrefix: '/join-store',
          },
        ],
        category: ['BROWSABLE', 'DEFAULT'],
      },
    ],
  },
  web: {
    favicon: './assets/favicon.png',
  },
  extra: {
    eas: {
      projectId: 'aa201a17-645a-448a-8178-0b31e507b352',
    },
  },
  owner: 'catlog',
  runtimeVersion: {
    policy: 'appVersion',
  },
  updates: {
    url: 'https://u.expo.dev/aa201a17-645a-448a-8178-0b31e507b352',
  },
} as ExpoConfig;
