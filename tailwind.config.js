/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./app.{js,jsx,ts,tsx}', './src/**/*.{js,jsx,ts,tsx}'],
  theme: {
    fontFamily: {
      interBold: ['Inter-Bold'],
      interBlack: ['Inter-Black'],
      interSemiBold: ['Inter-SemiBold'],
      interMedium: ['Inter-Medium'],
      interRegular: ['Inter-Regular'],
      fhOscarBold: ['FHOscar-Bold'],
      fhOscarBlack: ['FHOscar-Black'],
      fhOscarMedium: ['FHOscar-Medium'],
      fhOscarLight: ['FHOscar-Light'],
    },
    colors: {
      white: '#FFFFFF',
      transparentWhite: '#FFFFFF',
      transparent: '#ffffff00',
      primary: {
        extraLight: '#C6C0FE',
        light: '#7E76DD',
        main: '#332089',
        pastel: '#F7F5FF',
      },
      accentDarkRed: {
        main: '#55001F',
        pastel: '#EEE5E9',
      },
      accentRed: {
        extraLight: '#FEB8DD',
        light: '#EA5E8B',
        main: '#BF0637',
        pastel: '#FFEBF5',
        transparent: '#F4E2E6',
      },
      accentYellow: {
        extraLight: '#FFE1BB',
        light: '#FFC476',
        main: '#EF940F',
        pastel: '#FEF6EB',
        pastel2: '#FCEACF',
      },
      accentGreen: {
        extraLight: '#CAFFE9',
        light: '#73E8BB',
        main: '#39B588',
        pastel: '#EBFFF7',
        pastel2: '#EFFAF6',
        pastel3: '#F5FFFB',
        transparent: '#E7F3EF',
      },
      accentOrange: {
        extraLight: '#FFC8B3',
        light: '#FF8159',
        main: '#F35508',
        pastel: '#FFF0EB',
      },
      black: {
        main: '#292D32',
        secondary: '#3E3E3E',
        muted: '#656565',
        placeholder: '#747478',
      },
      grey: {
        bgOne: '#F8F8F8',
        bgTwo: '#FAFAFA',
        border: '#E5E5E580',
        muted: '#AAAAAA',
        mutedLight: '#C2C2C2',
        mutedDark: '#656565',
        extraLight: '#F0F0F0',
      },
      notifications: {
        error: '#FFF0F8',
        success: '#E2FFF3',
      },
    },
    extend: {
      spacing: {
        1: '1px',
        2: '2px',
        3: '3px',
        4: '4px',
        5: '5px',
        6: '6px',
        7: '7px',
        8: '8px',
        9: '9px',
        10: '10px',
        12: '12px',
        14: '14px',
        15: '15px',
        16: '16px',
        18: '18px',
        20: '20px',
        24: '24px',
        25: '25px',
        26: '26px',
        27: '27px',
        28: '28px',
        30: '30px',
        32: '32px',
        35: '35px',
        36: '36px',
        40: '40px',
        45: '45px',
        48: '48px',
        50: '50px',
        70: '70px',
        75: '75px',
        80: '80px',
        100: '100px',
      },
      borderRadius: {
        4: '4px',
        5: '5px',
        8: '8px',
        10: '10px',
        12: '12px',
        15: '15px',
        30: '30px',
        40: '40px',
      },
      fontSize: {
        '1xs': '13px',
        '1sm': '15px',
      },
      lineHeight: {
        'extra-tight': '1.15',
      },
    },
  },
  plugins: [],
};
