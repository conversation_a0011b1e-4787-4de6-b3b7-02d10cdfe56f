import 'react-native-gesture-handler';
import { useEffect, useRef } from 'react';
import { useFonts } from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';
import { BaseApp, navigationRef } from '@/navigation';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Platform, UIManager } from 'react-native';
import * as NavigationBar from 'expo-navigation-bar';
import * as Sentry from '@sentry/react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { NavigationContainer } from '@react-navigation/native';
import { linking } from 'src/navigation/navigation-utils';
import { FallBack } from 'src/navigation/navigator';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import useNotificationSetup from 'src/hooks/use-notification-setup';
import { getMessaging } from '@react-native-firebase/messaging';
import useNotificationHandler from 'src/hooks/use-notification-handler';
import * as Notifications from 'expo-notifications';
import ErrorBoundary from 'src/components/ui/others/error-boundary';
import { NetworkProvider } from 'src/contexts/network-context';
import DevToolsProvider from 'src/components/dev-tools/dev-tools-provider';
import analytics from '@react-native-firebase/analytics';
import * as Clarity from '@microsoft/react-native-clarity';
import * as QuickActions from 'expo-quick-actions';
import { useQuickActionCallback } from "expo-quick-actions/hooks";
import QuickActionProvider from 'src/components/app-setups/quick-action-provider';

// Prevent splash screen from auto-hiding
SplashScreen.preventAutoHideAsync().catch(() => {});

export default function App() {
  const queryClient = new QueryClient();
  Clarity.initialize('r52f2rsexz', {
    logLevel: Clarity.LogLevel.Verbose,
  });
  const routeNameRef = useRef<string>();

  const {
    handleForegroundNotification,
    handleNotificationOpenApp,
    handleNotificationOpenAppFromQuitState,
    handleBackgroundNotification,
  } = useNotificationHandler();

  // Configure notification handler
  useEffect(() => {
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: false,
        priority: Notifications.AndroidNotificationPriority.HIGH,
      }),
    });
  }, []);

  useEffect(() => {
    // Handle foreground notifications
    // const unsubscribeForeground = getMessaging().onMessage(handleForegroundNotification);
    const unsubscribeForeground = getMessaging().onMessage(async remoteMessage => {
      await handleForegroundNotification(remoteMessage);
    });

    // getMessaging().setOpenSettingsForNotificationsHandler(handleBackgroundNotification);

    getMessaging().setBackgroundMessageHandler(async remoteMessage => {
      // await handleBackgroundNotification(remoteMessage);
    });

    getMessaging().onNotificationOpenedApp(async remoteMessage => {
      await handleNotificationOpenApp(remoteMessage);
    });

    // Handle initial notification when app is opened from a closed state
    getMessaging()
      .getInitialNotification()
      .then(async remoteMessage => {
        await handleNotificationOpenAppFromQuitState(remoteMessage);
      });

    // Cleanup
    return () => {
      unsubscribeForeground();
    };
  }, [handleForegroundNotification, handleBackgroundNotification]);

  SplashScreen.setOptions({
    duration: 300,
    fade: true,
  });

  const [fontsLoaded] = useFonts({
    'FHOscar-Light': require('@/assets/fonts/fhoscar/FHOscar-Light.otf'),
    'FHOscar-Bold': require('@/assets/fonts/fhoscar/FHOscar-Bold.otf'),
    'FHOscar-Black': require('@/assets/fonts/fhoscar/FHOscar-Black.otf'),
    'Inter-Black': require('@/assets/fonts/inter/Inter-Black.ttf'),
    'Inter-Bold': require('@/assets/fonts/inter/Inter-Bold.ttf'),
    'Inter-Light': require('@/assets/fonts/inter/Inter-Light.ttf'),
    'Inter-Medium': require('@/assets/fonts/inter/Inter-Medium.ttf'),
    'Inter-Regular': require('@/assets/fonts/inter/Inter-Regular.ttf'),
    'Inter-SemiBold': require('@/assets/fonts/inter/Inter-SemiBold.ttf'),
  });

  Sentry.init({
    dsn: 'https://<EMAIL>/4508750878605312',
    debug: __DEV__ ? true : false, // If `true`, Sentry will try to print out useful debugging information if something goes wrong with sending the event. Set it to `false` in production
    // _experiments: {
    //   replaysOnErrorSampleRate: 1.0,
    //   replaysSessionSampleRate: 1.0,
    // },
    integrations: [
      // Sentry.mobileReplayIntegration({
      //   maskAllText: false,
      //   maskAllImages: false,
      //   maskAllVectors: false,
      // }),
      Sentry.screenshotIntegration(),
    ],
  });

  useEffect(() => {
    if (Platform.OS === 'android') {
      NavigationBar.setBackgroundColorAsync('white').catch(() => {});
      NavigationBar.setButtonStyleAsync('dark').catch(() => {});

      if (UIManager.setLayoutAnimationEnabledExperimental) {
        UIManager.setLayoutAnimationEnabledExperimental(true);
      }
    }
  }, []);

  if (!fontsLoaded) {
    return null;
  }

  return (
    <Sentry.ErrorBoundary onError={() => {}} fallback={() => <ErrorBoundary />}>
      <SafeAreaProvider>
        <NetworkProvider>
          <QueryClientProvider client={queryClient}>
            <GestureHandlerRootView
              style={{
                flex: 1,
              }}>
              <DevToolsProvider>
                <NavigationContainer
                  linking={linking}
                  fallback={<FallBack />}
                  ref={navigationRef}
                  onReady={() => {
                    routeNameRef.current = navigationRef?.current?.getCurrentRoute()?.name!;
                    Clarity.setCurrentScreenName(routeNameRef.current);
                  }}
                  onStateChange={async () => {
                    const previousRouteName = routeNameRef?.current;
                    const currentRouteName = navigationRef?.current?.getCurrentRoute()?.name!;

                    if (previousRouteName !== currentRouteName) {
                      await analytics().logScreenView({
                        screen_name: currentRouteName,
                        screen_class: currentRouteName,
                      });
                      Clarity.setCurrentScreenName(currentRouteName);
                    }
                    routeNameRef.current = currentRouteName;
                  }}>
                  <BottomSheetModalProvider>
                    <BaseApp fontsLoaded={fontsLoaded} />
                  </BottomSheetModalProvider>
                  <QuickActionProvider />
                </NavigationContainer>
              </DevToolsProvider>
            </GestureHandlerRootView>
          </QueryClientProvider>
        </NetworkProvider>
      </SafeAreaProvider>
    </Sentry.ErrorBoundary>
  );
}